# PowerShell script to launch Todo Management System with Windows Terminal tabs
# Uses Jomez Pro color scheme from the web interface
#
# Usage:
#   .\LaunchTodoSystem.ps1                 # Launch Todo system only
#   .\LaunchTodoSystem.ps1 --launch-web    # Launch Todo system and open web browser
#   .\LaunchTodoSystem.ps1 --help          # Show help information

param(
    [switch]$LaunchWeb,     # Open web browser to http://localhost:8080
    [switch]$Help           # Show help information
)

# Configuration
$PID_FILE = "$PSScriptRoot\todo-system-pids.json"
$API_TAB_NAME = "Todo List Manager API Server"
$WEB_TAB_NAME = "Todo List Manager Web Server"
$WEB_URL = "http://localhost:8080"
$API_DOCS_URL = "http://localhost:8000/docs"

# Show help if requested
if ($Help) {
    Write-Host ""
    Write-Host "🚀 Todo Management System Launcher" -ForegroundColor Green
    Write-Host "=" * 50 -ForegroundColor DarkGray
    Write-Host ""
    Write-Host "DESCRIPTION:" -ForegroundColor Yellow
    Write-Host "  Launches the Todo Management System with Windows Terminal tabs"
    Write-Host "  for both the API server and web interface. Automatically cleans"
    Write-Host "  up any existing processes before starting fresh instances."
    Write-Host ""
    Write-Host "USAGE:" -ForegroundColor Yellow
    Write-Host "  .\LaunchTodoSystem.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "OPTIONS:" -ForegroundColor Yellow
    Write-Host "  --launch-web    Automatically open web browser to Todo interface"
    Write-Host "  --help          Show this help information"
    Write-Host ""
    Write-Host "EXAMPLES:" -ForegroundColor Yellow
    Write-Host "  .\LaunchTodoSystem.ps1"
    Write-Host "    Launch Todo system only"
    Write-Host ""
    Write-Host "  .\LaunchTodoSystem.ps1 --launch-web"
    Write-Host "    Launch Todo system and open web browser"
    Write-Host ""
    Write-Host "ENDPOINTS:" -ForegroundColor Yellow
    Write-Host "  Web Interface:    $WEB_URL"
    Write-Host "  API Documentation: $API_DOCS_URL"
    Write-Host ""
    Write-Host "NOTE:" -ForegroundColor Cyan
    Write-Host "  Running this script again will automatically clean up existing"
    Write-Host "  Todo Management System processes before launching new ones."
    Write-Host ""
    exit 0
}

# Kill any existing Todo Management System processes and terminals
Write-Host "🔍 Checking for existing Todo Management System processes..." -ForegroundColor Yellow

# Function to read stored PIDs from previous runs
function Get-StoredPids {
    if (Test-Path $PID_FILE) {
        try {
            $pidData = Get-Content $PID_FILE -Raw | ConvertFrom-Json
            return $pidData
        } catch {
            Write-Host "⚠️ Could not read PID file, creating new one" -ForegroundColor Yellow
            return @{ api_pid = $null; web_pid = $null; powershell_pids = @() }
        }
    }
    return @{ api_pid = $null; web_pid = $null; powershell_pids = @() }
}

# Function to save PIDs for future cleanup
function Save-Pids {
    param($PidData)
    try {
        $PidData | ConvertTo-Json | Set-Content $PID_FILE
        Write-Host "💾 Saved process IDs for future cleanup" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Could not save PID file" -ForegroundColor Yellow
    }
}

# Function to kill processes by PID with error handling
function Kill-ProcessSafely {
    param([int]$ProcessId, [string]$Description = "Process")
    
    if ($ProcessId -and $ProcessId -gt 0) {
        try {
            $process = Get-Process -Id $ProcessId -ErrorAction SilentlyContinue
            if ($process) {
                Write-Host "🔪 Killing $Description (PID: $ProcessId)" -ForegroundColor Red
                Stop-Process -Id $ProcessId -Force -ErrorAction SilentlyContinue
                Start-Sleep -Milliseconds 500  # Give it time to terminate
                return $true
            }
        } catch {
            Write-Host "⚠️ Could not kill $Description (PID: $ProcessId)" -ForegroundColor Yellow
        }
    }
    return $false
}

# Function to find and kill PowerShell processes by window title
function Kill-PowerShellByTitle {
    param([string]$WindowTitle)
    
    try {
        # Use Get-WmiObject to find PowerShell processes with specific window titles
        $processes = Get-WmiObject -Class Win32_Process | Where-Object { 
            $_.Name -like "*powershell*" -and
            $_.CommandLine -like "*$WindowTitle*"
        }
        
        if ($processes) {
            foreach ($proc in $processes) {
                Write-Host "🎯 Found PowerShell process for '$WindowTitle' (PID: $($proc.ProcessId))" -ForegroundColor Cyan
                Kill-ProcessSafely -ProcessId $proc.ProcessId -Description "PowerShell for $WindowTitle"
            }
            return $true
        }
    } catch {
        Write-Host "⚠️ Error searching for PowerShell processes: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    return $false
}

# Function to kill processes using Windows Terminal tab titles (advanced method)
function Kill-ByWindowsTerminalTabs {
    try {
        # This uses Windows Terminal CLI to list and potentially close tabs
        # Note: This is experimental and may not work in all Windows Terminal versions
        $wtOutput = & wt --help 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "🪟 Attempting to close Windows Terminal tabs by name..." -ForegroundColor Cyan
            
            # Try to use Windows Terminal's tab management (if available)
            # Note: This is a placeholder - actual implementation would need WT's tab API
            foreach ($tabName in @($API_TAB_NAME, $WEB_TAB_NAME)) {
                Write-Host "  🔍 Searching for tab: $tabName" -ForegroundColor Gray
                # Windows Terminal doesn't have a built-in way to close tabs by name via CLI
                # So we'll rely on the PowerShell process killing instead
            }
        }
    } catch {
        Write-Host "⚠️ Windows Terminal tab management not available" -ForegroundColor Yellow
    }
}

# Function to launch web browser to the Todo interface
function Start-WebBrowser {
    param([string]$Url, [int]$MaxRetries = 10, [int]$DelaySeconds = 3)
    
    Write-Host "🌐 Preparing to launch web browser..." -ForegroundColor Cyan
    Write-Host "  Target URL: $Url" -ForegroundColor Gray
    Write-Host "  Waiting for web server to be ready..." -ForegroundColor Yellow
    
    # Wait for the web server to be responsive
    $retryCount = 0
    $serverReady = $false
    
    while ($retryCount -lt $MaxRetries -and -not $serverReady) {
        try {
            $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                $serverReady = $true
                Write-Host "  ✅ Web server is ready!" -ForegroundColor Green
            }
        } catch {
            $retryCount++
            Write-Host "  ⏳ Attempt $retryCount/$MaxRetries - Server not ready yet..." -ForegroundColor Yellow
            if ($retryCount -lt $MaxRetries) {
                Start-Sleep -Seconds $DelaySeconds
            }
        }
    }
    
    if ($serverReady) {
        try {
            Write-Host "🚀 Launching default web browser..." -ForegroundColor Green
            Start-Process $Url
            Write-Host "  ✅ Web browser launched successfully!" -ForegroundColor Green
            Write-Host "  🌐 Todo Management System should now be visible in your browser" -ForegroundColor Cyan
        } catch {
            Write-Host "  ⚠️ Could not launch web browser automatically: $($_.Exception.Message)" -ForegroundColor Yellow
            Write-Host "  💡 Please manually navigate to: $Url" -ForegroundColor Cyan
        }
    } else {
        Write-Host "  ❌ Web server did not become ready within $($MaxRetries * $DelaySeconds) seconds" -ForegroundColor Red
        Write-Host "  💡 You can try accessing it manually once the servers finish starting:" -ForegroundColor Cyan
        Write-Host "     $Url" -ForegroundColor White
    }
}

# === CLEANUP PHASE ===
Write-Host ""
Write-Host "🧹 CLEANUP PHASE - Terminating existing Todo Management System processes" -ForegroundColor Yellow
Write-Host "=" * 80 -ForegroundColor DarkGray

# Load previously stored PIDs
$storedPids = Get-StoredPids
$killedAny = $false

# Method 1: Kill by stored PIDs (most reliable)
Write-Host "📋 Method 1: Killing processes by stored PIDs..." -ForegroundColor Cyan
if ($storedPids.api_pid) {
    if (Kill-ProcessSafely -ProcessId $storedPids.api_pid -Description "API Server") {
        $killedAny = $true
    }
}
if ($storedPids.web_pid) {
    if (Kill-ProcessSafely -ProcessId $storedPids.web_pid -Description "Web Server") {
        $killedAny = $true
    }
}
if ($storedPids.powershell_pids -and $storedPids.powershell_pids.Count -gt 0) {
    foreach ($processId in $storedPids.powershell_pids) {
        if (Kill-ProcessSafely -ProcessId $processId -Description "PowerShell Terminal") {
            $killedAny = $true
        }
    }
}

# Method 2: Kill by tab names (search for specific PowerShell instances)
Write-Host "🎯 Method 2: Searching for PowerShell processes by tab names..." -ForegroundColor Cyan
if (Kill-PowerShellByTitle -WindowTitle $API_TAB_NAME) {
    $killedAny = $true
}
if (Kill-PowerShellByTitle -WindowTitle $WEB_TAB_NAME) {
    $killedAny = $true
}

# Method 3: Kill by port usage (fallback method)
Write-Host "🔌 Method 3: Killing processes by port usage..." -ForegroundColor Cyan
$portProcesses = @()

# Port 8000 (API server)
try {
    $port8000Processes = Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue
    if ($port8000Processes) {
        $port8000Processes | ForEach-Object { 
            if (Kill-ProcessSafely -ProcessId $_.OwningProcess -Description "Port 8000 Process") {
                $killedAny = $true
            }
            $portProcesses += $_.OwningProcess
        }
    }
} catch {
    Write-Host "  ℹ️ Port 8000 not in use" -ForegroundColor Gray
}

# Port 8080 (Web server)
try {
    $port8080Processes = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue
    if ($port8080Processes) {
        $port8080Processes | ForEach-Object { 
            if (Kill-ProcessSafely -ProcessId $_.OwningProcess -Description "Port 8080 Process") {
                $killedAny = $true
            }
            $portProcesses += $_.OwningProcess
        }
    }
} catch {
    Write-Host "  ℹ️ Port 8080 not in use" -ForegroundColor Gray
}

# Method 4: Kill by script names (final cleanup)
Write-Host "📜 Method 4: Final cleanup by script names..." -ForegroundColor Cyan
$serverScripts = @("start-api-server.ps1", "start-web-server.ps1", "uvicorn")
foreach ($scriptName in $serverScripts) {
    $processes = Get-WmiObject -Class Win32_Process | Where-Object { 
        $_.CommandLine -like "*$scriptName*" -and 
        $_.ProcessId -notin $portProcesses 
    }
    
    if ($processes) {
        foreach ($proc in $processes) {
            if (Kill-ProcessSafely -ProcessId $proc.ProcessId -Description "$scriptName process") {
                $killedAny = $true
            }
        }
    }
}

# Clean up the PID file since we're starting fresh
if (Test-Path $PID_FILE) {
    Remove-Item $PID_FILE -Force -ErrorAction SilentlyContinue
    Write-Host "🗑️ Cleaned up old PID tracking file" -ForegroundColor Gray
}

# Give processes time to fully terminate
if ($killedAny) {
    Write-Host "⏳ Waiting for processes to terminate..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
    Write-Host "✅ Cleanup completed successfully" -ForegroundColor Green
} else {
    Write-Host "ℹ️ No existing Todo Management System processes found" -ForegroundColor Gray
}

Write-Host ""
Write-Host "🚀 LAUNCH PHASE - Starting fresh Todo Management System" -ForegroundColor Green
Write-Host "=" * 80 -ForegroundColor DarkGray
Write-Host ""

# Define Jomez Pro color palette from the web interface
$JOMEZPRO_TEAL        = "#00c4aa"   # Primary teal from CSS --jomezpro-teal
$JOMEZPRO_BLUE        = "#0099cc"   # Secondary blue from CSS --jomezpro-blue  
$JOMEZPRO_BRIGHT_TEAL = "#1dd1a1"   # Bright teal from CSS --jomezpro-bright-teal
$JOMEZPRO_DARK        = "#2c3e50"   # Dark color from CSS --jomezpro-dark
$JOMEZPRO_SUCCESS     = "#27ae60"   # Success green from CSS --jomezpro-success
$JOMEZPRO_INFO        = "#3498db"   # Info blue from CSS --jomezpro-info

# Assign tab colors using Jomez Pro palette
$API_SERVER_COLOR     = $JOMEZPRO_TEAL        # Main API server - primary teal
$WEB_SERVER_COLOR     = $JOMEZPRO_BRIGHT_TEAL # Web server - bright teal  
$DEVELOPMENT_COLOR    = $JOMEZPRO_BLUE        # Development work - blue
$TESTING_COLOR        = $JOMEZPRO_INFO        # Testing and debug - info blue
$DOCUMENTATION_COLOR  = $JOMEZPRO_SUCCESS     # Documentation - success green
$ADMIN_COLOR         = $JOMEZPRO_DARK         # Admin tasks - dark

# Define base repository directory
$todoSystemDir = "C:\Users\<USER>\source\repos\todo-lists"
$apiDir = "$todoSystemDir\todo-api"
$webDir = "$todoSystemDir\todo-web"

# Define paths to the startup scripts
$apiStartScript = "$todoSystemDir\start-api-server.ps1"
$webStartScript = "$todoSystemDir\start-web-server.ps1"

# Build Windows Terminal command string with specific tab names
$wtCommand = "new-tab --title `"$API_TAB_NAME`" --tabColor `"$API_SERVER_COLOR`" -d `"$apiDir`" powershell -NoExit -ExecutionPolicy Bypass -File `"$apiStartScript`" ; split-pane --title `"$WEB_TAB_NAME`" --tabColor `"$WEB_SERVER_COLOR`" -d `"$webDir`" powershell -NoExit -ExecutionPolicy Bypass -File `"$webStartScript`""

Write-Host "🎨 Tab Configuration:" -ForegroundColor White
Write-Host "  • API Server: $API_TAB_NAME" -ForegroundColor Cyan
Write-Host "    Color: Jomez Pro Teal ($API_SERVER_COLOR)" -ForegroundColor Cyan
Write-Host "  • Web Server: $WEB_TAB_NAME" -ForegroundColor Green  
Write-Host "    Color: Jomez Pro Bright Teal ($WEB_SERVER_COLOR)" -ForegroundColor Green
Write-Host ""

Write-Host "🚀 Launching Windows Terminal with Todo Management System..." -ForegroundColor Cyan

# Launch Windows Terminal with the constructed command
$terminalProcess = Start-Process wt -ArgumentList $wtCommand -PassThru

Write-Host "✅ Windows Terminal launched successfully!" -ForegroundColor Green
Write-Host "📊 Terminal Process ID: $($terminalProcess.Id)" -ForegroundColor Gray

# Give the terminal a moment to start the child processes
Write-Host "⏳ Waiting for server processes to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Try to capture the PIDs of the running servers
Write-Host "🔍 Capturing server process IDs for future cleanup..." -ForegroundColor Cyan

# Initialize PID tracking
$newPidData = @{
    api_pid = $null
    web_pid = $null
    powershell_pids = @()
    terminal_pid = $terminalProcess.Id
    created_time = (Get-Date).ToString()
}

# Find API server process (usually uvicorn on port 8000)
Start-Sleep -Seconds 3  # Give servers time to bind to ports
try {
    $apiPortProcess = Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($apiPortProcess) {
        $newPidData.api_pid = $apiPortProcess.OwningProcess
        Write-Host "  📋 API Server PID: $($newPidData.api_pid)" -ForegroundColor Green
    }
} catch {
    Write-Host "  ⚠️ Could not capture API server PID yet" -ForegroundColor Yellow
}

# Find Web server process (usually Python on port 8080)  
try {
    $webPortProcess = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($webPortProcess) {
        $newPidData.web_pid = $webPortProcess.OwningProcess
        Write-Host "  🌐 Web Server PID: $($newPidData.web_pid)" -ForegroundColor Green
    }
} catch {
    Write-Host "  ⚠️ Could not capture web server PID yet" -ForegroundColor Yellow
}

# Find PowerShell processes (the terminal tabs)
try {
    $powershellProcesses = Get-WmiObject -Class Win32_Process | Where-Object { 
        $_.Name -like "*powershell*" -and (
            $_.CommandLine -like "*$($apiStartScript.Split('\\')[-1])*" -or
            $_.CommandLine -like "*$($webStartScript.Split('\\')[-1])*"
        )
    }
    
    if ($powershellProcesses) {
        $newPidData.powershell_pids = @($powershellProcesses | ForEach-Object { $_.ProcessId })
        Write-Host "  💻 PowerShell Tab PIDs: $($newPidData.powershell_pids -join ', ')" -ForegroundColor Green
    }
} catch {
    Write-Host "  ⚠️ Could not capture PowerShell tab PIDs" -ForegroundColor Yellow
}

# Save PID data for future cleanup
Save-Pids -PidData $newPidData

Write-Host ""
Write-Host "🎯 Todo Management System Status:" -ForegroundColor White
Write-Host "  • Terminal tabs are starting up with server processes" -ForegroundColor Cyan
Write-Host "  • Process IDs have been saved for future cleanup" -ForegroundColor Green
Write-Host "  • Each tab will show its startup status" -ForegroundColor Yellow

# Launch web browser if requested
if ($LaunchWeb) {
    Write-Host ""
    Write-Host "🌐 WEB BROWSER LAUNCH - Opening Todo Management Interface" -ForegroundColor Green
    Write-Host "=" * 80 -ForegroundColor DarkGray
    Start-WebBrowser -Url $WEB_URL
} else {
    Write-Host ""
    Write-Host "🌐 Once both servers are running:" -ForegroundColor White
    Write-Host "  • API Documentation: $API_DOCS_URL" -ForegroundColor Cyan
    Write-Host "  • Todo Web Interface: $WEB_URL" -ForegroundColor Green
    Write-Host ""
    Write-Host "💡 Pro Tip: Use --launch-web flag to automatically open the web interface!" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "STOP: To stop all processes, run this script again - it will clean up automatically!" -ForegroundColor Yellow
Write-Host "HELP: For help and usage information, use: .\LaunchTodoSystem.ps1 --help" -ForegroundColor Cyan