#!/usr/bin/env python3
"""
Todo Management API

A comprehensive REST API for managing todo items across projects with
directory whitelisting security and full CRUD operations.
"""

import os
import yaml
import json
import hashlib
from datetime import datetime, date
from typing import List, Dict, Any, Optional, Union
from pathlib import Path

from fastapi import FastAPI, HTTPException, Query, Path as PathParam, Request, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel, Field, validator
import uvicorn
import yaml

# ============================================================================
# Schema Defaults
# ============================================================================

def load_schema_defaults():
    """Load default values from the schema YAML file."""
    schema_path = Path(__file__).parent.parent / "schema" / "todo-item-schema.yml"
    
    defaults = {}
    if schema_path.exists():
        try:
            with open(schema_path, 'r') as f:
                schema = yaml.safe_load(f)
                
            # Extract defaults from schema mapping
            if 'mapping' in schema:
                for field, config in schema['mapping'].items():
                    if 'default' in config:
                        defaults[field] = config['default']
                        
        except Exception as e:
            print(f"Warning: Could not load schema defaults: {e}")
    
    # Hardcoded fallbacks matching the schema
    fallback_defaults = {
        '_hash': '',
        'assigned_to': None,
        'category': 'general',
        'dependencies': [],
        'description': '',
        'due_date': None,
        'estimated_hours': 4.0,
        'priority': 'medium',
        'urgency': 'medium',
        'section': 'General Tasks',
        'source_file': '',
        'start_date': None,
        'status': 'pending',
        'parent_id': None,
        'tags': [],
        'list_order': 1000
    }
    
    # Merge loaded defaults with fallbacks
    return {**fallback_defaults, **defaults}

# Load schema defaults once at startup
SCHEMA_DEFAULTS = load_schema_defaults()

def apply_schema_defaults(todo_data):
    """Apply schema defaults to todo data for any missing fields."""
    result = dict(todo_data)  # Make a copy
    
    for field, default_value in SCHEMA_DEFAULTS.items():
        if field not in result or result[field] is None:
            # Only apply defaults for optional fields that are missing
            if field not in ['id', 'title', 'created_date']:  # Required fields
                result[field] = default_value
    
    return result

# ============================================================================
# Data Models
# ============================================================================

class Subtask(BaseModel):
    text: str
    completed: bool = False

class TodoBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=500)
    description: Optional[str] = None
    priority: str = Field(..., pattern=r'^(critical|high|medium|low)$')
    urgency: str = Field(default="medium", pattern=r'^(critical|high|medium|low)$')
    category: str = Field(..., min_length=1, max_length=100)
    estimated_hours: Optional[float] = Field(None, ge=0, le=1000)
    assigned_to: Optional[str] = None
    start_date: Optional[date] = None
    due_date: Optional[date] = None
    tags: List[str] = Field(default_factory=list)
    dependencies: List[str] = Field(default_factory=list)
    subtasks: List[Subtask] = Field(default_factory=list)
    section: Optional[str] = None
    list_order: Optional[int] = Field(default=None, description="Relative position in todo list")

class CreateTodoRequest(TodoBase):
    pass

class UpdateTodoRequest(TodoBase):
    status: str = Field(..., pattern=r'^(pending|completed|in_progress|blocked|cancelled)$')

class PatchTodoRequest(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    description: Optional[str] = None
    status: Optional[str] = Field(None, pattern=r'^(pending|completed|in_progress|blocked|cancelled)$')
    priority: Optional[str] = Field(None, pattern=r'^(critical|high|medium|low)$')
    urgency: Optional[str] = Field(None, pattern=r'^(critical|high|medium|low)$')
    category: Optional[str] = Field(None, min_length=1, max_length=100)
    estimated_hours: Optional[float] = Field(None, ge=0, le=1000)
    assigned_to: Optional[str] = None
    start_date: Optional[date] = None
    due_date: Optional[date] = None
    tags: Optional[List[str]] = None
    dependencies: Optional[List[str]] = None
    subtasks: Optional[List[Subtask]] = None
    section: Optional[str] = None
    list_order: Optional[int] = None

class Todo(TodoBase):
    id: str
    status: str = Field(..., pattern=r'^(pending|completed|in_progress|blocked|cancelled)$')
    created_date: date
    _hash: Optional[str] = None

class TodoListMetadata(BaseModel):
    project_name: str
    generated_date: Optional[date] = None
    last_updated: Optional[datetime] = None
    source: Optional[str] = None
    total_todos: int = 0
    integration_date: Optional[datetime] = None
    project_type: Optional[str] = Field(default="Work", pattern=r'^(Work|Home)$')
    display_name: Optional[str] = Field(default=None, max_length=100)

class ProjectSummary(BaseModel):
    name: str
    path: str
    total_todos: int
    completed_todos: int
    pending_todos: int
    last_updated: Optional[datetime] = None
    project_type: Optional[str] = "Work"
    display_name: Optional[str] = None
    urgency: Optional[str] = "Normal"
    importance: Optional[str] = "Normal"

class Project(BaseModel):
    name: str
    path: str
    todo_list_path: str
    metadata: TodoListMetadata
    total_todos: int

class UnmanagedProject(BaseModel):
    name: str
    path: str
    description: Optional[str] = None
    has_git: bool = False
    last_modified: Optional[datetime] = None

class WhitelistRequest(BaseModel):
    directory: str = Field(..., min_length=1)

class UpdateProjectMetadataRequest(BaseModel):
    project_type: Optional[str] = Field(None, pattern=r'^(Work|Home)$')
    display_name: Optional[str] = Field(None, max_length=100)
    urgency: Optional[str] = Field(None, pattern=r'^(High|Normal|Low)$')
    importance: Optional[str] = Field(None, pattern=r'^(High|Normal|Low)$')

class SuccessResponse(BaseModel):
    success: bool = True
    message: str

class ErrorResponse(BaseModel):
    error: str
    code: str
    details: Optional[Dict[str, Any]] = None

# ============================================================================
# API Configuration
# ============================================================================

app = FastAPI(
    title="Todo Management API",
    description="A comprehensive REST API for managing todo items across projects",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Enable CORS for local development
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ============================================================================
# Security and Configuration
# ============================================================================

class SecurityManager:
    """Manages directory whitelisting for secure file operations."""
    
    def __init__(self):
        self.whitelist_file = Path.home() / ".todo-api-whitelist.json"
        self.base_allowed_path = Path.home() / "source" / "repos"
        self._whitelisted_dirs = self._load_whitelist()
    
    def _load_whitelist(self) -> List[str]:
        """Load whitelisted directories from file."""
        if self.whitelist_file.exists():
            try:
                with open(self.whitelist_file, 'r') as f:
                    data = json.load(f)
                    return data.get('whitelisted_directories', [])
            except Exception:
                pass
        return []
    
    def _save_whitelist(self):
        """Save whitelisted directories to file."""
        try:
            self.whitelist_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.whitelist_file, 'w') as f:
                json.dump({'whitelisted_directories': self._whitelisted_dirs}, f, indent=2)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to save whitelist: {str(e)}")
    
    def add_directory(self, directory: str) -> bool:
        """Add a directory to the whitelist if it's under base_allowed_path."""
        dir_path = Path(directory).resolve()
        
        # Check if directory is under allowed base path
        try:
            dir_path.relative_to(self.base_allowed_path)
        except ValueError:
            raise HTTPException(
                status_code=403, 
                detail=f"Directory must be under {self.base_allowed_path}"
            )
        
        # Check if directory exists
        if not dir_path.exists():
            raise HTTPException(status_code=400, detail="Directory does not exist")
        
        dir_str = str(dir_path)
        if dir_str not in self._whitelisted_dirs:
            self._whitelisted_dirs.append(dir_str)
            self._save_whitelist()
            return True
        return False
    
    def remove_directory(self, directory: str) -> bool:
        """Remove a directory from the whitelist."""
        dir_path = str(Path(directory).resolve())
        if dir_path in self._whitelisted_dirs:
            self._whitelisted_dirs.remove(dir_path)
            self._save_whitelist()
            return True
        return False
    
    def is_path_allowed(self, path: str) -> bool:
        """Check if a path is under any whitelisted directory."""
        path_obj = Path(path).resolve()
        for whitelist_dir in self._whitelisted_dirs:
            try:
                path_obj.relative_to(whitelist_dir)
                return True
            except ValueError:
                continue
        return False
    
    def get_whitelisted_directories(self) -> List[str]:
        """Get list of whitelisted directories."""
        return self._whitelisted_dirs.copy()

# Global security manager instance
security_manager = SecurityManager()

# ============================================================================
# Todo Management Logic
# ============================================================================

class TodoManager:
    """Manages todo operations with file system integration."""
    
    @staticmethod
    def _generate_todo_id(title: str, category: str) -> str:
        """Generate a unique todo ID based on category and title."""
        # Use category prefix and sanitized title
        prefix = category.lower().replace(' ', '-')[:20]
        # Create a short hash of the title for uniqueness
        title_hash = hashlib.md5(title.encode()).hexdigest()[:6]
        return f"{prefix}-{title_hash}"
    
    @staticmethod
    def _calculate_hash(todo_data: Dict[str, Any]) -> str:
        """Calculate a hash for todo change detection."""
        # Create a normalized version for hashing (excluding hash field itself)
        hash_data = {k: v for k, v in todo_data.items() if k != '_hash'}
        content_str = json.dumps(hash_data, sort_keys=True, default=str)
        return hashlib.sha256(content_str.encode()).hexdigest()[:8]
    
    @staticmethod
    def get_project_path(project_name: str) -> Path:
        """Get the expected project path."""
        base_path = Path.home() / "source" / "repos" / project_name
        return base_path / "project-progress"
    
    @staticmethod
    def get_todo_file_path(project_name: str) -> Path:
        """Get the path to the consolidated todo file."""
        project_path = TodoManager.get_project_path(project_name)
        return project_path / "to-do-lists" / "todos.yml"
    
    @staticmethod
    def load_todos(project_name: str) -> Dict[str, Any]:
        """Load todos from the project's todo file."""
        todo_file = TodoManager.get_todo_file_path(project_name)
        
        if not todo_file.exists():
            raise HTTPException(status_code=404, detail="Project todo file not found")
        
        try:
            with open(todo_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f) or {}
                
                # Apply schema defaults to all loaded todos
                if 'todos' in data and isinstance(data['todos'], list):
                    for i, todo in enumerate(data['todos']):
                        data['todos'][i] = apply_schema_defaults(todo)
                        
                return data
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to load todos: {str(e)}")
    
    @staticmethod
    def save_todos(project_name: str, todo_data: Dict[str, Any]):
        """Save todos to the project's todo file."""
        todo_file = TodoManager.get_todo_file_path(project_name)
        
        # Check if path is whitelisted
        if not security_manager.is_path_allowed(str(todo_file)):
            raise HTTPException(
                status_code=403, 
                detail="Project directory not whitelisted for write operations"
            )
        
        try:
            # Ensure directory exists
            todo_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Update metadata
            todo_data.setdefault('metadata', {})
            todo_data['metadata']['last_updated'] = datetime.now().isoformat()
            todo_data['metadata']['total_todos'] = len(todo_data.get('todos', []))
            
            with open(todo_file, 'w', encoding='utf-8') as f:
                yaml.dump(todo_data, f, default_flow_style=False, indent=2, allow_unicode=True)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to save todos: {str(e)}")
    
    @staticmethod
    def find_todo(todos: List[Dict[str, Any]], todo_id: str) -> Optional[Dict[str, Any]]:
        """Find a todo by ID in the list."""
        for todo in todos:
            if todo.get('id') == todo_id:
                return todo
        return None
    
    @staticmethod
    def filter_todos(todos: List[Dict[str, Any]], status: Optional[str] = None, 
                    priority: Optional[str] = None, category: Optional[str] = None,
                    tag: Optional[str] = None) -> List[Dict[str, Any]]:
        """Filter todos based on criteria."""
        filtered = todos
        
        if status:
            filtered = [t for t in filtered if t.get('status') == status]
        if priority:
            filtered = [t for t in filtered if t.get('priority') == priority]
        if category:
            filtered = [t for t in filtered if t.get('category') == category]
        if tag:
            filtered = [t for t in filtered if tag in t.get('tags', [])]
        
        return filtered

# ============================================================================
# API Endpoints
# ============================================================================

@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint with API documentation links."""
    return """
    <html>
        <head><title>Todo Management API</title></head>
        <body>
            <h1>Todo Management API</h1>
            <p>A comprehensive REST API for managing todo items across projects.</p>
            <ul>
                <li><a href="/docs">OpenAPI Documentation (Swagger UI)</a></li>
                <li><a href="/redoc">Alternative Documentation (ReDoc)</a></li>
                <li><a href="/api/v1/health">Health Check</a></li>
            </ul>
        </body>
    </html>
    """

# Health Check
@app.get("/api/v1/health")
async def health_check():
    """Check API health and status."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

# Security Endpoints
@app.get("/api/v1/security/whitelist")
async def get_whitelisted_directories():
    """Get list of whitelisted directories."""
    return {"whitelisted_directories": security_manager.get_whitelisted_directories()}

@app.post("/api/v1/security/whitelist", response_model=SuccessResponse)
async def add_whitelisted_directory(request: WhitelistRequest):
    """Add a directory to the whitelist."""
    added = security_manager.add_directory(request.directory)
    message = "Directory added to whitelist" if added else "Directory already whitelisted"
    return SuccessResponse(message=message)

@app.delete("/api/v1/security/whitelist", response_model=SuccessResponse)
async def remove_whitelisted_directory(request: WhitelistRequest):
    """Remove a directory from the whitelist."""
    removed = security_manager.remove_directory(request.directory)
    if not removed:
        raise HTTPException(status_code=404, detail="Directory not found in whitelist")
    return SuccessResponse(message="Directory removed from whitelist")

# Project Endpoints
@app.get("/api/v1/projects")
async def list_projects():
    """List all projects with todo lists."""
    projects = []
    base_path = Path.home() / "source" / "repos"
    
    if not base_path.exists():
        return {"projects": []}
    
    for project_dir in base_path.iterdir():
        if project_dir.is_dir():
            todo_file = project_dir / "project-progress" / "to-do-lists" / "todos.yml"
            if todo_file.exists():
                try:
                    todo_data = TodoManager.load_todos(project_dir.name)
                    todos = todo_data.get('todos', [])
                    
                    completed_count = len([t for t in todos if t.get('status') == 'completed'])
                    pending_count = len([t for t in todos if t.get('status') == 'pending'])
                    
                    metadata = todo_data.get('metadata', {})
                    last_updated = metadata.get('last_updated')
                    if isinstance(last_updated, str):
                        try:
                            last_updated = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                        except:
                            last_updated = None
                    
                    projects.append(ProjectSummary(
                        name=project_dir.name,
                        path=str(project_dir),
                        total_todos=len(todos),
                        completed_todos=completed_count,
                        pending_todos=pending_count,
                        last_updated=last_updated,
                        project_type=metadata.get('project_type', 'Work'),
                        display_name=metadata.get('display_name'),
                        urgency=metadata.get('urgency', 'Normal'),
                        importance=metadata.get('importance', 'Normal')
                    ))
                except:
                    # Skip projects with invalid todo files
                    continue
    
    return {"projects": projects}

@app.get("/api/v1/unmanaged-projects")
async def list_unmanaged_projects():
    """List all projects in ~/source/repos that don't have todo management structure."""
    unmanaged_projects = []
    base_path = Path.home() / "source" / "repos"
    
    if not base_path.exists():
        return {"projects": []}
    
    for project_dir in base_path.iterdir():
        if project_dir.is_dir() and not project_dir.name.startswith('.'):
            todo_file = project_dir / "project-progress" / "to-do-lists" / "todos.yml"
            
            # Only include projects that DON'T have todo management structure
            if not todo_file.exists():
                try:
                    # Check if it's a git repository
                    has_git = (project_dir / ".git").exists()
                    
                    # Get last modified time
                    last_modified = None
                    try:
                        stat = project_dir.stat()
                        last_modified = datetime.fromtimestamp(stat.st_mtime)
                    except:
                        pass
                    
                    # Try to find a description from README files
                    description = None
                    readme_files = ['README.md', 'README.txt', 'readme.md', 'readme.txt']
                    for readme_name in readme_files:
                        readme_path = project_dir / readme_name
                        if readme_path.exists():
                            try:
                                with open(readme_path, 'r', encoding='utf-8') as f:
                                    # Get first non-header line as description
                                    lines = f.readlines()
                                    for line in lines[:10]:  # Check first 10 lines
                                        line = line.strip()
                                        if line and not line.startswith('#') and len(line) > 10:
                                            description = line[:200]  # Truncate to 200 chars
                                            break
                                    break
                            except:
                                continue
                    
                    unmanaged_projects.append(UnmanagedProject(
                        name=project_dir.name,
                        path=str(project_dir),
                        description=description,
                        has_git=has_git,
                        last_modified=last_modified
                    ))
                except Exception as e:
                    # Skip projects that can't be processed
                    continue
    
    # Sort by name
    unmanaged_projects.sort(key=lambda p: p.name.lower())
    
    return {"projects": unmanaged_projects}

@app.post("/api/v1/projects/{project_name}/enable-todo-management")
async def enable_todo_management(project_name: str = PathParam(..., description="Name of the project")):
    """Enable todo management for an existing project by creating the necessary directory structure."""
    base_path = Path.home() / "source" / "repos"
    project_path = base_path / project_name
    
    if not project_path.exists() or not project_path.is_dir():
        raise HTTPException(status_code=404, detail=f"Project '{project_name}' not found")
    
    # Check if todo management is already enabled
    todo_file = project_path / "project-progress" / "to-do-lists" / "todos.yml"
    if todo_file.exists():
        raise HTTPException(status_code=400, detail=f"Project '{project_name}' already has todo management enabled")
    
    try:
        # Create the directory structure
        todo_dir = project_path / "project-progress" / "to-do-lists"
        todo_dir.mkdir(parents=True, exist_ok=True)
        
        # Create initial todos.yml file
        initial_todo_data = {
            'metadata': {
                'project_name': project_name,
                'generated_date': date.today().isoformat(),
                'last_updated': datetime.now().isoformat(),
                'source': 'api_initialization',
                'total_todos': 1,
                'project_type': 'Work'
            },
            'todos': [
                {
                    'id': 'welcome-001',
                    '_hash': hashlib.md5(f"welcome-{project_name}".encode()).hexdigest()[:8],
                    'title': f'Welcome to {project_name} Todo Management',
                    'description': 'This is your first todo item. You can edit or delete it and start adding your own tasks.',
                    'status': 'pending',
                    'priority': 'medium',
                    'category': 'setup',
                    'created_date': date.today().isoformat(),
                    'estimated_hours': 0.25,
                    'assigned_to': None,
                    'due_date': None,
                    'tags': ['welcome', 'setup'],
                    'dependencies': [],
                    'subtasks': [],
                    'section': 'Project Setup'
                }
            ]
        }
        
        # Write the initial todos.yml file
        with open(todo_file, 'w', encoding='utf-8') as f:
            yaml.dump(initial_todo_data, f, default_flow_style=False, sort_keys=False)
        
        return SuccessResponse(
            message=f"Todo management enabled for project '{project_name}'. Initial structure created."
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to enable todo management: {str(e)}")

@app.patch("/api/v1/projects/{project_name}/metadata")
async def update_project_metadata(
    project_name: str = PathParam(..., description="Name of the project"),
    metadata_update: UpdateProjectMetadataRequest = Body(...)
):
    """Update project metadata like project type."""
    try:
        print(f"Updating metadata for project: {project_name}")
        print(f"Update data: {metadata_update}")
        
        # Load current todo data
        todo_data = TodoManager.load_todos(project_name)
        print(f"Loaded todo data keys: {list(todo_data.keys())}")
        
        # Ensure metadata exists
        if 'metadata' not in todo_data:
            todo_data['metadata'] = {}
            print("Created metadata section")
        
        # Update metadata
        print(f"metadata_update.project_type: {metadata_update.project_type}")
        print(f"metadata_update.display_name: {metadata_update.display_name}")
        
        if metadata_update.project_type:
            todo_data['metadata']['project_type'] = metadata_update.project_type
            print(f"Updated project_type to: {metadata_update.project_type}")
            
        if metadata_update.display_name is not None:
            if metadata_update.display_name:
                todo_data['metadata']['display_name'] = metadata_update.display_name
                print(f"Updated display_name to: {metadata_update.display_name}")
            else:
                # Remove display_name if it's empty
                todo_data['metadata'].pop('display_name', None)
                print("Removed display_name (was empty)")
            
        if metadata_update.urgency:
            todo_data['metadata']['urgency'] = metadata_update.urgency
            print(f"Updated urgency to: {metadata_update.urgency}")
            
        if metadata_update.importance:
            todo_data['metadata']['importance'] = metadata_update.importance
            print(f"Updated importance to: {metadata_update.importance}")
        
        print(f"Final metadata: {todo_data['metadata']}")
        
        # Update last_updated timestamp
        todo_data['metadata']['last_updated'] = datetime.now().isoformat()
        
        # Save the updated data using TodoManager
        TodoManager.save_todos(project_name, todo_data)
        
        return SuccessResponse(
            message=f"Project metadata updated successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update project metadata: {str(e)}")

@app.get("/api/v1/projects/{project_name}/progress-report")
async def generate_progress_report(project_name: str = PathParam(..., description="Name of the project")):
    """Generate an HTML progress report for a project."""
    try:
        # Load project todo data
        todo_data = TodoManager.load_todos(project_name)
        todos = todo_data.get('todos', [])
        metadata = todo_data.get('metadata', {})
        
        # Generate HTML content
        html_content = generate_project_progress_html(project_name, todos, metadata)
        
        return {"html": html_content, "project_name": project_name}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate progress report: {str(e)}")

def generate_project_progress_html(project_name: str, todos: List[Dict], metadata: Dict) -> str:
    """Generate HTML progress report from todos data."""
    from datetime import datetime, timedelta
    import re
    
    # Group todos by priority, category, status, and time periods
    grouped = {
        'by_priority': {'critical': [], 'high': [], 'medium': [], 'low': []},
        'by_category': {'testing': [], 'general': [], 'infrastructure': [], 'documentation': [], 'development': [], 'integration': []},
        'by_status': {'completed': [], 'in_progress': [], 'pending': []},
        'by_time': {'recent': [], 'this_week': [], 'older': []},
        'stats': {
            'total': len(todos),
            'completed': 0,
            'pending': 0,
            'in_progress': 0,
            'by_priority': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0},
            'by_category': {'testing': 0, 'general': 0, 'infrastructure': 0, 'documentation': 0, 'development': 0, 'integration': 0},
            'estimated_hours': {'total': 0, 'completed': 0, 'remaining': 0}
        }
    }
    
    # Time calculations for categorizing todos
    now = datetime.now()
    week_ago = now - timedelta(days=7)
    three_days_ago = now - timedelta(days=3)
    
    for todo in todos:
        priority = todo.get('priority', 'medium').lower()
        category = todo.get('category', 'general').lower()
        status = todo.get('status', 'pending').lower()
        estimated_hours = todo.get('estimated_hours', 0) or 0
        
        # Add to priority groups
        if priority in grouped['by_priority']:
            grouped['by_priority'][priority].append(todo)
            grouped['stats']['by_priority'][priority] += 1
        
        # Add to category groups  
        if category in grouped['by_category']:
            grouped['by_category'][category].append(todo)
            grouped['stats']['by_category'][category] += 1
        
        # Add to status groups
        if status in grouped['by_status']:
            grouped['by_status'][status].append(todo)
        
        # Time-based categorization
        todo_date = None
        created_date = todo.get('created_date')
        if created_date:
            try:
                if isinstance(created_date, str):
                    todo_date = datetime.fromisoformat(created_date.replace('Z', '+00:00'))
                elif isinstance(created_date, datetime):
                    todo_date = created_date
            except (ValueError, TypeError):
                pass
        
        if todo_date:
            if todo_date >= three_days_ago:
                grouped['by_time']['recent'].append(todo)
            elif todo_date >= week_ago:
                grouped['by_time']['this_week'].append(todo)
            else:
                grouped['by_time']['older'].append(todo)
        else:
            grouped['by_time']['older'].append(todo)
        
        # Update status stats
        if status == 'completed':
            grouped['stats']['completed'] += 1
            grouped['stats']['estimated_hours']['completed'] += estimated_hours
        elif status == 'in_progress':
            grouped['stats']['in_progress'] += 1
        else:
            grouped['stats']['pending'] += 1
        
        # Total estimated hours
        grouped['stats']['estimated_hours']['total'] += estimated_hours
    
    # Calculate remaining hours
    grouped['stats']['estimated_hours']['remaining'] = (
        grouped['stats']['estimated_hours']['total'] - grouped['stats']['estimated_hours']['completed']
    )
    
    # Calculate completion rate
    total = grouped['stats']['total']
    completed = grouped['stats']['completed']
    completion_rate = round((completed / max(total, 1)) * 100, 1)
    
    # Generate HTML for todo items
    def format_todo_html(todo):
        priority = todo.get('priority', 'medium').lower()
        status = todo.get('status', 'pending').lower()
        category = todo.get('category', 'general').lower()
        
        description = todo.get('description', '').replace('\n', '<br>')
        estimated_hours = todo.get('estimated_hours', 0)
        hours_text = f"{estimated_hours}h" if estimated_hours else ""
        
        # Format subtasks
        subtasks_html = ""
        subtasks = todo.get('subtasks', [])
        if subtasks:
            subtasks_html = '<div class="todo-subtasks"><h4>Subtasks:</h4>'
            for subtask in subtasks:
                completed = subtask.get('completed', False)
                text = subtask.get('text', '')
                checked = 'checked' if completed else ''
                completed_class = 'completed' if completed else ''
                subtasks_html += f'<div class="subtask {completed_class}"><input type="checkbox" class="subtask-checkbox" {checked} disabled><span>{text}</span></div>'
            subtasks_html += '</div>'
        
        # Format tags
        tags_html = ""
        tags = todo.get('tags', [])
        if tags:
            tags_html = '<div class="todo-tags">' + ''.join([f'<span class="tag">{tag}</span>' for tag in tags]) + '</div>'
        
        # Generate collapsed summary (first 100 chars of description)
        summary_description = description[:100] + '...' if len(description) > 100 else description
        
        return f'''
        <div class="todo-item {priority} {status}" onclick="toggleTodoDetails(this)">
            <div class="todo-header">
                <h4 class="todo-title">{todo.get('title', 'Untitled Todo')}</h4>
                <div class="todo-badges">
                    <span class="badge priority {priority}">{priority}</span>
                    <span class="badge status {status}">{status}</span>
                    <span class="badge category">{category}</span>
                    <span class="expand-toggle">▼</span>
                </div>
            </div>
            <div class="todo-summary-line" style="color: #6c757d; font-size: 14px; margin: 8px 0;">
                <span class="todo-id">{todo.get('id', 'N/A')}</span>
                {f' • <span class="estimated-hours">{hours_text}</span>' if hours_text else ''}
                {f' • <span class="todo-summary-desc">{summary_description}</span>' if summary_description else ''}
            </div>
            <div class="todo-details" style="display: none; margin-top: 10px;">
                <div class="todo-meta">
                    <span><strong>ID:</strong> {todo.get('id', 'N/A')}</span>
                    {f'<span><strong>Estimated:</strong> {hours_text}</span>' if hours_text else ''}
                    <span><strong>Created:</strong> {todo.get('created_date', 'N/A')}</span>
                </div>
                {f'<div class="todo-description">{description}</div>' if description else ''}
                {subtasks_html}
                {tags_html}
            </div>
        </div>
        '''
    
    # Generate sections
    priority_sections = {}
    for priority in ['critical', 'high', 'medium', 'low']:
        priority_sections[priority] = '\n'.join([format_todo_html(todo) for todo in grouped['by_priority'][priority]])
    
    category_sections = {}
    for category in ['testing', 'general', 'infrastructure', 'documentation', 'development', 'integration']:
        category_sections[category] = '\n'.join([format_todo_html(todo) for todo in grouped['by_category'][category]])
    
    # Generate complete HTML
    html_template = f'''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>{project_name} - Project Progress Report</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            /* Based on the workflow CSS but integrated */
            body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }}
            .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            h1 {{ color: white; background: #000000; padding: 20px; border-radius: 8px; margin-top: 0; text-align: center; }}
            .stats-summary {{ background: linear-gradient(135deg, #00c4aa 0%, #0099cc 100%); color: white; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }}
            .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 20px; margin-top: 15px; }}
            .stat-item {{ text-align: center; }}
            .stat-number {{ display: block; font-size: 2em; font-weight: bold; margin-bottom: 5px; }}
            .stat-label {{ font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; opacity: 0.9; }}
            .todo-item {{ background: white; border-left: 4px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 0 8px 8px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.08); cursor: pointer; transition: all 0.3s ease; }}
            .todo-item:hover {{ box-shadow: 0 4px 12px rgba(0,0,0,0.12); transform: translateY(-1px); }}
            .todo-item.critical {{ border-left-color: #00c4aa; }}
            .todo-item.high {{ border-left-color: #0099cc; }}
            .todo-item.medium {{ border-left-color: #1dd1a1; }}
            .todo-item.low {{ border-left-color: #95a5a6; }}
            .todo-item.completed {{ opacity: 0.6; border-left-color: #28a745; }}
            .todo-item.hidden {{ display: none; }}
            .todo-item.expanded .todo-details {{ display: block !important; }}
            .todo-item.expanded .expand-toggle {{ transform: rotate(180deg); }}
            .todo-header {{ display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px; }}
            .todo-title {{ font-weight: 600; font-size: 1.1em; color: #2c3e50; margin: 0; flex-grow: 1; }}
            .todo-badges {{ display: flex; gap: 8px; flex-shrink: 0; margin-left: 15px; }}
            .badge {{ padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; }}
            .badge.priority {{ color: white; }}
            .badge.priority.critical {{ background: #00c4aa; }}
            .badge.priority.high {{ background: #0099cc; }}
            .badge.priority.medium {{ background: #1dd1a1; }}
            .badge.priority.low {{ background: #95a5a6; }}
            .badge.status {{ background: #ecf0f1; color: #7f8c8d; }}
            .badge.status.completed {{ background: #28a745; color: white; }}
            .badge.status.in_progress {{ background: #f39c12; color: white; }}
            .badge.status.pending {{ background: #6c757d; color: white; }}
            .badge.category {{ background: #1dd1a1; color: white; }}
            .expand-toggle {{ background: #e9ecef; color: #6c757d; padding: 4px 6px; border-radius: 50%; font-size: 10px; cursor: pointer; transition: transform 0.3s ease; user-select: none; }}
            .expand-toggle:hover {{ background: #dee2e6; }}
            .todo-summary-line {{ line-height: 1.4; }}
            .todo-meta {{ display: flex; gap: 15px; margin-bottom: 10px; font-size: 13px; color: #6c757d; }}
            .todo-description {{ margin: 10px 0; line-height: 1.6; color: #555; }}
            .todo-subtasks {{ margin-top: 15px; }}
            .subtask {{ display: flex; align-items: center; margin: 8px 0; padding: 6px 0; font-size: 14px; }}
            .subtask-checkbox {{ margin-right: 10px; transform: scale(1.2); }}
            .subtask.completed {{ text-decoration: line-through; opacity: 0.7; }}
            .todo-tags {{ margin-top: 12px; }}
            .tag {{ display: inline-block; background: #e9ecef; color: #495057; padding: 3px 8px; border-radius: 12px; font-size: 11px; margin: 2px 4px 2px 0; }}
            .priority-section {{ margin: 30px 0; }}
            .category-section {{ margin: 20px 0; padding-left: 20px; }}
            .toggle-controls {{ background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }}
            .toggle-btn {{ background: #0099cc; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-size: 14px; }}
            .toggle-btn:hover {{ background: #0077aa; }}
            .toggle-btn.active {{ background: #28a745; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>{project_name} - Project Progress Report</h1>
            
            <!-- Executive Summary -->
            <div class="executive-summary" style="background: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #0099cc;">
                <h2 style="color: #2c3e50; margin-top: 0; margin-bottom: 15px;">📋 Executive Summary</h2>
                <p style="font-size: 16px; line-height: 1.6; color: #495057; margin-bottom: 15px;">
                    The <strong>{project_name}</strong> project currently has <strong>{total} todo items</strong> with a completion rate of <strong>{completion_rate}%</strong>. 
                    {f"{grouped['stats']['in_progress']} items are actively in progress" if grouped['stats']['in_progress'] > 0 else "No items are currently in active development"}, 
                    with {grouped['stats']['pending']} items pending and {completed} items completed.
                </p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 20px;">
                    <div style="background: white; padding: 15px; border-radius: 6px; text-align: center; border: 1px solid #dee2e6;">
                        <div style="font-size: 24px; font-weight: bold; color: #28a745;">{grouped['stats']['estimated_hours']['completed']:.1f}h</div>
                        <div style="font-size: 12px; color: #6c757d; text-transform: uppercase;">Completed Hours</div>
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 6px; text-align: center; border: 1px solid #dee2e6;">
                        <div style="font-size: 24px; font-weight: bold; color: #ffc107;">{grouped['stats']['estimated_hours']['remaining']:.1f}h</div>
                        <div style="font-size: 12px; color: #6c757d; text-transform: uppercase;">Remaining Hours</div>
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 6px; text-align: center; border: 1px solid #dee2e6;">
                        <div style="font-size: 24px; font-weight: bold; color: #17a2b8;">{grouped['stats']['estimated_hours']['total']:.1f}h</div>
                        <div style="font-size: 12px; color: #6c757d; text-transform: uppercase;">Total Estimated</div>
                    </div>
                </div>
            </div>

            <div class="stats-summary">
                <h3>📊 Project Overview</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">{total}</span>
                        <span class="stat-label">Total Todos</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{completed}</span>
                        <span class="stat-label">Completed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{grouped['stats']['pending']}</span>
                        <span class="stat-label">Pending</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{grouped['stats']['in_progress']}</span>
                        <span class="stat-label">In Progress</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{completion_rate}%</span>
                        <span class="stat-label">Complete</span>
                    </div>
                </div>
            </div>
            
            
            <!-- Achievements and Completed Items -->
            <div class="achievements-section" style="background: #e8f5e8; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
                <h2 style="color: #155724; margin-top: 0; margin-bottom: 15px;">🎉 Achievements & Completed Items</h2>
                {f'<p style="color: #155724; font-size: 16px; margin-bottom: 15px;">Successfully completed <strong>{completed} out of {total} items</strong> ({completion_rate}% completion rate) with an estimated <strong>{grouped["stats"]["estimated_hours"]["completed"]:.1f} hours</strong> of work delivered.</p>' if completed > 0 else '<p style="color: #155724; font-size: 16px;">No items have been completed yet. Focus on moving items from pending to in-progress status.</p>'}
                {''.join([f'<div style="background: white; margin: 8px 0; padding: 12px; border-radius: 6px; border-left: 3px solid #28a745;"><strong>{todo.get("title", "Untitled")}</strong> <span style="color: #6c757d;">({todo.get("category", "general")})</span><br><small style="color: #6c757d;">{todo.get("description", "")[:100]}{"..." if len(todo.get("description", "")) > 100 else ""}</small></div>' for todo in grouped['by_status']['completed'][:5]]) if grouped['by_status']['completed'] else ''}
                {f'<p style="margin-top: 15px; font-size: 14px; color: #155724;"><em>Showing {min(5, len(grouped["by_status"]["completed"]))} of {len(grouped["by_status"]["completed"])} completed items.</em></p>' if len(grouped['by_status']['completed']) > 0 else ''}
            </div>
            
            <!-- Current Focus Areas -->
            <div class="current-focus-section" style="background: #fff3cd; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
                <h2 style="color: #856404; margin-top: 0; margin-bottom: 15px;">🎯 Current Focus Areas</h2>
                {f'<p style="color: #856404; font-size: 16px; margin-bottom: 15px;">Currently working on <strong>{grouped["stats"]["in_progress"]} active items</strong> with an estimated <strong>{sum(todo.get("estimated_hours", 0) for todo in grouped["by_status"]["in_progress"]):.1f} hours</strong> of work in progress.</p>' if grouped['stats']['in_progress'] > 0 else '<p style="color: #856404; font-size: 16px;">No items are currently in active development. Consider moving pending items to in-progress status.</p>'}
                {''.join([f'<div style="background: white; margin: 8px 0; padding: 12px; border-radius: 6px; border-left: 3px solid #ffc107;"><strong>{todo.get("title", "Untitled")}</strong> <span style="color: #6c757d;">({todo.get("priority", "medium")} priority, {todo.get("estimated_hours", 0):.1f}h)</span><br><small style="color: #6c757d;">{todo.get("description", "")[:100]}{"..." if len(todo.get("description", "")) > 100 else ""}</small></div>' for todo in grouped['by_status']['in_progress']]) if grouped['by_status']['in_progress'] else ''}
            </div>
            
            <!-- Upcoming Priorities -->
            <div class="upcoming-priorities-section" style="background: #d1ecf1; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #17a2b8;">
                <h2 style="color: #0c5460; margin-top: 0; margin-bottom: 15px;">⏭️ Upcoming Priorities</h2>
                {f'<p style="color: #0c5460; font-size: 16px; margin-bottom: 15px;">There are <strong>{grouped["stats"]["pending"]} pending items</strong> waiting to be started, with an estimated <strong>{sum(todo.get("estimated_hours", 0) for todo in grouped["by_status"]["pending"]):.1f} hours</strong> of work ahead.</p>' if grouped['stats']['pending'] > 0 else '<p style="color: #0c5460; font-size: 16px;">No pending items. All work is either completed or in progress!</p>'}
                <div style="margin-bottom: 15px;">
                    <h4 style="color: #0c5460; margin-bottom: 10px;">High Priority Items ({len([t for t in grouped['by_status']['pending'] if t.get('priority', 'medium') in ['critical', 'high']])}):</h4>
                    {''.join([f'<div style="background: white; margin: 8px 0; padding: 12px; border-radius: 6px; border-left: 3px solid #dc3545;"><strong>{todo.get("title", "Untitled")}</strong> <span style="color: #6c757d;">({todo.get("priority", "medium")} priority, {todo.get("estimated_hours", 0):.1f}h)</span><br><small style="color: #6c757d;">{todo.get("description", "")[:100]}{"..." if len(todo.get("description", "")) > 100 else ""}</small></div>' for todo in [t for t in grouped['by_status']['pending'] if t.get('priority', 'medium') in ['critical', 'high']][:3]]) if any(t.get('priority', 'medium') in ['critical', 'high'] for t in grouped['by_status']['pending']) else '<p style="color: #6c757d; font-style: italic;">No high priority pending items.</p>'}
                </div>
                <div>
                    <h4 style="color: #0c5460; margin-bottom: 10px;">Next Steps Recommendation:</h4>
                    <ul style="color: #0c5460; line-height: 1.6;">
                        {f'<li>Move {min(3, grouped["stats"]["pending"])} highest priority pending items to in-progress status</li>' if grouped['stats']['pending'] > 0 else '<li>Create new todo items to maintain project momentum</li>'}
                        {f'<li>Focus on completing {grouped["stats"]["in_progress"]} in-progress items before starting new work</li>' if grouped['stats']['in_progress'] > 0 else '<li>Start working on pending items by moving them to in-progress</li>'}
                        <li>Review and update item priorities based on current project needs</li>
                        <li>Consider breaking down large items into smaller, manageable subtasks</li>
                    </ul>
                </div>
            </div>
            
            <hr>
            
            <!-- Todo Controls -->
            <div class="todo-controls" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                <div class="toggle-controls">
                    <button class="toggle-btn" onclick="toggleCompletedTodos()" style="background: #0099cc; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-size: 14px;">Show Completed Todos</button>
                </div>
                <div class="pagination-controls" style="display: flex; align-items: center; gap: 10px;">
                    <label for="items-per-page" style="font-weight: 600; color: #495057;">Items per page:</label>
                    <select id="items-per-page" onchange="updatePagination()" style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; background: white;">
                        <option value="10" selected>10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                        <option value="all">All</option>
                    </select>
                    <div class="pagination-nav" style="display: flex; gap: 5px;">
                        <button id="prev-page" onclick="changePage(-1)" style="padding: 8px 12px; border: 1px solid #ced4da; background: white; border-radius: 4px; cursor: pointer;" disabled>‹ Prev</button>
                        <span id="page-info" style="padding: 8px 12px; color: #495057; font-weight: 600;">Page 1</span>
                        <button id="next-page" onclick="changePage(1)" style="padding: 8px 12px; border: 1px solid #ced4da; background: white; border-radius: 4px; cursor: pointer;">Next ›</button>
                    </div>
                </div>
            </div>
            
            <div class="priority-section">
                <h2>🔥 Critical Priority ({grouped['stats']['by_priority']['critical']})</h2>
                {priority_sections['critical']}
            </div>
            
            <div class="priority-section">
                <h2>⚡ High Priority ({grouped['stats']['by_priority']['high']})</h2>
                {priority_sections['high']}
            </div>
            
            <div class="priority-section">
                <h2>📋 Medium Priority ({grouped['stats']['by_priority']['medium']})</h2>
                {priority_sections['medium']}
            </div>
            
            <div class="priority-section">
                <h2>📝 Low Priority ({grouped['stats']['by_priority']['low']})</h2>
                {priority_sections['low']}
            </div>
            
            <hr>
            
            <h2>📂 By Category</h2>
            
            <div class="category-section">
                <h3>🧪 Testing ({grouped['stats']['by_category']['testing']})</h3>
                {category_sections['testing']}
            </div>
            
            <div class="category-section">
                <h3>📝 General ({grouped['stats']['by_category']['general']})</h3>
                {category_sections['general']}
            </div>
            
            <div class="category-section">
                <h3>🏗️ Infrastructure ({grouped['stats']['by_category']['infrastructure']})</h3>
                {category_sections['infrastructure']}
            </div>
            
            <div class="category-section">
                <h3>📖 Documentation ({grouped['stats']['by_category']['documentation']})</h3>
                {category_sections['documentation']}
            </div>
            
            <div class="category-section">
                <h3>💻 Development ({grouped['stats']['by_category']['development']})</h3>
                {category_sections['development']}
            </div>
            
            <div class="category-section">
                <h3>🔗 Integration ({grouped['stats']['by_category']['integration']})</h3>
                {category_sections['integration']}
            </div>
            
            <!-- Development Activity Summary -->
            <div class="development-activity-section" style="background: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #6f42c1;">
                <h2 style="color: #5a2d82; margin-top: 0; margin-bottom: 15px;">📈 Development Activity Summary</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div style="background: white; padding: 20px; border-radius: 6px; text-align: center; border: 1px solid #dee2e6;">
                        <h4 style="color: #5a2d82; margin-top: 0;">Project Velocity</h4>
                        <div style="font-size: 28px; font-weight: bold; color: #28a745; margin: 10px 0;">{completion_rate:.1f}%</div>
                        <p style="margin: 0; color: #6c757d; font-size: 14px;">Completion Rate</p>
                    </div>
                    <div style="background: white; padding: 20px; border-radius: 6px; text-align: center; border: 1px solid #dee2e6;">
                        <h4 style="color: #5a2d82; margin-top: 0;">Work Distribution</h4>
                        <div style="font-size: 16px; color: #495057; line-height: 1.4;">
                            <div>Critical: {grouped['stats']['by_priority']['critical']}</div>
                            <div>High: {grouped['stats']['by_priority']['high']}</div>
                            <div>Medium: {grouped['stats']['by_priority']['medium']}</div>
                            <div>Low: {grouped['stats']['by_priority']['low']}</div>
                        </div>
                    </div>
                    <div style="background: white; padding: 20px; border-radius: 6px; text-align: center; border: 1px solid #dee2e6;">
                        <h4 style="color: #5a2d82; margin-top: 0;">Time Investment</h4>
                        <div style="font-size: 20px; font-weight: bold; color: #17a2b8; margin: 5px 0;">{grouped['stats']['estimated_hours']['total']:.1f}h</div>
                        <div style="font-size: 14px; color: #6c757d;">Total Estimated</div>
                        <div style="font-size: 14px; color: #28a745; margin-top: 8px;">{grouped['stats']['estimated_hours']['completed']:.1f}h Completed</div>
                    </div>
                </div>
                <div style="margin-top: 20px; padding: 15px; background: #e9ecef; border-radius: 6px;">
                    <h4 style="color: #5a2d82; margin-top: 0; margin-bottom: 10px;">Recent Activity Insights:</h4>
                    <ul style="color: #495057; line-height: 1.6; margin: 0; padding-left: 20px;">
                        <li>Most active category: <strong>{max(grouped['stats']['by_category'].items(), key=lambda x: x[1]) if grouped['stats']['by_category'] else "No category data"}</strong></li>
                        <li>Progress trend: {"Excellent progress" if completion_rate >= 80 else "Good progress" if completion_rate >= 60 else "Steady progress" if completion_rate >= 40 else "Getting started" if completion_rate >= 20 else "Early stage"}</li>
                        <li>Work balance: {f"{grouped['stats']['in_progress']} active, {grouped['stats']['pending']} queued" if grouped['stats']['pending'] > 0 else "Focus on active items"}</li>
                        <li>Next milestone: {"Near completion" if completion_rate >= 90 else f"Target {((completion_rate // 10) + 1) * 10}% completion" if completion_rate < 90 else "Project completion"}</li>
                    </ul>
                </div>
            </div>
            
            <!-- Git Activity & Recent Changes -->
            <div class="git-activity-section" style="background: #e3f2fd; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3;">
                <h2 style="color: #0d47a1; margin-top: 0; margin-bottom: 15px;">📊 Git Activity & Recent Changes (Last 2 Weeks)</h2>
                <div id="git-activity-content">
                    <p style="color: #0d47a1; font-style: italic; margin: 20px 0;">Loading git activity data...</p>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                        <div style="background: white; padding: 20px; border-radius: 6px; text-align: center; border: 1px solid #bbdefb;">
                            <h4 style="color: #0d47a1; margin-top: 0;">Recent Commits</h4>
                            <div id="commit-count" style="font-size: 28px; font-weight: bold; color: #2196f3; margin: 10px 0;">--</div>
                            <p style="margin: 0; color: #6c757d; font-size: 14px;">Total Commits</p>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 6px; text-align: center; border: 1px solid #bbdefb;">
                            <h4 style="color: #0d47a1; margin-top: 0;">Lines Changed</h4>
                            <div style="font-size: 16px; color: #495057; line-height: 1.4;">
                                <div id="additions" style="color: #28a745;">+-- additions</div>
                                <div id="deletions" style="color: #dc3545;">--- deletions</div>
                            </div>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 6px; text-align: center; border: 1px solid #bbdefb;">
                            <h4 style="color: #0d47a1; margin-top: 0;">Files Modified</h4>
                            <div id="files-changed" style="font-size: 28px; font-weight: bold; color: #ff9800; margin: 10px 0;">--</div>
                            <p style="margin: 0; color: #6c757d; font-size: 14px;">Unique Files</p>
                        </div>
                    </div>
                    <div style="margin-top: 20px; padding: 15px; background: #f3e5f5; border-radius: 6px; border-left: 3px solid #9c27b0;">
                        <h4 style="color: #4a148c; margin-top: 0; margin-bottom: 10px;">📝 Recent Commit Summary:</h4>
                        <div id="recent-commits" style="color: #495057; line-height: 1.6;">
                            <p style="font-style: italic;">Loading recent commits...</p>
                        </div>
                    </div>
                    <div style="margin-top: 15px; padding: 15px; background: #fff3e0; border-radius: 6px; border-left: 3px solid #ff9800;">
                        <h4 style="color: #e65100; margin-top: 0; margin-bottom: 10px;">🔍 Most Active Files:</h4>
                        <div id="active-files" style="color: #495057; line-height: 1.6;">
                            <p style="font-style: italic;">Analyzing file changes...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <hr>
            <p><em>This comprehensive project progress report was automatically generated from todo data with enhanced sections matching workflow-generated reports.</em></p>
            <p><small><strong>Generated:</strong> {datetime.now().strftime('%B %d, %Y at %I:%M %p')}</small></p>
        </div>
        
        <script>
            // Pagination state
            let currentPage = 1;
            let itemsPerPage = 10;
            let allTodos = [];
            let showCompleted = false;
            
            function toggleCompletedTodos() {{
                const toggleBtn = document.querySelector('.toggle-btn');
                showCompleted = !showCompleted;
                
                if (showCompleted) {{
                    toggleBtn.textContent = 'Hide Completed Todos';
                    toggleBtn.style.background = '#28a745';
                }} else {{
                    toggleBtn.textContent = 'Show Completed Todos';
                    toggleBtn.style.background = '#0099cc';
                }}
                
                currentPage = 1; // Reset to first page
                updatePagination();
            }}
            
            function updatePagination() {{
                const select = document.getElementById('items-per-page');
                itemsPerPage = select.value === 'all' ? 'all' : parseInt(select.value);
                currentPage = 1; // Reset to first page
                paginateTodos();
            }}
            
            function changePage(direction) {{
                currentPage += direction;
                paginateTodos();
            }}
            
            function paginateTodos() {{
                // Get all visible todos (excluding completed if hidden)
                const visibleTodos = allTodos.filter(todo => {{
                    const isCompleted = todo.classList.contains('completed');
                    return showCompleted || !isCompleted;
                }});
                
                const totalItems = visibleTodos.length;
                const totalPages = itemsPerPage === 'all' ? 1 : Math.ceil(totalItems / itemsPerPage);
                
                // Ensure current page is valid
                currentPage = Math.max(1, Math.min(currentPage, totalPages));
                
                // Hide all todos first
                allTodos.forEach(todo => {{
                    todo.style.display = 'none';
                }});
                
                // Show todos for current page
                if (itemsPerPage === 'all') {{
                    visibleTodos.forEach(todo => {{
                        todo.style.display = 'block';
                    }});
                }} else {{
                    const start = (currentPage - 1) * itemsPerPage;
                    const end = start + itemsPerPage;
                    const todosToShow = visibleTodos.slice(start, end);
                    
                    todosToShow.forEach(todo => {{
                        todo.style.display = 'block';
                    }});
                }}
                
                // Update pagination controls
                const prevBtn = document.getElementById('prev-page');
                const nextBtn = document.getElementById('next-page');
                const pageInfo = document.getElementById('page-info');
                
                prevBtn.disabled = currentPage <= 1;
                nextBtn.disabled = currentPage >= totalPages || itemsPerPage === 'all';
                
                if (itemsPerPage === 'all') {{
                    pageInfo.textContent = `Showing all ${totalItems} items`;
                }} else {{
                    const start = Math.min((currentPage - 1) * itemsPerPage + 1, totalItems);
                    const end = Math.min(currentPage * itemsPerPage, totalItems);
                    pageInfo.textContent = `${start}-${end} of ${totalItems} (Page ${currentPage}/${totalPages})`;
                }}
                
                // Update button styles
                prevBtn.style.opacity = prevBtn.disabled ? '0.5' : '1';
                nextBtn.style.opacity = nextBtn.disabled ? '0.5' : '1';
                prevBtn.style.cursor = prevBtn.disabled ? 'not-allowed' : 'pointer';
                nextBtn.style.cursor = nextBtn.disabled ? 'not-allowed' : 'pointer';
            }}
            
            // Todo expand/collapse functionality
            function toggleTodoDetails(todoElement) {{
                const isExpanded = todoElement.classList.contains('expanded');
                const toggle = todoElement.querySelector('.expand-toggle');
                
                if (isExpanded) {{
                    todoElement.classList.remove('expanded');
                    toggle.textContent = '▼';
                }} else {{
                    todoElement.classList.add('expanded');
                    toggle.textContent = '▲';
                }}
                
                // Prevent click from interfering with pagination count
                event.stopPropagation();
            }}
            
            // Git activity functions
            async function loadGitActivity() {{
                try {{
                    const response = await fetch('/api/git-activity/{project_name}');
                    if (response.ok) {{
                        const data = await response.json();
                        displayGitActivity(data);
                    }} else {{
                        document.getElementById('git-activity-content').innerHTML = 
                            '<p style="color: #dc3545; font-style: italic;">Unable to load git activity data. Git repository may not be available.</p>';
                    }}
                }} catch (error) {{
                    console.error('Error loading git activity:', error);
                    document.getElementById('git-activity-content').innerHTML = 
                        '<p style="color: #dc3545; font-style: italic;">Error loading git activity data.</p>';
                }}
            }}
            
            function displayGitActivity(data) {{
                const stats = data.statistics || {{}};
                const commits = data.commits || [];
                
                // Update statistics
                document.getElementById('commit-count').textContent = stats.total_commits || 0;
                document.getElementById('additions').textContent = `+${{(stats.total_additions || 0).toLocaleString()}} additions`;
                document.getElementById('deletions').textContent = `-${{(stats.total_deletions || 0).toLocaleString()}} deletions`;
                document.getElementById('files-changed').textContent = stats.files_modified_count || 0;
                
                // Recent commits
                const recentCommitsEl = document.getElementById('recent-commits');
                if (commits.length > 0) {{
                    const recentCommits = commits.slice(0, 5).map(commit => {{
                        const date = new Date(commit.date).toLocaleDateString();
                        const message = commit.message.length > 80 ? commit.message.substring(0, 80) + '...' : commit.message;
                        return `<div style="margin: 8px 0; padding: 8px; background: white; border-radius: 4px; border-left: 3px solid #2196f3;">
                            <strong>${{message}}</strong><br>
                            <small style="color: #6c757d;">${{commit.author}} - ${{date}}</small>
                        </div>`;
                    }}).join('');
                    recentCommitsEl.innerHTML = recentCommits;
                }} else {{
                    recentCommitsEl.innerHTML = '<p style="font-style: italic;">No recent commits found.</p>';
                }}
                
                // Most active files
                const activeFilesEl = document.getElementById('active-files');
                const fileStats = stats.file_changes || {{}};
                if (Object.keys(fileStats).length > 0) {{
                    const sortedFiles = Object.entries(fileStats)
                        .sort(([,a], [,b]) => (b.additions + b.deletions) - (a.additions + a.deletions))
                        .slice(0, 8);
                    
                    const filesHtml = sortedFiles.map(([file, changes]) => {{
                        const fileName = file.split('/').pop() || file;
                        const totalChanges = changes.additions + changes.deletions;
                        return `<div style="margin: 4px 0; padding: 6px; background: white; border-radius: 4px; display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-weight: 600;">${{fileName}}</span>
                            <span style="font-size: 12px; color: #6c757d;">
                                <span style="color: #28a745;">+${{changes.additions}}</span>
                                <span style="color: #dc3545;">-${{changes.deletions}}</span>
                                (${{totalChanges}} total)
                            </span>
                        </div>`;
                    }}).join('');
                    activeFilesEl.innerHTML = filesHtml;
                }} else {{
                    activeFilesEl.innerHTML = '<p style="font-style: italic;">No file change data available.</p>';
                }}
            }}
            
            // Initialize
            document.addEventListener('DOMContentLoaded', function() {{
                // Collect all todo items
                allTodos = Array.from(document.querySelectorAll('.todo-item'));
                
                // Initialize pagination
                paginateTodos();
                
                // Load git activity
                loadGitActivity();
            }});
        </script>
    </body>
    </html>
    '''
    
    return html_template

@app.get("/api/v1/projects/{project_name}", response_model=Project)
async def get_project(project_name: str = PathParam(..., description="Name of the project")):
    """Get detailed information about a specific project."""
    try:
        todo_data = TodoManager.load_todos(project_name)
        metadata_dict = todo_data.get('metadata', {})
        
        # Convert datetime strings
        for date_field in ['last_updated', 'integration_date']:
            if date_field in metadata_dict and isinstance(metadata_dict[date_field], str):
                try:
                    metadata_dict[date_field] = datetime.fromisoformat(
                        metadata_dict[date_field].replace('Z', '+00:00')
                    )
                except:
                    metadata_dict[date_field] = None
        
        # Convert date strings
        if 'generated_date' in metadata_dict and isinstance(metadata_dict['generated_date'], str):
            try:
                metadata_dict['generated_date'] = datetime.strptime(
                    metadata_dict['generated_date'], '%Y-%m-%d'
                ).date()
            except:
                metadata_dict['generated_date'] = None
        
        metadata = TodoListMetadata(**metadata_dict)
        
        return Project(
            name=project_name,
            path=str(TodoManager.get_project_path(project_name).parent),
            todo_list_path=str(TodoManager.get_todo_file_path(project_name)),
            metadata=metadata,
            total_todos=len(todo_data.get('todos', []))
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get project: {str(e)}")

# Todo Endpoints
@app.get("/api/v1/projects/{project_name}/todos")
async def list_todos(
    project_name: str = PathParam(..., description="Name of the project"),
    status: Optional[str] = Query(None, pattern=r'^(pending|completed|in_progress|cancelled)$'),
    priority: Optional[str] = Query(None, pattern=r'^(critical|high|medium|low)$'),
    category: Optional[str] = Query(None),
    tag: Optional[str] = Query(None)
):
    """List todos for a project with optional filtering."""
    todo_data = TodoManager.load_todos(project_name)
    todos = todo_data.get('todos', [])
    
    # Apply filters
    filtered_todos = TodoManager.filter_todos(todos, status, priority, category, tag)
    
    # Convert date strings to proper date objects
    for todo in filtered_todos:
        if 'created_date' in todo and isinstance(todo['created_date'], str):
            try:
                todo['created_date'] = datetime.strptime(todo['created_date'], '%Y-%m-%d').date()
            except:
                pass
        if 'due_date' in todo and isinstance(todo['due_date'], str):
            try:
                todo['due_date'] = datetime.strptime(todo['due_date'], '%Y-%m-%d').date()
            except:
                pass
    
    metadata_dict = todo_data.get('metadata', {})
    # Convert metadata datetime fields
    for date_field in ['last_updated', 'integration_date']:
        if date_field in metadata_dict and isinstance(metadata_dict[date_field], str):
            try:
                metadata_dict[date_field] = datetime.fromisoformat(
                    metadata_dict[date_field].replace('Z', '+00:00')
                )
            except:
                metadata_dict[date_field] = None
    
    if 'generated_date' in metadata_dict and isinstance(metadata_dict['generated_date'], str):
        try:
            metadata_dict['generated_date'] = datetime.strptime(
                metadata_dict['generated_date'], '%Y-%m-%d'
            ).date()
        except:
            metadata_dict['generated_date'] = None
    
    metadata = TodoListMetadata(**metadata_dict)
    
    return {
        "todos": filtered_todos,
        "metadata": metadata
    }

@app.get("/api/v1/projects/{project_name}/todos/{todo_id}", response_model=Todo)
async def get_todo(
    project_name: str = PathParam(..., description="Name of the project"),
    todo_id: str = PathParam(..., description="ID of the todo item")
):
    """Get a specific todo by its ID."""
    todo_data = TodoManager.load_todos(project_name)
    todos = todo_data.get('todos', [])
    
    todo = TodoManager.find_todo(todos, todo_id)
    if not todo:
        raise HTTPException(status_code=404, detail="Todo not found")
    
    # Convert date strings
    if 'created_date' in todo and isinstance(todo['created_date'], str):
        try:
            todo['created_date'] = datetime.strptime(todo['created_date'], '%Y-%m-%d').date()
        except:
            pass
    if 'due_date' in todo and isinstance(todo['due_date'], str):
        try:
            todo['due_date'] = datetime.strptime(todo['due_date'], '%Y-%m-%d').date()
        except:
            pass
    
    return Todo(**todo)

@app.post("/api/v1/projects/{project_name}/todos", response_model=Todo, status_code=201)
async def create_todo(
    request: CreateTodoRequest,
    project_name: str = PathParam(..., description="Name of the project")
):
    """Create a new todo item for the specified project."""
    todo_data = TodoManager.load_todos(project_name)
    todos = todo_data.get('todos', [])
    
    # Generate ID
    todo_id = TodoManager._generate_todo_id(request.title, request.category)
    
    # Check for duplicate IDs
    counter = 1
    original_id = todo_id
    while TodoManager.find_todo(todos, todo_id):
        todo_id = f"{original_id}-{counter:03d}"
        counter += 1
    
    # Create new todo
    new_todo = {
        "id": todo_id,
        "title": request.title,
        "description": request.description,
        "status": "pending",
        "priority": request.priority,
        "urgency": getattr(request, 'urgency', 'medium'),
        "category": request.category,
        "estimated_hours": request.estimated_hours,
        "assigned_to": request.assigned_to,
        "due_date": request.due_date.isoformat() if request.due_date else None,
        "created_date": date.today().isoformat(),
        "tags": request.tags,
        "dependencies": request.dependencies,
        "subtasks": [s.dict() for s in request.subtasks],
        "section": request.section,
        "list_order": getattr(request, 'list_order', SCHEMA_DEFAULTS.get('list_order', 1000))
    }
    
    # Apply schema defaults
    new_todo = apply_schema_defaults(new_todo)
    
    # Calculate hash
    new_todo["_hash"] = TodoManager._calculate_hash(new_todo)
    
    # Add to list
    todos.append(new_todo)
    todo_data["todos"] = todos
    
    # Save
    TodoManager.save_todos(project_name, todo_data)
    
    # Convert date for response
    new_todo['created_date'] = date.today()
    if new_todo['due_date']:
        new_todo['due_date'] = request.due_date
    
    return Todo(**new_todo)

@app.put("/api/v1/projects/{project_name}/todos/{todo_id}", response_model=Todo)
async def update_todo(
    request: UpdateTodoRequest,
    project_name: str = PathParam(..., description="Name of the project"),
    todo_id: str = PathParam(..., description="ID of the todo item")
):
    """Update an existing todo item."""
    todo_data = TodoManager.load_todos(project_name)
    todos = todo_data.get('todos', [])
    
    todo = TodoManager.find_todo(todos, todo_id)
    if not todo:
        raise HTTPException(status_code=404, detail="Todo not found")
    
    # Update fields
    todo.update({
        "title": request.title,
        "description": request.description,
        "status": request.status,
        "priority": request.priority,
        "category": request.category,
        "estimated_hours": request.estimated_hours,
        "assigned_to": request.assigned_to,
        "due_date": request.due_date.isoformat() if request.due_date else None,
        "tags": request.tags,
        "dependencies": request.dependencies,
        "subtasks": [s.dict() for s in request.subtasks],
        "section": request.section
    })
    
    # Update hash
    todo["_hash"] = TodoManager._calculate_hash(todo)
    
    # Save
    TodoManager.save_todos(project_name, todo_data)
    
    # Convert dates for response
    if 'created_date' in todo and isinstance(todo['created_date'], str):
        try:
            todo['created_date'] = datetime.strptime(todo['created_date'], '%Y-%m-%d').date()
        except:
            pass
    if todo['due_date']:
        todo['due_date'] = request.due_date
    
    return Todo(**todo)

@app.patch("/api/v1/projects/{project_name}/todos/{todo_id}", response_model=Todo)
async def patch_todo(
    request: PatchTodoRequest,
    project_name: str = PathParam(..., description="Name of the project"),
    todo_id: str = PathParam(..., description="ID of the todo item")
):
    """Partially update a todo item."""
    todo_data = TodoManager.load_todos(project_name)
    todos = todo_data.get('todos', [])
    
    todo = TodoManager.find_todo(todos, todo_id)
    if not todo:
        raise HTTPException(status_code=404, detail="Todo not found")
    
    # Update only provided fields
    update_data = request.dict(exclude_unset=True)
    if 'due_date' in update_data and update_data['due_date'] is not None:
        update_data['due_date'] = update_data['due_date'].isoformat()
    if 'subtasks' in update_data:
        update_data['subtasks'] = [s.dict() for s in request.subtasks] if request.subtasks else []
    
    todo.update(update_data)
    
    # Update hash
    todo["_hash"] = TodoManager._calculate_hash(todo)
    
    # Save
    TodoManager.save_todos(project_name, todo_data)
    
    # Convert dates for response
    if 'created_date' in todo and isinstance(todo['created_date'], str):
        try:
            todo['created_date'] = datetime.strptime(todo['created_date'], '%Y-%m-%d').date()
        except:
            pass
    if 'due_date' in todo and todo['due_date']:
        try:
            todo['due_date'] = datetime.strptime(todo['due_date'], '%Y-%m-%d').date()
        except:
            pass
    
    return Todo(**todo)

@app.delete("/api/v1/projects/{project_name}/todos/{todo_id}", status_code=204)
async def delete_todo(
    project_name: str = PathParam(..., description="Name of the project"),
    todo_id: str = PathParam(..., description="ID of the todo item")
):
    """Delete a specific todo item."""
    todo_data = TodoManager.load_todos(project_name)
    todos = todo_data.get('todos', [])
    
    todo = TodoManager.find_todo(todos, todo_id)
    if not todo:
        raise HTTPException(status_code=404, detail="Todo not found")
    
    # Remove todo
    todos.remove(todo)
    todo_data["todos"] = todos
    
    # Save
    TodoManager.save_todos(project_name, todo_data)

# ============================================================================
# Main Application
# ============================================================================

# =====================================================================
# WORKFLOW MANAGEMENT ENDPOINTS
# =====================================================================

@app.get("/api/workflows")
async def get_available_workflows():
    """Get list of available workflows from the scripts directory"""
    try:
        scripts_path = Path.home() / "source" / "repos" / "scripts"
        workflows_path = scripts_path / "workflows"
        
        if not workflows_path.exists():
            return {"workflows": []}
        
        workflows = []
        
        # Scan for workflow directories
        for workflow_dir in workflows_path.iterdir():
            if workflow_dir.is_dir():
                workflow_yml = workflow_dir / "workflow.yml"
                if workflow_yml.exists():
                    try:
                        # Read workflow metadata
                        with open(workflow_yml, 'r', encoding='utf-8') as f:
                            import yaml
                            workflow_config = yaml.safe_load(f)
                        
                        workflow_info = {
                            "id": workflow_dir.name,
                            "name": workflow_config.get("workflow", {}).get("name", workflow_dir.name),
                            "description": workflow_config.get("workflow", {}).get("description", "No description available"),
                            "version": workflow_config.get("workflow", {}).get("version", "1.0"),
                            "total_steps": workflow_config.get("metadata", {}).get("total_steps", 0),
                            "path": str(workflow_dir)
                        }
                        workflows.append(workflow_info)
                    except Exception as e:
                        logger.error(f"Error reading workflow config for {workflow_dir.name}: {e}")
        
        return {"workflows": workflows}
    
    except Exception as e:
        logger.error(f"Error loading workflows: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to load workflows: {str(e)}")


@app.post("/api/workflows/{workflow_id}/start")
async def start_workflow(workflow_id: str, config: dict):
    """Start a workflow execution"""
    try:
        import subprocess
        import uuid
        from datetime import datetime
        
        # Generate unique execution ID
        execution_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        
        # Scripts directory
        scripts_path = Path.home() / "source" / "repos" / "scripts"
        workflow_path = scripts_path / "workflows" / workflow_id
        
        if not workflow_path.exists():
            raise HTTPException(status_code=404, detail=f"Workflow '{workflow_id}' not found")
        
        # Create execution directory for status files
        execution_dir = workflow_path / "config" / "runs" / f"{timestamp}_{execution_id[:8]}"
        execution_dir.mkdir(parents=True, exist_ok=True)
        
        # Create status HTML file path
        status_file = execution_dir / "workflow_status.html"
        
        # Prepare command arguments
        cmd = [
            "python", 
            str(scripts_path / "run-workflow.py"),
            "--workflow-dir", str(workflow_path),
            "--project-path", config.get("project_path", ""),
            "--execution-id", execution_id,
            "--status-file", str(status_file)
        ]
        
        # Add optional parameters
        if config.get("project_name"):
            cmd.extend(["--project-name", config["project_name"]])
        if config.get("log_level"):
            cmd.extend(["--log-level", config["log_level"]])
        if config.get("timeout"):
            cmd.extend(["--timeout", str(config["timeout"])])
        
        # Store execution info
        execution_info = {
            "execution_id": execution_id,
            "workflow_id": workflow_id,
            "status": "starting",
            "start_time": datetime.now().isoformat(),
            "config": config,
            "status_file": str(status_file),
            "execution_dir": str(execution_dir)
        }
        
        # Store in a simple file-based storage (could be enhanced with database)
        executions_file = scripts_path / "workflow_executions.json"
        executions = {}
        if executions_file.exists():
            with open(executions_file, 'r') as f:
                executions = json.load(f)
        
        executions[execution_id] = execution_info
        
        with open(executions_file, 'w') as f:
            json.dump(executions, f, indent=2)
        
        # Start the workflow process
        process = subprocess.Popen(
            cmd,
            cwd=str(scripts_path),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Update execution info with process ID
        execution_info["pid"] = process.pid
        execution_info["status"] = "running"
        executions[execution_id] = execution_info
        
        with open(executions_file, 'w') as f:
            json.dump(executions, f, indent=2)
        
        return {
            "execution_id": execution_id,
            "status": "started",
            "message": f"Workflow '{workflow_id}' started successfully"
        }
    
    except Exception as e:
        logger.error(f"Error starting workflow: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start workflow: {str(e)}")


@app.get("/api/workflows/executions/{execution_id}/status")
async def get_workflow_status(execution_id: str):
    """Get the current status of a workflow execution"""
    try:
        scripts_path = Path.home() / "source" / "repos" / "scripts"
        executions_file = scripts_path / "workflow_executions.json"
        
        if not executions_file.exists():
            raise HTTPException(status_code=404, detail="Execution not found")
        
        with open(executions_file, 'r') as f:
            executions = json.load(f)
        
        if execution_id not in executions:
            raise HTTPException(status_code=404, detail="Execution not found")
        
        execution_info = executions[execution_id]
        
        # Check if process is still running
        import psutil
        pid = execution_info.get("pid")
        if pid:
            try:
                process = psutil.Process(pid)
                if process.is_running():
                    execution_info["status"] = "running"
                else:
                    execution_info["status"] = "completed"
            except psutil.NoSuchProcess:
                execution_info["status"] = "completed"
        
        # Try to get more detailed status from status file if available
        status_file = Path(execution_info.get("status_file", ""))
        if status_file.exists():
            try:
                # Parse status from HTML or look for JSON status file
                status_json_file = status_file.parent / "status.json"
                if status_json_file.exists():
                    with open(status_json_file, 'r') as f:
                        detailed_status = json.load(f)
                    execution_info.update(detailed_status)
            except Exception as e:
                logger.debug(f"Could not read detailed status: {e}")
        
        return execution_info
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting workflow status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get workflow status: {str(e)}")


@app.post("/api/workflows/executions/{execution_id}/stop")
async def stop_workflow(execution_id: str):
    """Stop a running workflow execution"""
    try:
        scripts_path = Path.home() / "source" / "repos" / "scripts"
        executions_file = scripts_path / "workflow_executions.json"
        
        if not executions_file.exists():
            raise HTTPException(status_code=404, detail="Execution not found")
        
        with open(executions_file, 'r') as f:
            executions = json.load(f)
        
        if execution_id not in executions:
            raise HTTPException(status_code=404, detail="Execution not found")
        
        execution_info = executions[execution_id]
        pid = execution_info.get("pid")
        
        if pid:
            try:
                import psutil
                process = psutil.Process(pid)
                if process.is_running():
                    process.terminate()  # Graceful termination
                    try:
                        process.wait(timeout=10)  # Wait up to 10 seconds
                    except psutil.TimeoutExpired:
                        process.kill()  # Force kill if needed
                    
                    execution_info["status"] = "stopped"
                    execution_info["end_time"] = datetime.now().isoformat()
                    executions[execution_id] = execution_info
                    
                    with open(executions_file, 'w') as f:
                        json.dump(executions, f, indent=2)
                    
                    return {"status": "stopped", "message": "Workflow stopped successfully"}
                else:
                    return {"status": "already_stopped", "message": "Workflow was already stopped"}
            except psutil.NoSuchProcess:
                execution_info["status"] = "stopped"
                executions[execution_id] = execution_info
                
                with open(executions_file, 'w') as f:
                    json.dump(executions, f, indent=2)
                
                return {"status": "already_stopped", "message": "Workflow was already stopped"}
        
        return {"status": "not_running", "message": "Workflow is not running"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping workflow: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to stop workflow: {str(e)}")


@app.get("/api/workflows/executions/{execution_id}/status-html")
async def get_workflow_status_html(execution_id: str):
    """Get the HTML status file content for a workflow execution"""
    try:
        scripts_path = Path.home() / "source" / "repos" / "scripts"
        executions_file = scripts_path / "workflow_executions.json"
        
        if not executions_file.exists():
            raise HTTPException(status_code=404, detail="Execution not found")
        
        with open(executions_file, 'r') as f:
            executions = json.load(f)
        
        if execution_id not in executions:
            raise HTTPException(status_code=404, detail="Execution not found")
        
        execution_info = executions[execution_id]
        status_file = Path(execution_info.get("status_file", ""))
        
        if not status_file.exists():
            return {"content": None, "last_modified": None}
        
        # Read the status file
        with open(status_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Get file modification time
        last_modified = status_file.stat().st_mtime
        
        return {
            "content": content,
            "last_modified": last_modified,
            "file_path": str(status_file)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reading workflow status HTML: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to read status HTML: {str(e)}")


# =====================================================================
# WORKFLOW EDITING ENDPOINTS
# =====================================================================

@app.get("/api/workflows/{workflow_id}")
async def get_workflow_definition(workflow_id: str):
    """Get the complete workflow definition including all step files"""
    try:
        scripts_path = Path.home() / "source" / "repos" / "scripts"
        workflow_path = scripts_path / "workflows" / workflow_id
        
        if not workflow_path.exists():
            raise HTTPException(status_code=404, detail=f"Workflow '{workflow_id}' not found")
        
        workflow_yml = workflow_path / "workflow.yml"
        if not workflow_yml.exists():
            raise HTTPException(status_code=404, detail=f"Workflow config not found")
        
        # Load workflow config
        with open(workflow_yml, 'r', encoding='utf-8') as f:
            import yaml
            workflow_config = yaml.safe_load(f)
        
        # Load step files
        steps = []
        step_files = sorted(workflow_path.glob("*.yml"))
        step_files = [f for f in step_files if f.name != "workflow.yml"]
        
        for step_file in step_files:
            with open(step_file, 'r', encoding='utf-8') as f:
                step_config = yaml.safe_load(f)
                step_config["_filename"] = step_file.name
                step_config["_file_path"] = str(step_file)
                steps.append(step_config)
        
        return {
            "workflow_id": workflow_id,
            "config": workflow_config,
            "steps": steps,
            "path": str(workflow_path)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error loading workflow definition: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to load workflow: {str(e)}")


@app.post("/api/workflows/{workflow_id}/steps/{step_position}/insert")
async def insert_workflow_step(workflow_id: str, step_position: int, step_config: dict):
    """Insert a new step at the specified position in the workflow"""
    try:
        scripts_path = Path.home() / "source" / "repos" / "scripts"
        workflow_path = scripts_path / "workflows" / workflow_id
        
        if not workflow_path.exists():
            raise HTTPException(status_code=404, detail=f"Workflow '{workflow_id}' not found")
        
        # Get existing step files
        step_files = sorted(workflow_path.glob("[0-9][0-9]-*.yml"))
        
        # Calculate new step number and shift existing steps
        new_step_num = f"{step_position:02d}"
        new_filename = f"{new_step_num}-{step_config.get('step_id', 'new-step')}.yml"
        
        # Shift existing steps that come after the insertion point
        for step_file in reversed(step_files):
            current_num = int(step_file.name[:2])
            if current_num >= step_position:
                new_num = f"{current_num + 1:02d}"
                new_name = f"{new_num}-{step_file.name[3:]}"
                step_file.rename(workflow_path / new_name)
        
        # Create new step file
        new_step_path = workflow_path / new_filename
        with open(new_step_path, 'w', encoding='utf-8') as f:
            import yaml
            yaml.dump(step_config, f, default_flow_style=False, allow_unicode=True)
        
        # Update next_step references in adjacent steps
        await _update_next_step_references(workflow_path)
        
        return {"message": f"Step inserted successfully at position {step_position}"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error inserting workflow step: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to insert step: {str(e)}")


@app.delete("/api/workflows/{workflow_id}/steps/{step_filename}")
async def remove_workflow_step(workflow_id: str, step_filename: str):
    """Remove a step from the workflow and update step numbering"""
    try:
        scripts_path = Path.home() / "source" / "repos" / "scripts"
        workflow_path = scripts_path / "workflows" / workflow_id
        
        if not workflow_path.exists():
            raise HTTPException(status_code=404, detail=f"Workflow '{workflow_id}' not found")
        
        step_file = workflow_path / step_filename
        if not step_file.exists():
            raise HTTPException(status_code=404, detail=f"Step file '{step_filename}' not found")
        
        # Get the step number being removed
        removed_step_num = int(step_filename[:2])
        
        # Delete the step file
        step_file.unlink()
        
        # Renumber remaining steps
        step_files = sorted(workflow_path.glob("[0-9][0-9]-*.yml"))
        for step_file in step_files:
            current_num = int(step_file.name[:2])
            if current_num > removed_step_num:
                new_num = f"{current_num - 1:02d}"
                new_name = f"{new_num}-{step_file.name[3:]}"
                step_file.rename(workflow_path / new_name)
        
        # Update next_step references in all steps
        await _update_next_step_references(workflow_path)
        
        return {"message": f"Step '{step_filename}' removed successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing workflow step: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to remove step: {str(e)}")


@app.put("/api/workflows/{workflow_id}/steps/{step_filename}")
async def update_workflow_step(workflow_id: str, step_filename: str, step_config: dict):
    """Update an existing workflow step"""
    try:
        scripts_path = Path.home() / "source" / "repos" / "scripts"
        workflow_path = scripts_path / "workflows" / workflow_id
        
        if not workflow_path.exists():
            raise HTTPException(status_code=404, detail=f"Workflow '{workflow_id}' not found")
        
        step_file = workflow_path / step_filename
        if not step_file.exists():
            raise HTTPException(status_code=404, detail=f"Step file '{step_filename}' not found")
        
        # Update step file
        with open(step_file, 'w', encoding='utf-8') as f:
            import yaml
            yaml.dump(step_config, f, default_flow_style=False, allow_unicode=True)
        
        # Update next_step references if step_id changed
        await _update_next_step_references(workflow_path)
        
        return {"message": f"Step '{step_filename}' updated successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating workflow step: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update step: {str(e)}")


async def _update_next_step_references(workflow_path: Path):
    """Update next_step references in all workflow steps to maintain proper sequencing"""
    try:
        step_files = sorted(workflow_path.glob("[0-9][0-9]-*.yml"))
        
        # Build map of step IDs to their positions
        step_id_map = {}
        step_configs = []
        
        for i, step_file in enumerate(step_files):
            with open(step_file, 'r', encoding='utf-8') as f:
                import yaml
                config = yaml.safe_load(f)
                step_configs.append((step_file, config))
                step_id_map[config.get('step_id')] = i
        
        # Update next_step references
        for i, (step_file, config) in enumerate(step_configs):
            if 'next_step' in config:
                # Update next_step to point to the next sequential step
                if i < len(step_configs) - 1:
                    next_config = step_configs[i + 1][1]
                    config['next_step'] = {
                        'default': next_config.get('step_id')
                    }
                else:
                    # Last step - remove next_step
                    if 'next_step' in config:
                        del config['next_step']
                
                # Write updated config back
                with open(step_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    except Exception as e:
        logger.error(f"Error updating next_step references: {e}")
        raise


@app.get("/api/git-activity/{project_name}")
async def get_git_activity(project_name: str):
    """Get git activity for a project over the last 2 weeks"""
    try:
        import subprocess
        from datetime import datetime, timedelta
        
        project_path = TodoManager.get_project_path(project_name).parent
        
        if not project_path.exists():
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Check if it's a git repository
        git_dir = project_path / ".git"
        if not git_dir.exists():
            return {
                "statistics": {
                    "total_commits": 0,
                    "total_additions": 0,
                    "total_deletions": 0,
                    "files_modified_count": 0,
                    "file_changes": {}
                },
                "commits": [],
                "message": "Not a git repository"
            }
        
        # Calculate date 2 weeks ago
        two_weeks_ago = (datetime.now() - timedelta(days=14)).strftime('%Y-%m-%d')
        
        # Get commit log
        git_log_cmd = [
            'git', 'log', 
            f'--since={two_weeks_ago}',
            '--pretty=format:%H|%an|%ad|%s',
            '--date=iso',
            '--no-merges'
        ]
        
        git_log_result = subprocess.run(
            git_log_cmd, 
            cwd=project_path, 
            capture_output=True, 
            text=True
        )
        
        commits = []
        if git_log_result.returncode == 0 and git_log_result.stdout.strip():
            for line in git_log_result.stdout.strip().split('\n'):
                parts = line.split('|', 3)
                if len(parts) >= 4:
                    hash_val, author, date, message = parts
                    commits.append({
                        "hash": hash_val,
                        "author": author,
                        "date": date,
                        "message": message
                    })
        
        # Get detailed stats
        git_stats_cmd = [
            'git', 'log', 
            f'--since={two_weeks_ago}',
            '--numstat',
            '--pretty=format:',
            '--no-merges'
        ]
        
        git_stats_result = subprocess.run(
            git_stats_cmd,
            cwd=project_path,
            capture_output=True,
            text=True
        )
        
        total_additions = 0
        total_deletions = 0
        file_changes = {}
        
        if git_stats_result.returncode == 0 and git_stats_result.stdout.strip():
            for line in git_stats_result.stdout.strip().split('\n'):
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 3:
                        additions_str, deletions_str, filename = parts
                        
                        try:
                            additions = int(additions_str) if additions_str != '-' else 0
                            deletions = int(deletions_str) if deletions_str != '-' else 0
                            
                            total_additions += additions
                            total_deletions += deletions
                            
                            if filename not in file_changes:
                                file_changes[filename] = {"additions": 0, "deletions": 0}
                            
                            file_changes[filename]["additions"] += additions
                            file_changes[filename]["deletions"] += deletions
                            
                        except ValueError:
                            continue
        
        return {
            "statistics": {
                "total_commits": len(commits),
                "total_additions": total_additions,
                "total_deletions": total_deletions,
                "files_modified_count": len(file_changes),
                "file_changes": file_changes
            },
            "commits": commits,
            "project_path": str(project_path)
        }
    
    except subprocess.CalledProcessError as e:
        logger.error(f"Git command failed: {e}")
        return {
            "statistics": {
                "total_commits": 0,
                "total_additions": 0,
                "total_deletions": 0,
                "files_modified_count": 0,
                "file_changes": {}
            },
            "commits": [],
            "error": "Git command failed"
        }
    except Exception as e:
        logger.error(f"Error getting git activity: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get git activity: {str(e)}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Todo Management API Server")
    parser.add_argument("--host", default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")
    
    args = parser.parse_args()
    
    print(f"Starting Todo API server on http://{args.host}:{args.port}")
    print(f"API Documentation: http://{args.host}:{args.port}/docs")
    
    uvicorn.run(
        "app:app",
        host=args.host,
        port=args.port,
        reload=args.reload
    )