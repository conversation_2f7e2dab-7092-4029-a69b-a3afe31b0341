<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo Management System</title>
    <link rel="stylesheet" href="css/style.css">
    <meta name="description" content="Todo Management System - Project Dashboard">
</head>
<body>
    <header class="main-header">
        <div class="container">
            <h1><a href="index.html" style="text-decoration: none; color: inherit;">📋 Todo Management System</a></h1>
            <nav class="main-nav" role="navigation" aria-label="Main navigation">
                <ul>
                    <li><a href="today.html">Today</a></li>
                    <li><a href="weekly.html">Weekly</a></li>
                    <li><a href="workflow.html">Workflows</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- Loading State -->
            <div id="loading-state" class="loading-state" aria-live="polite">
                <div class="loading-spinner"></div>
                <p>Loading projects...</p>
            </div>

            <!-- Error State -->
            <div id="error-state" class="error-state hidden" role="alert" aria-live="assertive">
                <div class="error-icon">⚠️</div>
                <h2>Unable to Load Projects</h2>
                <p id="error-message">Please check if the API server is running.</p>
                <button id="retry-btn" class="btn btn-primary">Retry</button>
                <details class="error-details">
                    <summary>Troubleshooting Steps</summary>
                    <ol>
                        <li>Ensure the API server is running on <code>http://localhost:8000</code></li>
                        <li>Check that directories are whitelisted via the <a href="api-test.html">API Test page</a></li>
                        <li>Verify that projects have the correct structure: <code>project-progress/to-do-lists/todos.yml</code></li>
                    </ol>
                </details>
            </div>

            <!-- Dashboard Content -->
            <div id="dashboard-content" class="dashboard-content hidden">
                <!-- Summary Statistics -->
                <section class="dashboard-summary" aria-labelledby="summary-heading">
                    <h2 id="summary-heading" class="sr-only">Project Summary</h2>
                    <div class="summary-grid">
                        <div class="summary-card">
                            <div class="summary-icon">📊</div>
                            <div class="summary-info">
                                <span id="total-projects" class="summary-number">0</span>
                                <span class="summary-label">Total Projects</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="summary-icon">✅</div>
                            <div class="summary-info">
                                <span id="total-todos" class="summary-number">0</span>
                                <span class="summary-label">Total Todos</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="summary-icon">🎯</div>
                            <div class="summary-info">
                                <span id="completion-rate" class="summary-number">0%</span>
                                <span class="summary-label">Completion Rate</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="summary-icon">⚡</div>
                            <div class="summary-info">
                                <span id="pending-todos" class="summary-number">0</span>
                                <span class="summary-label">Pending</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Filter and Search -->
                <section class="dashboard-controls" aria-labelledby="controls-heading">
                    <h2 id="controls-heading" class="sr-only">Dashboard Controls</h2>
                    <div class="controls-bar">
                        <div class="search-container">
                            <label for="project-search" class="sr-only">Search projects</label>
                            <input 
                                type="search" 
                                id="project-search" 
                                placeholder="Search projects..." 
                                aria-describedby="search-help"
                            >
                            <span id="search-help" class="sr-only">Search by project name</span>
                        </div>
                        <div class="filter-container">
                            <label for="status-filter">Filter by status:</label>
                            <select id="status-filter" aria-describedby="filter-help">
                                <option value="all">All Projects</option>
                                <option value="active">Active (has pending todos)</option>
                                <option value="completed">Completed (all todos done)</option>
                            </select>
                            <span id="filter-help" class="sr-only">Filter projects by completion status</span>
                        </div>
                        <div class="project-type-filters">
                            <span class="filter-label">Show project types:</span>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="show-work-projects" checked aria-describedby="work-help">
                                    <span class="checkbox-text">💼 Work</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="show-home-projects" aria-describedby="home-help">
                                    <span class="checkbox-text">🏠 Home</span>
                                </label>
                            </div>
                            <span id="work-help" class="sr-only">Show work projects</span>
                            <span id="home-help" class="sr-only">Show home projects</span>
                        </div>
                        <button id="refresh-btn" class="btn btn-secondary" aria-label="Refresh project data">
                            <span aria-hidden="true">🔄</span> Refresh
                        </button>
                    </div>
                </section>

                <!-- Projects Grid -->
                <section class="projects-section" aria-labelledby="projects-heading">
                    <div class="projects-header">
                        <h2 id="projects-heading">Projects</h2>
                        <div class="projects-actions">
                            <button id="add-existing-project-btn" class="btn btn-secondary">
                                <span aria-hidden="true">📁</span> Add Existing Project
                            </button>
                            <button id="add-new-project-btn" class="btn btn-primary">
                                <span aria-hidden="true">➕</span> Add New Project
                            </button>
                        </div>
                    </div>
                    <div id="projects-grid" class="projects-grid" role="list">
                        <!-- Project cards will be dynamically inserted here -->
                    </div>
                    
                    <!-- Empty State -->
                    <div id="empty-state" class="empty-state hidden">
                        <div class="empty-icon">📁</div>
                        <h3>No Projects Found</h3>
                        <p>No projects with todo lists were discovered.</p>
                        <details class="empty-details">
                            <summary>How to add projects</summary>
                            <p>Projects are automatically discovered when they contain the following structure:</p>
                            <code>~/source/repos/ProjectName/project-progress/to-do-lists/todos.yml</code>
                            <p>Make sure your directories are whitelisted via the <a href="api-test.html">API Test page</a>.</p>
                        </details>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2025 Todo Management System | <a href="index.html">Back to Dashboard</a> | <a href="api-test.html">API Test</a> | <a href="today.html">Today</a> | <a href="#" id="about-link">About</a></p>
        </div>
    </footer>

    <!-- About Modal -->
    <div id="about-modal" class="modal hidden" role="dialog" aria-labelledby="about-title" aria-hidden="true">
        <div class="modal-content">
            <header class="modal-header">
                <h2 id="about-title">About Todo Management System</h2>
                <button class="modal-close" aria-label="Close dialog">&times;</button>
            </header>
            <div class="modal-body">
                <p>A comprehensive todo management system with FastAPI backend and dynamic web interface.</p>
                <p><strong>Features:</strong></p>
                <ul>
                    <li>Project discovery and management</li>
                    <li>Real-time todo operations</li>
                    <li>Responsive design</li>
                    <li>Accessibility support</li>
                </ul>
                <p><strong>Version:</strong> 1.0.0</p>
            </div>
        </div>
    </div>

    <!-- Add Existing Project Modal -->
    <div id="existing-project-modal" class="modal hidden" role="dialog" aria-labelledby="existing-project-title" aria-hidden="true">
        <div class="modal-content">
            <header class="modal-header">
                <h2 id="existing-project-title">Add Existing Project</h2>
                <button class="modal-close" aria-label="Close dialog">&times;</button>
            </header>
            <div class="modal-body">
                <p>Select an existing project from ~/source/repos to add to your todo management system:</p>
                <div class="form-field">
                    <label for="existing-project-type-select">Default Project Type for New Projects:</label>
                    <select id="existing-project-type-select">
                        <option value="Work" selected>💼 Work</option>
                        <option value="Home">🏠 Home</option>
                    </select>
                    <small>This will be the default type when enabling todo management on unmanaged projects</small>
                </div>
                <div id="existing-projects-loading" class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>Scanning ~/source/repos...</p>
                </div>
                <div id="existing-projects-list" class="projects-list hidden">
                    <!-- Projects will be populated here -->
                </div>
                <div id="existing-projects-empty" class="empty-state hidden">
                    <p>No additional projects found in ~/source/repos</p>
                    <small>All projects are already managed or no projects were found.</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Add New Project Modal -->
    <div id="new-project-modal" class="modal hidden" role="dialog" aria-labelledby="new-project-title" aria-hidden="true">
        <div class="modal-content">
            <header class="modal-header">
                <h2 id="new-project-title">Add New Project</h2>
                <button class="modal-close" aria-label="Close dialog">&times;</button>
            </header>
            <div class="modal-body">
                <form id="new-project-form">
                    <div class="form-field">
                        <label for="project-name-input">Project Name *</label>
                        <input type="text" id="project-name-input" required placeholder="Enter project name">
                        <small>This will create a directory: ~/source/repos/ProjectName</small>
                    </div>
                    <div class="form-field">
                        <label for="project-description-input">Description</label>
                        <textarea id="project-description-input" placeholder="Optional project description" rows="3"></textarea>
                    </div>
                    <div class="form-field">
                        <label for="project-type-input">Project Type</label>
                        <select id="project-type-input">
                            <option value="Work" selected>💼 Work</option>
                            <option value="Home">🏠 Home</option>
                        </select>
                        <small>Work projects appear with standard styling, Home projects have a light teal background</small>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="cancel-new-project-btn">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Project</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Project Modal -->
    <div id="edit-project-modal" class="modal hidden" role="dialog" aria-labelledby="edit-project-title" aria-hidden="true">
        <div class="modal-content">
            <header class="modal-header">
                <h2 id="edit-project-title">Edit Project</h2>
                <button class="modal-close" aria-label="Close dialog">&times;</button>
            </header>
            <div class="modal-body">
                <form id="edit-project-form">
                    <div class="form-field">
                        <label for="edit-project-name-input">Project Name</label>
                        <input type="text" id="edit-project-name-input" readonly disabled>
                        <small>Project name cannot be changed</small>
                    </div>
                    <div class="form-field">
                        <label for="edit-project-display-name-input">Display Name</label>
                        <input type="text" id="edit-project-display-name-input" maxlength="100" placeholder="Optional display name">
                        <small>Override the folder name with a more readable title</small>
                    </div>
                    <div class="form-field">
                        <label for="edit-project-type-input">Project Type</label>
                        <select id="edit-project-type-input">
                            <option value="Work">💼 Work</option>
                            <option value="Home">🏠 Home</option>
                        </select>
                        <small>Changes the visual appearance of the project card</small>
                    </div>
                    <div class="form-field">
                        <label for="edit-project-urgency-input">Urgency</label>
                        <select id="edit-project-urgency-input">
                            <option value="High">🔴 High</option>
                            <option value="Normal" selected>🟡 Normal</option>
                            <option value="Low">🟢 Low</option>
                        </select>
                        <small>How time-sensitive is this project? (Covey Quadrant System)</small>
                    </div>
                    <div class="form-field">
                        <label for="edit-project-importance-input">Importance</label>
                        <select id="edit-project-importance-input">
                            <option value="High">⭐ High</option>
                            <option value="Normal" selected>📌 Normal</option>
                            <option value="Low">📎 Low</option>
                        </select>
                        <small>How critical is this project to your goals? (Covey Quadrant System)</small>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="cancel-edit-project-btn">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/app.js"></script>
    
    <!-- Debug logging -->
    <script>
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
            document.getElementById('loading-state').style.display = 'none';
            const errorState = document.getElementById('error-state');
            errorState.style.display = 'block';
            errorState.classList.remove('hidden');
            document.getElementById('error-message').textContent = 'JavaScript error: ' + e.message;
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
            document.getElementById('loading-state').style.display = 'none';
            const errorState = document.getElementById('error-state');
            errorState.style.display = 'block';
            errorState.classList.remove('hidden');
            document.getElementById('error-message').textContent = 'Promise error: ' + e.reason;
        });
    </script>
</body>
</html>