# Start Todo API Server
Write-Host "Starting Todo API Server..." -ForegroundColor Cyan
Write-Host "Directory: $(Get-Location)" -ForegroundColor Gray
Write-Host ""

try {
    python app.py
} catch {
    Write-Host "Error starting API server: $_" -ForegroundColor Red
} finally {
    Write-Host ""
    Write-Host "API Server started on http://localhost:8000" -ForegroundColor Green
    Write-Host "API Documentation: http://localhost:8000/docs" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Press any key to close this tab..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}