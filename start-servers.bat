@echo off
echo Starting Todo Management System...
echo.

echo Starting API Server on port 8000...
start "Todo API Server" /D "C:\Users\<USER>\source\repos\todo-lists\todo-api" python app.py

echo Waiting for API server to start...
timeout /t 3 /nobreak >nul

echo Starting Web Server on port 8080...
start "Todo Web Server" /D "C:\Users\<USER>\source\repos\todo-lists\todo-web" python -m http.server 8080

echo.
echo Both servers are starting...
echo.
echo API Server: http://localhost:8000
echo Web Interface: http://localhost:8080
echo API Documentation: http://localhost:8000/docs
echo.
echo Press any key to continue...
pause