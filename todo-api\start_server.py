#!/usr/bin/env python3
"""
Todo API Server Startup Script

Quick start script for the Todo Management API with automatic setup.
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages."""
    print("Checking/Installing requirements...")
    try:
        # Try to import required packages first
        import fastapi, uvicorn, pydantic, yaml
        print("All required packages are already available")
    except ImportError:
        print("Installing missing packages...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("Requirements installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"Failed to install requirements: {e}")
            print("You may need to install manually: pip install fastapi uvicorn pydantic pyyaml python-multipart")
            sys.exit(1)

def check_and_setup_whitelist():
    """Check if base directory needs to be whitelisted."""
    base_path = Path.home() / "source" / "repos"
    if not base_path.exists():
        print(f"Warning: Base path {base_path} does not exist")
        print("   You may need to create it or update the configuration")
    else:
        print(f"Base path exists: {base_path}")

def main():
    """Main startup function."""
    print("Starting Todo Management API Server")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("app.py").exists():
        print("app.py not found. Please run this script from the todo-api directory.")
        sys.exit(1)
    
    # Install requirements
    install_requirements()
    
    # Check setup
    check_and_setup_whitelist()
    
    print("\n" + "=" * 50)
    print("Starting API server...")
    print("API Documentation will be available at: http://localhost:8000/docs")
    print("Alternative docs at: http://localhost:8000/redoc")
    print("Health check at: http://localhost:8000/api/v1/health")
    print("\nTo stop the server, press Ctrl+C")
    print("=" * 50 + "\n")
    
    # Start the server
    try:
        subprocess.run([sys.executable, "app.py", "--reload"])
    except KeyboardInterrupt:
        print("\n\nServer stopped. Goodbye!")

if __name__ == "__main__":
    main()