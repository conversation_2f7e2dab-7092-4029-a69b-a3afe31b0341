# Todo Management System

A comprehensive todo management system with FastAPI backend and dynamic web interface.

## Project Structure

```
todo-lists/
├── todo-api/           # FastAPI backend
│   ├── app.py         # Main API application
│   ├── client.py      # Python client library
│   ├── requirements.txt
│   └── ...
└── todo-web/          # Web interface
    ├── index.html     # Main dashboard
    ├── project.html   # Project detail page
    ├── api-test.html  # API testing interface
    ├── css/
    │   └── style.css
    ├── js/
    │   ├── app.js     # Main application logic
    │   ├── api.js     # API interaction layer
    │   └── utils.js   # Utility functions
    └── assets/
```

## Quick Start

### 1. Start the API Server
```bash
cd todo-api
python start_server.py
```

### 2. Open Web Interface
Open `todo-web/index.html` in your browser or serve via local web server:
```bash
cd todo-web
python -m http.server 8080
```

### 3. First Time Setup
1. Navigate to the API test page to whitelist your directories
2. Add your source/repos directory to enable project discovery
3. Refresh the main dashboard to see your projects

## Features

- **Project Discovery**: Automatically scans ~/source/repos for valid project structures
- **Real-time Todo Management**: Create, edit, delete, and mark todos as complete
- **Responsive Design**: Works on desktop and mobile devices
- **Accessibility**: Full keyboard navigation and screen reader support
- **Nokia Theme**: Consistent styling with existing workflow pages

## API Endpoints

- `GET /api/v1/projects` - List all projects
- `GET /api/v1/projects/{project}/todos` - Get todos for a project
- `POST /api/v1/projects/{project}/todos` - Create new todo
- `PUT /api/v1/projects/{project}/todos/{id}` - Update todo
- `DELETE /api/v1/projects/{project}/todos/{id}` - Delete todo

## Development

The web interface uses vanilla JavaScript with no external dependencies for core functionality. All API interactions are handled through the `api.js` module.

For development, use the API test page to verify backend connectivity and test CRUD operations.