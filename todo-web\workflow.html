<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workflow Manager - Todo Management System</title>
    <link rel="stylesheet" href="css/style.css">
    <meta name="description" content="Workflow Manager - Run and manage project workflows">
    
    <!-- External dependencies -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-yaml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-python.min.js"></script>
    
    <style>
        /* Workflow-specific styles */
        :root {
            --workflow-primary: #2c3e50;
            --workflow-secondary: #3498db;
            --workflow-success: #27ae60;
            --workflow-warning: #f39c12;
            --workflow-error: #e74c3c;
            --workflow-bg: #f8fafc;
            --workflow-card: #ffffff;
            --workflow-border: #e3e8ef;
            --workflow-text: #2c3e50;
            --workflow-text-muted: #6c757d;
        }

        .main-content {
            padding: 1rem 0 2rem 0;
        }

        .workflow-header {
            background: linear-gradient(135deg, var(--workflow-primary), var(--workflow-secondary));
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 1rem 1rem;
        }

        .workflow-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .workflow-header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .workflow-header p {
            opacity: 0.9;
            margin: 0;
        }

        .workflow-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Workflow Cards */
        .workflow-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .workflow-card {
            background: var(--workflow-card);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--workflow-border);
        }

        .workflow-card h2 {
            color: var(--workflow-primary);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .workflow-card h2 .icon {
            font-size: 1.5rem;
        }

        /* Available Workflows */
        .available-workflows {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .workflow-item {
            background: var(--workflow-bg);
            border: 1px solid var(--workflow-border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .workflow-item:hover {
            border-color: var(--workflow-secondary);
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);
        }

        .workflow-item.selected {
            border-color: var(--workflow-secondary);
            background: rgba(52, 152, 219, 0.05);
        }

        .workflow-item h3 {
            color: var(--workflow-primary);
            margin-bottom: 0.5rem;
        }

        .workflow-item .description {
            color: var(--workflow-text-muted);
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .workflow-item .meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: var(--workflow-text-muted);
        }

        .workflow-item .steps-count {
            background: var(--workflow-secondary);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
            font-weight: 500;
        }

        /* Configuration Section */
        .config-section {
            display: none;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--workflow-border);
        }

        .config-section.active {
            display: block;
        }

        .config-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            font-weight: 600;
            color: var(--workflow-text);
            margin-bottom: 0.5rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 0.75rem;
            border: 1px solid var(--workflow-border);
            border-radius: 0.5rem;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--workflow-secondary);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-group small {
            color: var(--workflow-text-muted);
            margin-top: 0.25rem;
        }

        /* Execution Status */
        .execution-status {
            background: var(--workflow-bg);
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-top: 1.5rem;
            display: none;
        }

        .execution-status.active {
            display: block;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .status-indicator.idle {
            background: var(--workflow-border);
            color: var(--workflow-text-muted);
        }

        .status-indicator.running {
            background: var(--workflow-warning);
            color: white;
        }

        .status-indicator.completed {
            background: var(--workflow-success);
            color: white;
        }

        .status-indicator.error {
            background: var(--workflow-error);
            color: white;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: var(--workflow-border);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--workflow-secondary), var(--workflow-success));
            border-radius: 3px;
            transition: width 0.5s ease;
            width: 0%;
        }

        .step-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .step-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            background: var(--workflow-card);
            border-radius: 0.5rem;
            border-left: 4px solid var(--workflow-border);
        }

        .step-item.pending {
            border-left-color: var(--workflow-border);
        }

        .step-item.running {
            border-left-color: var(--workflow-warning);
            background: rgba(243, 156, 18, 0.05);
        }

        .step-item.completed {
            border-left-color: var(--workflow-success);
            background: rgba(39, 174, 96, 0.05);
        }

        .step-item.error {
            border-left-color: var(--workflow-error);
            background: rgba(231, 76, 60, 0.05);
        }

        .step-icon {
            font-size: 1.2rem;
        }

        .step-info {
            flex: 1;
        }

        .step-name {
            font-weight: 600;
            color: var(--workflow-text);
        }

        .step-description {
            font-size: 0.8rem;
            color: var(--workflow-text-muted);
        }

        .step-status {
            font-size: 0.8rem;
            font-weight: 500;
        }

        /* Log Output */
        .log-output {
            background: #1a1a1a;
            border-radius: 0.5rem;
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            color: #e0e0e0;
            margin-top: 1rem;
            display: none;
        }

        .log-output.active {
            display: block;
        }

        .log-line {
            margin-bottom: 0.25rem;
            word-break: break-all;
        }

        .log-line.info {
            color: #4fc3f7;
        }

        .log-line.warning {
            color: #ffb74d;
        }

        .log-line.error {
            color: #f48fb1;
        }

        .log-line.success {
            color: #81c784;
        }

        /* Buttons */
        .btn-primary {
            background: var(--workflow-secondary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover:not(:disabled) {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .btn-primary:disabled {
            background: var(--workflow-text-muted);
            cursor: not-allowed;
        }

        .btn-secondary {
            background: var(--workflow-border);
            color: var(--workflow-text);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #d5d8dc;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: var(--workflow-error);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-danger:hover:not(:disabled) {
            background: #c0392b;
        }

        /* Workflow Editor Styles */
        .editor-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            align-items: center;
        }

        .workflow-select {
            flex: 1;
            padding: 0.75rem;
            border: 2px solid var(--workflow-border);
            border-radius: 8px;
            background: white;
            font-size: 14px;
        }

        .workflow-steps-editor {
            margin-top: 1.5rem;
        }

        .editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid var(--workflow-border);
        }

        .editor-actions {
            display: flex;
            gap: 0.5rem;
        }

        .steps-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .step-editor-item {
            background: white;
            border: 2px solid var(--workflow-border);
            border-radius: 12px;
            padding: 1rem;
            position: relative;
            transition: all 0.3s ease;
        }

        .step-editor-item:hover {
            border-color: var(--workflow-secondary);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);
        }

        .step-editor-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .step-info h4 {
            margin: 0 0 0.25rem 0;
            color: var(--workflow-text);
            font-size: 1.1rem;
        }

        .step-info .step-id {
            font-size: 0.85rem;
            color: var(--workflow-text-muted);
            font-family: monospace;
        }

        .step-actions {
            display: flex;
            gap: 0.5rem;
            flex-shrink: 0;
        }

        .step-actions button {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .btn-edit {
            background: var(--workflow-secondary);
            color: white;
        }

        .btn-edit:hover {
            background: #2980b9;
        }

        .btn-remove {
            background: var(--workflow-error);
            color: white;
        }

        .btn-remove:hover {
            background: #c0392b;
        }

        .btn-insert {
            background: var(--workflow-success);
            color: white;
        }

        .btn-insert:hover {
            background: #219a52;
        }

        .btn-success {
            background: var(--workflow-success);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .btn-success:hover {
            background: #219a52;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--workflow-border);
        }

        .modal-header h3 {
            margin: 0;
            color: var(--workflow-text);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--workflow-text-muted);
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: var(--workflow-error);
        }

        .modal-body {
            padding: 1.5rem;
        }

        .step-form .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .step-form .form-group {
            margin-bottom: 1rem;
        }

        .step-form label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--workflow-text);
        }

        .step-form input,
        .step-form textarea,
        .step-form select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--workflow-border);
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .step-form input:focus,
        .step-form textarea:focus,
        .step-form select:focus {
            outline: none;
            border-color: var(--workflow-secondary);
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
            padding: 1.5rem;
            border-top: 1px solid var(--workflow-border);
        }

        /* Tab Navigation Styles */
        .tab-navigation {
            display: flex;
            border-bottom: 2px solid var(--workflow-border);
            margin-bottom: 1.5rem;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 0.75rem 1.5rem;
            font-size: 14px;
            font-weight: 600;
            color: var(--workflow-text-muted);
            border-bottom: 2px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tab-btn:hover {
            color: var(--workflow-secondary);
        }

        .tab-btn.active {
            color: var(--workflow-secondary);
            border-bottom-color: var(--workflow-secondary);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .help-text {
            font-size: 13px;
            color: var(--workflow-text-muted);
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }

        .form-help {
            font-size: 12px;
            color: var(--workflow-text-muted);
            line-height: 1.3;
            margin-top: 0.5rem;
        }

        .form-help code {
            background: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 11px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .workflow-header .container {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .config-form {
                grid-template-columns: 1fr;
            }

            .workflow-actions {
                flex-direction: column;
                width: 100%;
            }

            .editor-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .editor-header {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }

            .step-form .form-row {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                margin: 1rem;
            }
        }

        /* Animation */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* Loading states */
        .loading {
            display: none;
            align-items: center;
            gap: 0.5rem;
            color: var(--workflow-text-muted);
        }

        .loading.active {
            display: flex;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid var(--workflow-border);
            border-top: 2px solid var(--workflow-secondary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <h1><a href="index.html" style="text-decoration: none; color: inherit;">📋 Todo Management System</a></h1>
            <nav class="main-nav" role="navigation" aria-label="Main navigation">
                <ul>
                    <li><a href="today.html">Today</a></li>
                    <li><a href="weekly.html">Weekly</a></li>
                    <li><a href="workflow.html" aria-current="page">Workflows</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- Loading State -->
            <div id="loading-state" class="loading-state hidden">
                <div class="loading-spinner"></div>
                <p>Loading workflow information...</p>
            </div>

            <!-- Error State -->
            <div id="error-state" class="error-state hidden">
                <div class="error-icon">⚠️</div>
                <h3>Error Loading Workflows</h3>
                <p id="error-message">Failed to load workflow information</p>
                <button id="retry-btn" class="btn btn-primary">Retry</button>
            </div>

            <!-- Workflow Content -->
            <div id="workflow-content" class="workflow-content hidden">
                <!-- Page Header -->
                <section class="page-header">
                    <div class="page-header-content">
                        <h2>🔧 Workflow Manager</h2>
                        <p>Run and monitor automated workflows for your projects</p>
                    </div>
                    <div class="page-header-actions">
                        <button id="create-new-workflow" class="btn btn-primary">
                            ➕ Create New Workflow
                        </button>
                        <button id="refresh-workflows" class="btn btn-secondary">
                            🔄 Refresh
                        </button>
                    </div>
                </section>
                
                <div class="workflow-grid">
                    <!-- Available Workflows -->
                    <div class="workflow-card">
                        <h2>
                            <span class="icon">📋</span>
                            Available Workflows
                        </h2>
                        <p>Select a workflow to configure and run on your project</p>
                        
                        <div class="loading" id="workflows-loading">
                            <div class="spinner"></div>
                            <span>Loading workflows...</span>
                        </div>

                        <div id="available-workflows" class="available-workflows">
                            <!-- Workflows will be populated here -->
                        </div>

                        <!-- Configuration Section -->
                        <div id="config-section" class="config-section">
                            <h3>Configuration</h3>
                            <form id="workflow-config-form" class="config-form">
                                <div class="form-group">
                                    <label for="project-path">Project Path</label>
                                    <input type="text" id="project-path" name="project-path" placeholder="/path/to/project">
                                    <small>Path to the project directory</small>
                                </div>

                                <div class="form-group">
                                    <label for="project-name">Project Name</label>
                                    <input type="text" id="project-name" name="project-name" placeholder="MyProject">
                                    <small>Display name for the project</small>
                                </div>

                                <div class="form-group">
                                    <label for="log-level">Log Level</label>
                                    <select id="log-level" name="log-level">
                                        <option value="DEBUG">Debug</option>
                                        <option value="INFO" selected>Info</option>
                                        <option value="WARNING">Warning</option>
                                        <option value="ERROR">Error</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="timeout">Timeout (seconds)</label>
                                    <input type="number" id="timeout" name="timeout" value="300" min="60" max="3600">
                                    <small>Maximum execution time per step</small>
                                </div>

                                <div class="form-group full-width">
                                    <label for="additional-config">Additional Configuration</label>
                                    <textarea id="additional-config" name="additional-config" placeholder="key: value&#10;another_key: another_value"></textarea>
                                    <small>Additional YAML configuration (optional)</small>
                                </div>
                            </form>

                            <div class="workflow-actions">
                                <button id="start-workflow" class="btn-primary" disabled>
                                    🚀 Start Workflow
                                </button>
                                <button id="stop-workflow" class="btn-danger" disabled>
                                    🛑 Stop Workflow
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Execution Status -->
                    <div class="workflow-card">
                        <h2>
                            <span class="icon">⚡</span>
                            Execution Status
                        </h2>
                        
                        <div id="execution-status" class="execution-status">
                            <div class="status-header">
                                <div class="status-indicator idle" id="status-indicator">
                                    <span id="status-icon">⏸️</span>
                                    <span id="status-text">Idle</span>
                                </div>
                                <button id="toggle-logs" class="btn-secondary">
                                    📜 Show Logs
                                </button>
                            </div>

                            <div class="progress-bar">
                                <div class="progress-fill" id="progress-fill"></div>
                            </div>

                            <div class="step-list" id="step-list">
                                <!-- Steps will be populated here -->
                            </div>

                            <div class="log-output" id="log-output">
                                <!-- Log entries will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Workflow Editor -->
                    <div class="workflow-card">
                        <h2>
                            <span class="icon">✏️</span>
                            Workflow Editor
                        </h2>
                        <p>Edit workflow steps - insert, remove, or modify existing steps</p>
                        
                        <!-- Editor Controls -->
                        <div class="editor-controls">
                            <select id="workflow-select-editor" class="workflow-select">
                                <option value="">Select a workflow to edit...</option>
                            </select>
                            <button id="load-workflow-editor" class="btn-secondary" disabled>
                                📂 Load Workflow
                            </button>
                        </div>

                        <!-- Workflow Steps Editor -->
                        <div id="workflow-steps-editor" class="workflow-steps-editor hidden">
                            <div class="editor-header">
                                <h3 id="editing-workflow-name">Editing: Workflow Name</h3>
                                <div class="editor-actions">
                                    <button id="add-step-btn" class="btn-success">
                                        ➕ Add Step
                                    </button>
                                    <button id="save-workflow-btn" class="btn-primary">
                                        💾 Save Changes
                                    </button>
                                </div>
                            </div>
                            
                            <div id="steps-container" class="steps-container">
                                <!-- Editable steps will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step Editor Modal -->
                <div id="step-editor-modal" class="modal hidden">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 id="modal-title">Edit Step</h3>
                            <button id="close-modal" class="close-btn">×</button>
                        </div>
                        <div class="modal-body">
                            <!-- Tab Navigation -->
                            <div class="tab-navigation">
                                <button type="button" class="tab-btn active" data-tab="basic">Basic Info</button>
                                <button type="button" class="tab-btn" data-tab="dependencies">Dependencies</button>
                                <button type="button" class="tab-btn" data-tab="parameters">Parameters</button>
                            </div>

                            <form id="step-editor-form" class="step-form">
                                <!-- Basic Info Tab -->
                                <div class="tab-content active" id="tab-basic">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="step-id">Step ID</label>
                                            <input type="text" id="step-id" name="step_id" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="step-title">Title</label>
                                            <input type="text" id="step-title" name="title" required>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="step-description">Description</label>
                                        <textarea id="step-description" name="description" rows="3"></textarea>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="step-version">Version</label>
                                            <input type="text" id="step-version" name="version" value="1.0">
                                        </div>
                                        <div class="form-group">
                                            <label for="step-timeout">Timeout (seconds)</label>
                                            <input type="number" id="step-timeout" name="timeout" value="300" min="10">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="step-script-path">Script Path</label>
                                        <input type="text" id="step-script-path" name="script_path" placeholder="~/source/repos/scripts/workflows/...">
                                    </div>
                                </div>

                                <!-- Dependencies Tab -->
                                <div class="tab-content" id="tab-dependencies">
                                    <div class="form-group">
                                        <label for="step-dependencies">Dependencies (JSON)</label>
                                        <p class="help-text">Define which steps must complete before this step can run. Leave empty for no dependencies.</p>
                                        <textarea id="step-dependencies" name="dependencies" rows="8" placeholder='[
  {
    "step_id": "previous-step",
    "output_key": "result"
  }
]'></textarea>
                                        <small class="form-help">
                                            <strong>Example:</strong><br>
                                            <code>[{"step_id": "directory-analysis", "output_key": "directory_structure"}]</code>
                                        </small>
                                    </div>
                                </div>

                                <!-- Parameters Tab -->
                                <div class="tab-content" id="tab-parameters">
                                    <div class="form-group">
                                        <label for="step-parameters">Input Parameters (JSON)</label>
                                        <p class="help-text">Define the input parameters this step accepts. Use template variables like {{working_directory}}.</p>
                                        <textarea id="step-parameters" name="parameters" rows="10" placeholder='[
  {
    "name": "working_directory",
    "required": true,
    "description": "Path to the working directory"
  },
  {
    "name": "project_name", 
    "required": true,
    "description": "Name of the project"
  }
]'></textarea>
                                        <small class="form-help">
                                            <strong>Template Variables:</strong> {{working_directory}}, {{project_name}}, {{current_date}}<br>
                                            <strong>Example:</strong><br>
                                            <code>[{"name": "working_directory", "required": true, "description": "Project path"}]</code>
                                        </small>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button id="cancel-step-edit" class="btn-secondary">Cancel</button>
                            <button id="save-step-edit" class="btn-primary">Save Step</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script>
        // Workflow Manager Implementation
        class WorkflowManager {
            constructor() {
                this.api = window.todoAPI;
                this.selectedWorkflow = null;
                this.currentExecution = null;
                this.pollingInterval = null;
                this.statusUpdateInterval = null;
                
                // Editor state
                this.currentWorkflowDefinition = null;
                this.editingStep = null;
                this.editingStepIndex = -1;
                
                // DOM elements
                this.elements = {
                    loadingState: document.getElementById('loading-state'),
                    errorState: document.getElementById('error-state'),
                    workflowContent: document.getElementById('workflow-content'),
                    errorMessage: document.getElementById('error-message'),
                    retryBtn: document.getElementById('retry-btn'),
                    refreshBtn: document.getElementById('refresh-workflows'),
                    createNewWorkflowBtn: document.getElementById('create-new-workflow'),
                    availableWorkflows: document.getElementById('available-workflows'),
                    workflowsLoading: document.getElementById('workflows-loading'),
                    configSection: document.getElementById('config-section'),
                    configForm: document.getElementById('workflow-config-form'),
                    startWorkflowBtn: document.getElementById('start-workflow'),
                    stopWorkflowBtn: document.getElementById('stop-workflow'),
                    executionStatus: document.getElementById('execution-status'),
                    statusIndicator: document.getElementById('status-indicator'),
                    statusIcon: document.getElementById('status-icon'),
                    statusText: document.getElementById('status-text'),
                    progressFill: document.getElementById('progress-fill'),
                    stepList: document.getElementById('step-list'),
                    logOutput: document.getElementById('log-output'),
                    toggleLogsBtn: document.getElementById('toggle-logs'),
                    projectPath: document.getElementById('project-path'),
                    // Editor elements
                    workflowSelectEditor: document.getElementById('workflow-select-editor'),
                    loadWorkflowEditorBtn: document.getElementById('load-workflow-editor'),
                    workflowStepsEditor: document.getElementById('workflow-steps-editor'),
                    editingWorkflowName: document.getElementById('editing-workflow-name'),
                    addStepBtn: document.getElementById('add-step-btn'),
                    saveWorkflowBtn: document.getElementById('save-workflow-btn'),
                    stepsContainer: document.getElementById('steps-container'),
                    stepEditorModal: document.getElementById('step-editor-modal'),
                    modalTitle: document.getElementById('modal-title'),
                    closeModal: document.getElementById('close-modal'),
                    stepEditorForm: document.getElementById('step-editor-form'),
                    cancelStepEdit: document.getElementById('cancel-step-edit'),
                    saveStepEdit: document.getElementById('save-step-edit'),
                    projectName: document.getElementById('project-name')
                };

                this.init();
            }

            async init() {
                try {
                    // Check if all required elements exist
                    const missingElements = Object.entries(this.elements)
                        .filter(([key, element]) => !element)
                        .map(([key]) => key);
                    
                    if (missingElements.length > 0) {
                        console.warn('Missing DOM elements:', missingElements);
                    }
                    
                    this.setupEventListeners();
                    await this.loadAvailableWorkflows();
                    this.showWorkflowContent();
                } catch (error) {
                    console.error('Error initializing workflow manager:', error);
                    this.showError(error.message);
                }
            }

            setupEventListeners() {
                // Retry button
                this.elements.retryBtn.addEventListener('click', () => {
                    this.init();
                });

                // Refresh workflows
                this.elements.refreshBtn.addEventListener('click', () => {
                    this.loadAvailableWorkflows();
                });

                // Create new workflow
                this.elements.createNewWorkflowBtn.addEventListener('click', () => {
                    this.createNewWorkflow();
                });

                // Start workflow
                this.elements.startWorkflowBtn.addEventListener('click', () => {
                    this.startWorkflow();
                });

                // Stop workflow
                this.elements.stopWorkflowBtn.addEventListener('click', () => {
                    this.stopWorkflow();
                });

                // Toggle logs
                this.elements.toggleLogsBtn.addEventListener('click', () => {
                    this.toggleLogs();
                });

                // Auto-fill project path with current project
                this.autoFillProjectPath();
                
                // Editor event listeners (with null checks)
                if (this.elements.workflowSelectEditor) {
                    this.elements.workflowSelectEditor.addEventListener('change', () => {
                        this.elements.loadWorkflowEditorBtn.disabled = !this.elements.workflowSelectEditor.value;
                    });
                }

                if (this.elements.loadWorkflowEditorBtn) {
                    this.elements.loadWorkflowEditorBtn.addEventListener('click', () => {
                        this.loadWorkflowForEditing();
                    });
                }

                if (this.elements.addStepBtn) {
                    this.elements.addStepBtn.addEventListener('click', () => {
                        this.openStepEditor('add', -1);
                    });
                }

                if (this.elements.saveWorkflowBtn) {
                    this.elements.saveWorkflowBtn.addEventListener('click', () => {
                        this.saveWorkflowChanges();
                    });
                }

                // Modal event listeners (with null checks)
                if (this.elements.closeModal) {
                    this.elements.closeModal.addEventListener('click', () => {
                        this.closeStepEditor();
                    });
                }

                if (this.elements.cancelStepEdit) {
                    this.elements.cancelStepEdit.addEventListener('click', () => {
                        this.closeStepEditor();
                    });
                }

                if (this.elements.saveStepEdit) {
                    this.elements.saveStepEdit.addEventListener('click', () => {
                        this.saveStepChanges();
                    });
                }

                // Close modal on outside click
                if (this.elements.stepEditorModal) {
                    this.elements.stepEditorModal.addEventListener('click', (e) => {
                        if (e.target === this.elements.stepEditorModal) {
                            this.closeStepEditor();
                        }
                    });
                }

                // Setup tab switching functionality
                this.setupTabSwitching();
            }

            setupTabSwitching() {
                // Get all tab buttons and content sections
                const tabButtons = this.elements.stepEditorModal.querySelectorAll('.tab-btn');
                const tabContents = this.elements.stepEditorModal.querySelectorAll('.tab-content');

                // Add click event listeners to tab buttons
                tabButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        const tabId = button.getAttribute('data-tab');
                        
                        // Remove active class from all tab buttons
                        tabButtons.forEach(btn => btn.classList.remove('active'));
                        
                        // Hide all tab contents
                        tabContents.forEach(content => content.classList.add('hidden'));
                        
                        // Activate clicked tab button
                        button.classList.add('active');
                        
                        // Show corresponding tab content
                        const targetContent = this.elements.stepEditorModal.querySelector(`#tab-${tabId}`);
                        if (targetContent) {
                            targetContent.classList.remove('hidden');
                        }
                    });
                });
            }

            async loadAvailableWorkflows() {
                this.elements.workflowsLoading.classList.add('active');
                this.elements.availableWorkflows.innerHTML = '';

                try {
                    const response = await this.api.getAvailableWorkflows();
                    const workflows = response.workflows || [];

                    if (workflows.length === 0) {
                        this.elements.availableWorkflows.innerHTML = `
                            <div class="empty-state">
                                <p>No workflows found</p>
                                <small>Make sure workflows are available in the scripts/workflows directory</small>
                            </div>
                        `;
                    } else {
                        workflows.forEach(workflow => {
                            this.renderWorkflowItem(workflow);
                        });
                        this.populateEditorWorkflowSelect(workflows);
                    }
                } catch (error) {
                    console.error('Error loading workflows:', error);
                    this.elements.availableWorkflows.innerHTML = `
                        <div class="error-message">
                            <p>Failed to load workflows: ${error.message}</p>
                        </div>
                    `;
                } finally {
                    this.elements.workflowsLoading.classList.remove('active');
                }
            }

            renderWorkflowItem(workflow) {
                const workflowDiv = document.createElement('div');
                workflowDiv.className = 'workflow-item';
                workflowDiv.dataset.workflowId = workflow.id;

                workflowDiv.innerHTML = `
                    <h3>${workflow.name}</h3>
                    <div class="description">${workflow.description}</div>
                    <div class="meta">
                        <span>Version: ${workflow.version}</span>
                        <span class="steps-count">${workflow.total_steps} steps</span>
                    </div>
                `;

                workflowDiv.addEventListener('click', () => {
                    this.selectWorkflow(workflow, workflowDiv);
                });

                this.elements.availableWorkflows.appendChild(workflowDiv);
            }

            selectWorkflow(workflow, element) {
                // Remove previous selection
                this.elements.availableWorkflows.querySelectorAll('.workflow-item').forEach(item => {
                    item.classList.remove('selected');
                });

                // Select current workflow
                element.classList.add('selected');
                this.selectedWorkflow = workflow;

                // Show configuration section
                this.elements.configSection.classList.add('active');
                this.elements.executionStatus.classList.add('active');
                this.elements.startWorkflowBtn.disabled = false;

                // Reset execution status
                this.resetExecutionStatus();
            }

            async startWorkflow() {
                if (!this.selectedWorkflow) {
                    TodoUtils.toast.error('Please select a workflow first');
                    return;
                }

                const formData = new FormData(this.elements.configForm);
                const config = {
                    workflow_id: this.selectedWorkflow.id,
                    project_path: formData.get('project-path'),
                    project_name: formData.get('project-name'),
                    log_level: formData.get('log-level'),
                    timeout: parseInt(formData.get('timeout')),
                    additional_config: formData.get('additional-config')
                };

                // Validate required fields
                if (!config.project_path) {
                    TodoUtils.toast.error('Project path is required');
                    this.elements.projectPath.focus();
                    return;
                }

                try {
                    this.elements.startWorkflowBtn.disabled = true;
                    this.elements.stopWorkflowBtn.disabled = false;

                    // Start the workflow
                    const response = await this.api.startWorkflow(config);
                    this.currentExecution = response.execution_id;

                    // Update status
                    this.updateStatus('running', '⚡', 'Running');

                    // Start status polling
                    this.startStatusPolling();

                    TodoUtils.toast.success('Workflow started successfully');
                } catch (error) {
                    console.error('Error starting workflow:', error);
                    TodoUtils.toast.error(`Failed to start workflow: ${error.message}`);
                    this.elements.startWorkflowBtn.disabled = false;
                    this.elements.stopWorkflowBtn.disabled = true;
                }
            }

            async stopWorkflow() {
                if (!this.currentExecution) {
                    TodoUtils.toast.error('No workflow is currently running');
                    return;
                }

                try {
                    await this.api.stopWorkflow(this.currentExecution);
                    this.stopStatusPolling();
                    this.updateStatus('idle', '⏸️', 'Stopped');
                    this.elements.startWorkflowBtn.disabled = false;
                    this.elements.stopWorkflowBtn.disabled = true;
                    TodoUtils.toast.info('Workflow stopped');
                } catch (error) {
                    console.error('Error stopping workflow:', error);
                    TodoUtils.toast.error(`Failed to stop workflow: ${error.message}`);
                }
            }

            startStatusPolling() {
                // Poll every 2 seconds for status updates
                this.statusUpdateInterval = setInterval(async () => {
                    await this.updateWorkflowStatus();
                }, 2000);

                // Poll every 5 seconds for HTML status file updates
                this.pollingInterval = setInterval(async () => {
                    await this.checkStatusFile();
                }, 5000);
            }

            stopStatusPolling() {
                if (this.statusUpdateInterval) {
                    clearInterval(this.statusUpdateInterval);
                    this.statusUpdateInterval = null;
                }
                if (this.pollingInterval) {
                    clearInterval(this.pollingInterval);
                    this.pollingInterval = null;
                }
            }

            async updateWorkflowStatus() {
                if (!this.currentExecution) return;

                try {
                    const status = await this.api.getWorkflowStatus(this.currentExecution);
                    this.renderWorkflowStatus(status);
                } catch (error) {
                    console.error('Error updating workflow status:', error);
                }
            }

            async checkStatusFile() {
                if (!this.currentExecution) return;

                try {
                    const statusHtml = await this.api.getWorkflowStatusHtml(this.currentExecution);
                    if (statusHtml && statusHtml.content) {
                        this.updateStatusFromHtml(statusHtml.content);
                    }
                } catch (error) {
                    // Status file might not exist yet, ignore errors
                    console.debug('Status file not available:', error.message);
                }
            }

            renderWorkflowStatus(status) {
                // Update progress
                const progress = (status.completed_steps / status.total_steps) * 100;
                this.elements.progressFill.style.width = `${progress}%`;

                // Update status indicator
                if (status.status === 'completed') {
                    this.updateStatus('completed', '✅', 'Completed');
                    this.stopStatusPolling();
                    this.elements.startWorkflowBtn.disabled = false;
                    this.elements.stopWorkflowBtn.disabled = true;
                } else if (status.status === 'error') {
                    this.updateStatus('error', '❌', 'Error');
                    this.stopStatusPolling();
                    this.elements.startWorkflowBtn.disabled = false;
                    this.elements.stopWorkflowBtn.disabled = true;
                } else if (status.status === 'running') {
                    this.updateStatus('running', '⚡', `Running (${status.completed_steps}/${status.total_steps})`);
                }

                // Update step list
                this.renderSteps(status.steps);
            }

            renderSteps(steps) {
                this.elements.stepList.innerHTML = '';

                steps.forEach(step => {
                    const stepDiv = document.createElement('div');
                    stepDiv.className = `step-item ${step.status}`;

                    const icon = this.getStepIcon(step.status);
                    const statusText = this.getStepStatusText(step.status);

                    stepDiv.innerHTML = `
                        <span class="step-icon">${icon}</span>
                        <div class="step-info">
                            <div class="step-name">${step.name}</div>
                            <div class="step-description">${step.description || ''}</div>
                        </div>
                        <div class="step-status">${statusText}</div>
                    `;

                    this.elements.stepList.appendChild(stepDiv);
                });
            }

            updateStatusFromHtml(htmlContent) {
                // Parse HTML content and extract log entries
                const parser = new DOMParser();
                const doc = parser.parseFromString(htmlContent, 'text/html');
                
                // Look for log entries or status updates
                const logEntries = doc.querySelectorAll('.log-entry, .status-update');
                logEntries.forEach(entry => {
                    this.addLogEntry(entry.textContent, entry.className);
                });
            }

            addLogEntry(message, className = '') {
                const logLine = document.createElement('div');
                logLine.className = `log-line ${className}`;
                logLine.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                
                this.elements.logOutput.appendChild(logLine);
                this.elements.logOutput.scrollTop = this.elements.logOutput.scrollHeight;
            }

            getStepIcon(status) {
                const icons = {
                    pending: '⏳',
                    running: '🔄',
                    completed: '✅',
                    error: '❌',
                    skipped: '⏭️'
                };
                return icons[status] || '❓';
            }

            getStepStatusText(status) {
                const texts = {
                    pending: 'Pending',
                    running: 'Running',
                    completed: 'Completed',
                    error: 'Error',
                    skipped: 'Skipped'
                };
                return texts[status] || 'Unknown';
            }

            updateStatus(statusClass, icon, text) {
                this.elements.statusIndicator.className = `status-indicator ${statusClass}`;
                this.elements.statusIcon.textContent = icon;
                this.elements.statusText.textContent = text;
            }

            resetExecutionStatus() {
                this.updateStatus('idle', '⏸️', 'Idle');
                this.elements.progressFill.style.width = '0%';
                this.elements.stepList.innerHTML = '';
                this.elements.logOutput.innerHTML = '';
                this.elements.startWorkflowBtn.disabled = false;
                this.elements.stopWorkflowBtn.disabled = true;
                this.currentExecution = null;
                this.stopStatusPolling();
            }

            toggleLogs() {
                const isVisible = this.elements.logOutput.classList.contains('active');
                
                if (isVisible) {
                    this.elements.logOutput.classList.remove('active');
                    this.elements.toggleLogsBtn.textContent = '📜 Show Logs';
                } else {
                    this.elements.logOutput.classList.add('active');
                    this.elements.toggleLogsBtn.textContent = '🗂️ Hide Logs';
                }
            }

            autoFillProjectPath() {
                // Get current project from URL params or localStorage
                const urlParams = TodoUtils.routing.getParams();
                const project = urlParams.get('project') || 'todo-lists';
                
                if (project) {
                    this.elements.projectPath.value = `C:\\Users\\<USER>\\source\\repos\\${project}`;
                    this.elements.projectName.value = project.charAt(0).toUpperCase() + project.slice(1);
                }
            }

            showLoadingState() {
                TodoUtils.dom.toggle(this.elements.loadingState, true);
                TodoUtils.dom.toggle(this.elements.errorState, false);
                TodoUtils.dom.toggle(this.elements.workflowContent, false);
            }

            showError(message) {
                this.elements.errorMessage.textContent = message;
                TodoUtils.dom.toggle(this.elements.loadingState, false);
                TodoUtils.dom.toggle(this.elements.errorState, true);
                TodoUtils.dom.toggle(this.elements.workflowContent, false);
            }

            showWorkflowContent() {
                TodoUtils.dom.toggle(this.elements.loadingState, false);
                TodoUtils.dom.toggle(this.elements.errorState, false);
                TodoUtils.dom.toggle(this.elements.workflowContent, true);
            }

            // Workflow Editor Methods
            populateEditorWorkflowSelect(workflows) {
                if (!this.elements.workflowSelectEditor) {
                    console.warn('Workflow select editor element not found');
                    return;
                }
                
                // Clear existing options except the first
                this.elements.workflowSelectEditor.innerHTML = '<option value="">Select a workflow to edit...</option>';
                
                workflows.forEach(workflow => {
                    const option = document.createElement('option');
                    option.value = workflow.id;
                    option.textContent = `${workflow.name} (${workflow.total_steps || 0} steps)`;
                    this.elements.workflowSelectEditor.appendChild(option);
                });
            }

            async loadWorkflowForEditing() {
                const workflowId = this.elements.workflowSelectEditor.value;
                if (!workflowId) return;

                try {
                    this.currentWorkflowDefinition = await this.api.getWorkflowDefinition(workflowId);
                    console.log('Loaded workflow definition:', this.currentWorkflowDefinition);
                    this.renderWorkflowStepsEditor();
                    
                    const workflowName = this.currentWorkflowDefinition.config?.workflow?.name || workflowId;
                    this.elements.editingWorkflowName.textContent = `Editing: ${workflowName}`;
                    TodoUtils.dom.toggle(this.elements.workflowStepsEditor, true);
                } catch (error) {
                    console.error('Error loading workflow for editing:', error);
                    alert('Failed to load workflow for editing: ' + error.message);
                }
            }

            renderWorkflowStepsEditor() {
                if (!this.currentWorkflowDefinition) return;
                
                const container = this.elements.stepsContainer;
                container.innerHTML = '';

                this.currentWorkflowDefinition.steps.forEach((step, index) => {
                    const stepDiv = this.createStepEditorItem(step, index);
                    container.appendChild(stepDiv);
                });
            }

            createStepEditorItem(step, index) {
                const stepDiv = document.createElement('div');
                stepDiv.className = 'step-editor-item';
                stepDiv.innerHTML = `
                    <div class="step-editor-header">
                        <div class="step-info">
                            <h4>${step.title || 'Untitled Step'}</h4>
                            <div class="step-id">${step.step_id || 'no-id'}</div>
                            <div class="step-description" style="margin-top: 0.5rem; color: #6c757d;">${step.description || ''}</div>
                        </div>
                        <div class="step-actions">
                            <button class="btn-insert" onclick="workflowManager.openStepEditor('insert', ${index})">↑ Insert</button>
                            <button class="btn-edit" onclick="workflowManager.openStepEditor('edit', ${index})">✏️ Edit</button>
                            <button class="btn-remove" onclick="workflowManager.removeStep(${index})">🗑️ Remove</button>
                        </div>
                    </div>
                `;
                return stepDiv;
            }

            openStepEditor(mode, index) {
                this.editingStepIndex = index;
                const step = mode === 'add' || mode === 'insert' ? {} : this.currentWorkflowDefinition.steps[index];
                this.editingStep = step;

                // Set modal title
                const titles = {
                    'add': 'Add New Step',
                    'insert': `Insert Step Before Step ${index + 1}`,
                    'edit': `Edit Step: ${step.title || step.step_id}`
                };
                this.elements.modalTitle.textContent = titles[mode];

                // Populate form
                this.populateStepForm(step);
                
                // Initialize tabs - show basic tab, hide others
                this.initializeTabs();
                
                // Show modal
                TodoUtils.dom.toggle(this.elements.stepEditorModal, true);
            }

            initializeTabs() {
                // Reset all tab buttons to inactive
                const tabButtons = this.elements.stepEditorModal.querySelectorAll('.tab-btn');
                tabButtons.forEach(btn => btn.classList.remove('active'));
                
                // Hide all tab contents
                const tabContents = this.elements.stepEditorModal.querySelectorAll('.tab-content');
                tabContents.forEach(content => content.classList.add('hidden'));
                
                // Activate first tab (basic info)
                const basicTabBtn = this.elements.stepEditorModal.querySelector('.tab-btn[data-tab="basic"]');
                const basicTabContent = this.elements.stepEditorModal.querySelector('#tab-basic');
                
                if (basicTabBtn) basicTabBtn.classList.add('active');
                if (basicTabContent) basicTabContent.classList.remove('hidden');
            }

            populateStepForm(step) {
                const form = this.elements.stepEditorForm;
                form.querySelector('#step-id').value = step.step_id || '';
                form.querySelector('#step-title').value = step.title || '';
                form.querySelector('#step-description').value = step.description || '';
                form.querySelector('#step-version').value = step.version || '1.0';
                form.querySelector('#step-timeout').value = step.config?.timeout || 300;
                form.querySelector('#step-script-path').value = step.execution?.script?.path || '';
                form.querySelector('#step-dependencies').value = JSON.stringify(step.dependencies || [], null, 2);
                form.querySelector('#step-parameters').value = JSON.stringify(step.input?.parameters || {}, null, 2);
            }

            closeStepEditor() {
                TodoUtils.dom.toggle(this.elements.stepEditorModal, false);
                this.editingStep = null;
                this.editingStepIndex = -1;
            }

            async saveStepChanges() {
                try {
                    const formData = this.getStepFormData();
                    if (!formData) return;

                    const mode = this.editingStepIndex === -1 ? 'add' : 
                               this.elements.modalTitle.textContent.includes('Insert') ? 'insert' : 'edit';

                    if (mode === 'add') {
                        // Add to end
                        this.currentWorkflowDefinition.steps.push(formData);
                    } else if (mode === 'insert') {
                        // Insert at position
                        this.currentWorkflowDefinition.steps.splice(this.editingStepIndex, 0, formData);
                    } else {
                        // Edit existing
                        this.currentWorkflowDefinition.steps[this.editingStepIndex] = formData;
                    }

                    this.renderWorkflowStepsEditor();
                    this.closeStepEditor();
                } catch (error) {
                    console.error('Error saving step:', error);
                    alert('Error saving step: ' + error.message);
                }
            }

            getStepFormData() {
                const form = this.elements.stepEditorForm;
                
                try {
                    const dependencies = JSON.parse(form.querySelector('#step-dependencies').value || '[]');
                    const parameters = JSON.parse(form.querySelector('#step-parameters').value || '{}');

                    return {
                        step_id: form.querySelector('#step-id').value,
                        title: form.querySelector('#step-title').value,
                        description: form.querySelector('#step-description').value,
                        version: form.querySelector('#step-version').value,
                        dependencies: dependencies,
                        input: {
                            parameters: parameters
                        },
                        execution: {
                            script: {
                                path: form.querySelector('#step-script-path').value
                            }
                        },
                        config: {
                            timeout: parseInt(form.querySelector('#step-timeout').value),
                        }
                    };
                } catch (error) {
                    throw new Error('Invalid JSON in dependencies or parameters');
                }
            }

            async removeStep(index) {
                const stepTitle = this.currentWorkflowDefinition.steps[index].title || this.currentWorkflowDefinition.steps[index].step_id;
                
                if (confirm(`Are you sure you want to remove step "${stepTitle}"?`)) {
                    try {
                        // Remove step from local array
                        this.currentWorkflowDefinition.steps.splice(index, 1);
                        
                        // Re-render the UI immediately
                        this.renderWorkflowStepsEditor();
                        
                        // Auto-save the changes to persist them (silently)
                        await this.saveWorkflowChanges(true);
                        
                        console.log('Step removed and workflow saved successfully');
                    } catch (error) {
                        console.error('Error removing step:', error);
                        alert('Error removing step: ' + error.message);
                        
                        // Reload the workflow to revert changes if save failed
                        await this.loadWorkflowDefinition(this.currentWorkflowDefinition.workflow_id);
                    }
                }
            }

            async saveWorkflowChanges(silent = false) {
                if (!this.currentWorkflowDefinition) return;
                
                try {
                    // Save each step via API
                    const workflowId = this.currentWorkflowDefinition.workflow_id;
                    
                    // For simplicity, we'll save all steps (in a real implementation, we'd track changes)
                    for (let i = 0; i < this.currentWorkflowDefinition.steps.length; i++) {
                        const step = this.currentWorkflowDefinition.steps[i];
                        const filename = step._filename || `${String(i + 1).padStart(2, '0')}-${step.step_id}.yml`;
                        
                        await this.api.updateWorkflowStep(workflowId, filename, step);
                    }
                    
                    if (!silent) {
                        alert('Workflow saved successfully!');
                    }
                    
                    // Refresh the workflow list
                    await this.loadAvailableWorkflows();
                } catch (error) {
                    console.error('Error saving workflow:', error);
                    if (!silent) {
                        alert('Error saving workflow: ' + error.message);
                    }
                    throw error; // Re-throw so removeStep can handle it
                }
            }

            createNewWorkflow() {
                // Prompt for workflow name
                const workflowName = prompt('Enter a name for the new workflow:');
                
                if (!workflowName || workflowName.trim() === '') {
                    return;
                }

                // Create a basic workflow structure
                const newWorkflow = {
                    workflow_id: workflowName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
                    name: workflowName,
                    description: 'A new workflow',
                    version: '1.0.0',
                    steps: [
                        {
                            step_id: 'initial-step',
                            title: 'Initial Step',
                            description: 'The first step of the workflow',
                            version: '1.0.0',
                            dependencies: [],
                            input: {
                                parameters: {}
                            },
                            execution: {
                                script: {
                                    path: ''
                                }
                            },
                            config: {
                                timeout: 300
                            }
                        }
                    ]
                };

                // Set this as the current workflow and open the editor
                this.currentWorkflowDefinition = newWorkflow;
                this.loadWorkflowEditor();

                // Show a message to the user
                alert(`Created new workflow "${workflowName}". Configure the steps and click "Save Workflow" to persist it.`);
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.workflowManager = new WorkflowManager();
        });
    </script>
        </div>
    </main>
</body>
</html>