<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Todo Management System</title>
    <style>
        body { font-family: monospace; padding: 20px; }
        .log { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        .error { background: #ffeeee; }
        .success { background: #eeffee; }
    </style>
</head>
<body>
    <h1>Debug Page</h1>
    <div id="log"></div>
    
    <script>
        const log = document.getElementById('log');
        
        function addLog(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(div);
            console.log(message);
        }
        
        async function testAPI() {
            addLog('Starting API tests...');
            
            try {
                // Test health endpoint
                addLog('Testing health endpoint...');
                const healthResponse = await fetch('http://localhost:8000/api/v1/health');
                addLog(`Health status: ${healthResponse.status}`);
                
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    addLog(`Health data: ${JSON.stringify(healthData)}`, 'success');
                } else {
                    addLog(`Health check failed: ${healthResponse.statusText}`, 'error');
                    return;
                }
                
                // Test projects endpoint
                addLog('Testing projects endpoint...');
                const projectsResponse = await fetch('http://localhost:8000/api/v1/projects');
                addLog(`Projects status: ${projectsResponse.status}`);
                
                if (projectsResponse.ok) {
                    const projectsData = await projectsResponse.json();
                    addLog(`Projects data: ${JSON.stringify(projectsData)}`, 'success');
                } else {
                    addLog(`Projects check failed: ${projectsResponse.statusText}`, 'error');
                }
                
            } catch (error) {
                addLog(`API test error: ${error.message}`, 'error');
            }
        }
        
        // Run tests on page load
        testAPI();
        
        // Test script loading
        addLog('Testing script loading...');
        
        // Load utils script
        const utilsScript = document.createElement('script');
        utilsScript.src = 'js/utils.js';
        utilsScript.onload = () => {
            addLog('Utils script loaded', 'success');
            if (window.TodoUtils) {
                addLog('TodoUtils available', 'success');
            } else {
                addLog('TodoUtils not available', 'error');
            }
        };
        utilsScript.onerror = () => addLog('Utils script failed to load', 'error');
        document.head.appendChild(utilsScript);
        
        // Load api script
        const apiScript = document.createElement('script');
        apiScript.src = 'js/api.js';
        apiScript.onload = () => {
            addLog('API script loaded', 'success');
            if (window.todoAPI) {
                addLog('todoAPI available', 'success');
            } else {
                addLog('todoAPI not available', 'error');
            }
        };
        apiScript.onerror = () => addLog('API script failed to load', 'error');
        document.head.appendChild(apiScript);
        
        // Load app script
        const appScript = document.createElement('script');
        appScript.src = 'js/app.js';
        appScript.onload = () => {
            addLog('App script loaded', 'success');
            if (window.TodoApp) {
                addLog('TodoApp available', 'success');
            } else {
                addLog('TodoApp not available', 'error');
            }
        };
        appScript.onerror = () => addLog('App script failed to load', 'error');
        document.head.appendChild(appScript);
    </script>
</body>
</html>