<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Todo Management System</title>
    <link rel="stylesheet" href="css/style.css">
    <meta name="description" content="API Testing Interface for Todo Management System">
    <style>
        /* Additional styles specific to API test page */
        .test-section {
            background: var(--jomezpro-white);
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px var(--jomezpro-shadow);
        }

        .test-form {
            display: grid;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group label {
            font-weight: 500;
            color: var(--jomezpro-gray);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 0.75rem;
            border: 1px solid var(--jomezpro-border);
            border-radius: 0.375rem;
            font-size: 1rem;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--jomezpro-teal);
            box-shadow: 0 0 0 3px rgba(0, 196, 170, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .response-container {
            background: var(--jomezpro-bg);
            border: 1px solid var(--jomezpro-border);
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .response-success {
            border-color: var(--jomezpro-success);
            background: rgba(39, 174, 96, 0.05);
        }

        .response-error {
            border-color: var(--jomezpro-error);
            background: rgba(231, 76, 60, 0.05);
        }

        .endpoint-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
            margin-bottom: 1rem;
            border-bottom: 1px solid var(--jomezpro-border);
        }

        .endpoint-tab {
            padding: 0.75rem 1rem;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .endpoint-tab:hover {
            background: var(--jomezpro-bg);
        }

        .endpoint-tab.active {
            border-bottom-color: var(--jomezpro-teal);
            color: var(--jomezpro-teal);
            font-weight: 500;
        }

        .endpoint-content {
            display: none;
        }

        .endpoint-content.active {
            display: block;
        }

        .quick-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }

        .health-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
        }

        .health-status.online {
            background: rgba(39, 174, 96, 0.1);
            color: var(--jomezpro-success);
        }

        .health-status.offline {
            background: rgba(231, 76, 60, 0.1);
            color: var(--jomezpro-error);
        }

        .status-indicator {
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
            background: currentColor;
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <h1>🔧 API Test Console</h1>
            <nav class="main-nav" role="navigation" aria-label="Main navigation">
                <ul>
                    <li><a href="index.html">Dashboard</a></li>
                    <li><a href="api-test.html" aria-current="page">API Test</a></li>
                    <li><a href="today.html">Today</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- API Status -->
            <section class="test-section">
                <h2>API Status</h2>
                <div id="health-status" class="health-status offline">
                    <div class="status-indicator"></div>
                    <span>Checking API connection...</span>
                </div>
                <div class="quick-actions">
                    <button id="health-check-btn" class="btn btn-secondary">
                        🔍 Health Check
                    </button>
                    <button id="setup-whitelist-btn" class="btn btn-primary">
                        🛡️ Setup Whitelist
                    </button>
                    <button id="clear-responses-btn" class="btn btn-secondary">
                        🗑️ Clear Responses
                    </button>
                </div>
            </section>

            <!-- Endpoint Testing -->
            <section class="test-section">
                <h2>Endpoint Testing</h2>
                
                <div class="endpoint-tabs">
                    <button class="endpoint-tab active" data-tab="security">Security</button>
                    <button class="endpoint-tab" data-tab="projects">Projects</button>
                    <button class="endpoint-tab" data-tab="todos">Todos</button>
                    <button class="endpoint-tab" data-tab="custom">Custom</button>
                </div>

                <!-- Security Tab -->
                <div id="security-tab" class="endpoint-content active">
                    <h3>Security & Whitelist Management</h3>
                    
                    <div class="test-form">
                        <div class="form-group">
                            <label for="whitelist-directory">Directory Path</label>
                            <input 
                                type="text" 
                                id="whitelist-directory" 
                                placeholder="C:/Users/<USER>/source/repos"
                                value="C:/Users/<USER>/source/repos"
                            >
                        </div>
                        <div class="form-actions">
                            <button id="get-whitelist-btn" class="btn btn-secondary">Get Whitelist</button>
                            <button id="add-whitelist-btn" class="btn btn-primary">Add Directory</button>
                            <button id="remove-whitelist-btn" class="btn btn-secondary">Remove Directory</button>
                        </div>
                    </div>
                </div>

                <!-- Projects Tab -->
                <div id="projects-tab" class="endpoint-content">
                    <h3>Project Management</h3>
                    
                    <div class="test-form">
                        <div class="form-group">
                            <label for="project-name">Project Name</label>
                            <input 
                                type="text" 
                                id="project-name" 
                                placeholder="AccountingCrmInterfaces"
                                value="AccountingCrmInterfaces"
                            >
                        </div>
                        <div class="form-actions">
                            <button id="get-projects-btn" class="btn btn-secondary">Get All Projects</button>
                            <button id="get-project-btn" class="btn btn-primary">Get Project Details</button>
                        </div>
                    </div>
                </div>

                <!-- Todos Tab -->
                <div id="todos-tab" class="endpoint-content">
                    <h3>Todo Management</h3>
                    
                    <div class="test-form">
                        <div class="form-group">
                            <label for="todo-project-name">Project Name</label>
                            <input 
                                type="text" 
                                id="todo-project-name" 
                                placeholder="AccountingCrmInterfaces"
                                value="AccountingCrmInterfaces"
                            >
                        </div>
                        <div class="form-group">
                            <label for="todo-id">Todo ID (for individual operations)</label>
                            <input 
                                type="text" 
                                id="todo-id" 
                                placeholder="testing-001"
                            >
                        </div>
                        <div class="form-group">
                            <label for="todo-status-filter">Status Filter</label>
                            <select id="todo-status-filter">
                                <option value="">All</option>
                                <option value="pending">Pending</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="new-todo-data">New Todo Data (JSON)</label>
                            <textarea id="new-todo-data" placeholder='{
  "title": "Test Todo",
  "description": "This is a test todo",
  "priority": "medium",
  "category": "testing"
}'></textarea>
                        </div>
                        <div class="form-actions">
                            <button id="get-todos-btn" class="btn btn-secondary">Get Todos</button>
                            <button id="get-todo-btn" class="btn btn-secondary">Get Single Todo</button>
                            <button id="create-todo-btn" class="btn btn-primary">Create Todo</button>
                            <button id="mark-complete-btn" class="btn btn-success">Mark Complete</button>
                        </div>
                    </div>
                </div>

                <!-- Custom Tab -->
                <div id="custom-tab" class="endpoint-content">
                    <h3>Custom API Request</h3>
                    
                    <div class="test-form">
                        <div class="form-group">
                            <label for="custom-method">HTTP Method</label>
                            <select id="custom-method">
                                <option value="GET">GET</option>
                                <option value="POST">POST</option>
                                <option value="PUT">PUT</option>
                                <option value="PATCH">PATCH</option>
                                <option value="DELETE">DELETE</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="custom-endpoint">Endpoint (relative to /api/v1)</label>
                            <input 
                                type="text" 
                                id="custom-endpoint" 
                                placeholder="/health"
                                value="/health"
                            >
                        </div>
                        <div class="form-group">
                            <label for="custom-body">Request Body (JSON)</label>
                            <textarea id="custom-body" placeholder='{"key": "value"}'></textarea>
                        </div>
                        <div class="form-actions">
                            <button id="send-custom-btn" class="btn btn-primary">Send Request</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Response Display -->
            <section class="test-section">
                <h2>Response</h2>
                <div id="response-container" class="response-container">
                    No response yet. Try making an API call above.
                </div>
            </section>

            <!-- Documentation -->
            <section class="test-section">
                <h2>Quick Reference</h2>
                <div>
                    <h3>Common Endpoints</h3>
                    <ul>
                        <li><code>GET /api/v1/health</code> - Health check</li>
                        <li><code>GET /api/v1/security/whitelist</code> - Get whitelisted directories</li>
                        <li><code>POST /api/v1/security/whitelist</code> - Add directory to whitelist</li>
                        <li><code>GET /api/v1/projects</code> - List all projects</li>
                        <li><code>GET /api/v1/projects/{name}</code> - Get project details</li>
                        <li><code>GET /api/v1/projects/{name}/todos</code> - Get todos for project</li>
                        <li><code>POST /api/v1/projects/{name}/todos</code> - Create new todo</li>
                        <li><code>PATCH /api/v1/projects/{name}/todos/{id}</code> - Update todo</li>
                    </ul>
                    
                    <h3>Default Whitelist Directories</h3>
                    <ul>
                        <li><code>C:/Users/<USER>/source/repos</code> (Windows)</li>
                        <li><code>/home/<USER>/source/repos</code> (Unix/Linux)</li>
                    </ul>
                    
                    <h3>Project Structure Expected</h3>
                    <code>~/source/repos/ProjectName/project-progress/to-do-lists/todos.yml</code>
                </div>
            </section>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2025 Todo Management System | <a href="index.html">Back to Dashboard</a> | <a href="api-test.html">API Test</a> | <a href="today.html">Today</a></p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script>
        // API Test Console Implementation
        class APITestConsole {
            constructor() {
                this.api = window.todoAPI;
                this.responseContainer = document.getElementById('response-container');
                this.healthStatus = document.getElementById('health-status');
                
                this.init();
            }

            async init() {
                this.setupEventListeners();
                this.setupTabs();
                await this.checkHealth();
            }

            setupEventListeners() {
                // Health check
                document.getElementById('health-check-btn').addEventListener('click', () => this.checkHealth());
                
                // Quick actions
                document.getElementById('setup-whitelist-btn').addEventListener('click', () => this.setupWhitelist());
                document.getElementById('clear-responses-btn').addEventListener('click', () => this.clearResponses());

                // Security endpoints
                document.getElementById('get-whitelist-btn').addEventListener('click', () => this.getWhitelist());
                document.getElementById('add-whitelist-btn').addEventListener('click', () => this.addWhitelist());
                document.getElementById('remove-whitelist-btn').addEventListener('click', () => this.removeWhitelist());

                // Project endpoints
                document.getElementById('get-projects-btn').addEventListener('click', () => this.getProjects());
                document.getElementById('get-project-btn').addEventListener('click', () => this.getProject());

                // Todo endpoints
                document.getElementById('get-todos-btn').addEventListener('click', () => this.getTodos());
                document.getElementById('get-todo-btn').addEventListener('click', () => this.getTodo());
                document.getElementById('create-todo-btn').addEventListener('click', () => this.createTodo());
                document.getElementById('mark-complete-btn').addEventListener('click', () => this.markComplete());

                // Custom request
                document.getElementById('send-custom-btn').addEventListener('click', () => this.sendCustomRequest());
            }

            setupTabs() {
                const tabs = document.querySelectorAll('.endpoint-tab');
                const contents = document.querySelectorAll('.endpoint-content');

                tabs.forEach(tab => {
                    tab.addEventListener('click', () => {
                        const targetTab = tab.dataset.tab;

                        // Update tab states
                        tabs.forEach(t => t.classList.remove('active'));
                        tab.classList.add('active');

                        // Update content states
                        contents.forEach(content => {
                            content.classList.remove('active');
                            if (content.id === `${targetTab}-tab`) {
                                content.classList.add('active');
                            }
                        });
                    });
                });
            }

            async checkHealth() {
                try {
                    this.updateHealthStatus('Checking...', false);
                    const response = await this.api.healthCheck();
                    this.updateHealthStatus('API Online', true);
                    this.displayResponse('Health Check', response, true);
                } catch (error) {
                    this.updateHealthStatus('API Offline', false);
                    this.displayResponse('Health Check', error, false);
                }
            }

            updateHealthStatus(message, isOnline) {
                this.healthStatus.className = `health-status ${isOnline ? 'online' : 'offline'}`;
                this.healthStatus.querySelector('span').textContent = message;
            }

            async setupWhitelist() {
                try {
                    const result = await this.api.setupWhitelist();
                    this.displayResponse('Setup Whitelist', { success: true, directory: result }, true);
                    TodoUtils.toast.success('Whitelist setup completed');
                } catch (error) {
                    this.displayResponse('Setup Whitelist', error, false);
                    TodoUtils.toast.error('Failed to setup whitelist');
                }
            }

            clearResponses() {
                this.responseContainer.textContent = 'Responses cleared.';
                this.responseContainer.className = 'response-container';
            }

            async getWhitelist() {
                try {
                    const directories = await this.api.getWhitelistedDirectories();
                    this.displayResponse('Get Whitelist', { directories }, true);
                } catch (error) {
                    this.displayResponse('Get Whitelist', error, false);
                }
            }

            async addWhitelist() {
                const directory = document.getElementById('whitelist-directory').value.trim();
                if (!directory) {
                    TodoUtils.toast.warning('Please enter a directory path');
                    return;
                }

                try {
                    const response = await this.api.addWhitelistedDirectory(directory);
                    this.displayResponse('Add Whitelist', response, true);
                    TodoUtils.toast.success('Directory added to whitelist');
                } catch (error) {
                    this.displayResponse('Add Whitelist', error, false);
                }
            }

            async removeWhitelist() {
                const directory = document.getElementById('whitelist-directory').value.trim();
                if (!directory) {
                    TodoUtils.toast.warning('Please enter a directory path');
                    return;
                }

                try {
                    const response = await this.api.removeWhitelistedDirectory(directory);
                    this.displayResponse('Remove Whitelist', response, true);
                    TodoUtils.toast.success('Directory removed from whitelist');
                } catch (error) {
                    this.displayResponse('Remove Whitelist', error, false);
                }
            }

            async getProjects() {
                try {
                    const projects = await this.api.getProjects();
                    this.displayResponse('Get Projects', { projects }, true);
                } catch (error) {
                    this.displayResponse('Get Projects', error, false);
                }
            }

            async getProject() {
                const projectName = document.getElementById('project-name').value.trim();
                if (!projectName) {
                    TodoUtils.toast.warning('Please enter a project name');
                    return;
                }

                try {
                    const project = await this.api.getProject(projectName);
                    this.displayResponse('Get Project', project, true);
                } catch (error) {
                    this.displayResponse('Get Project', error, false);
                }
            }

            async getTodos() {
                const projectName = document.getElementById('todo-project-name').value.trim();
                const status = document.getElementById('todo-status-filter').value;
                
                if (!projectName) {
                    TodoUtils.toast.warning('Please enter a project name');
                    return;
                }

                try {
                    const filters = status ? { status } : {};
                    const result = await this.api.getTodos(projectName, filters);
                    this.displayResponse('Get Todos', result, true);
                } catch (error) {
                    this.displayResponse('Get Todos', error, false);
                }
            }

            async getTodo() {
                const projectName = document.getElementById('todo-project-name').value.trim();
                const todoId = document.getElementById('todo-id').value.trim();
                
                if (!projectName || !todoId) {
                    TodoUtils.toast.warning('Please enter both project name and todo ID');
                    return;
                }

                try {
                    const todo = await this.api.getTodo(projectName, todoId);
                    this.displayResponse('Get Todo', todo, true);
                } catch (error) {
                    this.displayResponse('Get Todo', error, false);
                }
            }

            async createTodo() {
                const projectName = document.getElementById('todo-project-name').value.trim();
                const todoDataText = document.getElementById('new-todo-data').value.trim();
                
                if (!projectName || !todoDataText) {
                    TodoUtils.toast.warning('Please enter both project name and todo data');
                    return;
                }

                try {
                    const todoData = JSON.parse(todoDataText);
                    const result = await this.api.createTodo(projectName, todoData);
                    this.displayResponse('Create Todo', result, true);
                    TodoUtils.toast.success('Todo created successfully');
                } catch (error) {
                    if (error.name === 'SyntaxError') {
                        TodoUtils.toast.error('Invalid JSON in todo data');
                        this.displayResponse('Create Todo', { error: 'Invalid JSON format' }, false);
                    } else {
                        this.displayResponse('Create Todo', error, false);
                    }
                }
            }

            async markComplete() {
                const projectName = document.getElementById('todo-project-name').value.trim();
                const todoId = document.getElementById('todo-id').value.trim();
                
                if (!projectName || !todoId) {
                    TodoUtils.toast.warning('Please enter both project name and todo ID');
                    return;
                }

                try {
                    const result = await this.api.markTodoComplete(projectName, todoId);
                    this.displayResponse('Mark Complete', result, true);
                    TodoUtils.toast.success('Todo marked as complete');
                } catch (error) {
                    this.displayResponse('Mark Complete', error, false);
                }
            }

            async sendCustomRequest() {
                const method = document.getElementById('custom-method').value;
                const endpoint = document.getElementById('custom-endpoint').value.trim();
                const bodyText = document.getElementById('custom-body').value.trim();
                
                if (!endpoint) {
                    TodoUtils.toast.warning('Please enter an endpoint');
                    return;
                }

                try {
                    const options = { method };
                    
                    if (bodyText && ['POST', 'PUT', 'PATCH'].includes(method)) {
                        try {
                            const body = JSON.parse(bodyText);
                            options.body = JSON.stringify(body);
                        } catch (e) {
                            TodoUtils.toast.error('Invalid JSON in request body');
                            return;
                        }
                    }

                    const response = await this.api.request(endpoint, options);
                    this.displayResponse(`${method} ${endpoint}`, response, true);
                } catch (error) {
                    this.displayResponse(`${method} ${endpoint}`, error, false);
                }
            }

            displayResponse(operation, data, success) {
                const timestamp = new Date().toLocaleTimeString();
                const status = success ? 'SUCCESS' : 'ERROR';
                
                let displayData;
                if (data instanceof Error) {
                    displayData = {
                        error: data.message,
                        stack: data.stack
                    };
                } else {
                    displayData = data;
                }

                const responseText = `[${timestamp}] ${operation} - ${status}\n${JSON.stringify(displayData, null, 2)}\n\n`;
                
                this.responseContainer.textContent = responseText + this.responseContainer.textContent;
                this.responseContainer.className = `response-container ${success ? 'response-success' : 'response-error'}`;
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.apiTestConsole = new APITestConsole();
        });
    </script>
</body>
</html>