<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Details - Todo Management System</title>
    <link rel="stylesheet" href="css/style.css">
    <meta name="description" content="Project Details and Todo Management">
    <style>
        /* Additional styles specific to project detail page */
        .project-header {
            background: linear-gradient(135deg, var(--jomezpro-teal) 0%, var(--jomezpro-blue) 100%);
            color: var(--jomezpro-white);
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 2rem;
        }

        .header-info {
            flex: 1;
        }

        .project-header h1 {
            margin-bottom: 0.5rem;
            font-size: 2rem;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: flex-start;
        }

        .header-actions .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: var(--jomezpro-white);
            transition: all 0.2s ease;
        }

        .header-actions .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        /* Responsive header */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }
            
            .header-actions {
                justify-content: center;
            }
            
            .project-meta {
                gap: 1rem;
                font-size: 0.85rem;
            }
        }
        
        @media (max-width: 480px) {
            .project-meta {
                flex-wrap: wrap;
                gap: 0.75rem;
            }
        }

        .project-meta {
            display: flex;
            gap: 1.5rem;
            flex-wrap: nowrap;
            align-items: center;
            opacity: 0.9;
            white-space: nowrap;
            font-size: 0.9rem;
        }

        .project-meta span {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .todo-filters {
            background: var(--jomezpro-white);
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px var(--jomezpro-shadow);
        }

        .filter-bar {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-group label {
            font-weight: 500;
            color: var(--jomezpro-gray);
            white-space: nowrap;
        }

        .todo-actions {
            display: flex;
            gap: 1rem;
            margin-left: auto;
        }

        .todo-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .todo-item {
            background: var(--jomezpro-white);
            border-radius: 0.5rem;
            padding: .25rem;
            box-shadow: 0 2px 4px var(--jomezpro-shadow);
            border-left: 4px solid var(--jomezpro-border);
            transition: all 0.3s ease;
        }

        .todo-item:hover {
            box-shadow: 0 4px 8px var(--jomezpro-shadow);
        }

        .todo-item.status-pending {
            border-left-color: var(--jomezpro-warning);
        }

        .todo-item.status-in_progress {
            border-left-color: var(--jomezpro-info);
        }

        .todo-item.status-completed {
            border-left-color: var(--jomezpro-success);
            opacity: 0.8;
        }

        .todo-item.priority-critical {
            border-left-color: var(--jomezpro-error);
            border-left-width: 6px;
        }

        .todo-item.priority-high {
            border-left-width: 5px;
        }

        .todo-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            gap: 1rem;
        }

        .todo-title {
            flex: 1;
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--jomezpro-dark);
        }

        .todo-meta-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            align-items: flex-end;
            flex-shrink: 0;
        }

        .todo-status {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-pending {
            background: rgba(243, 156, 18, 0.1);
            color: var(--jomezpro-warning);
        }

        .status-in_progress {
            background: rgba(52, 152, 219, 0.1);
            color: var(--jomezpro-info);
        }

        .status-completed {
            background: rgba(39, 174, 96, 0.1);
            color: var(--jomezpro-success);
        }

        .todo-priority {
            font-size: 0.75rem;
            color: var(--jomezpro-light-gray);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .todo-description {
            color: var(--jomezpro-gray);
            line-height: 1.6;
            margin-bottom: 1rem;
            white-space: pre-wrap;
        }

        .todo-details {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .todo-detail {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: var(--jomezpro-light-gray);
        }

        .todo-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .todo-tag {
            background: var(--jomezpro-bg);
            color: var(--jomezpro-gray);
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
        }

        .todo-subtasks {
            margin-bottom: 1rem;
        }

        .todo-subtasks h4 {
            font-size: 0.875rem;
            color: var(--jomezpro-gray);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .subtask-list {
            list-style: none;
            padding: 0;
        }

        .subtask-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0;
            font-size: 0.875rem;
        }

        .subtask-checkbox {
            width: 1rem;
            height: 1rem;
            border: 2px solid var(--jomezpro-border);
            border-radius: 0.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .subtask-checkbox.completed {
            background: var(--jomezpro-success);
            border-color: var(--jomezpro-success);
            color: var(--jomezpro-white);
        }

        .subtask-text {
            flex: 1;
        }

        .subtask-text.completed {
            text-decoration: line-through;
            color: var(--jomezpro-light-gray);
        }

        .todo-actions-inline {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
        }

        .btn-success {
            background: var(--jomezpro-success);
            color: var(--jomezpro-white);
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: var(--jomezpro-warning);
            color: var(--jomezpro-white);
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: var(--jomezpro-error);
            color: var(--jomezpro-white);
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .empty-todos {
            text-align: center;
            padding: 4rem 2rem;
            background: var(--jomezpro-white);
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px var(--jomezpro-shadow);
        }

        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-box {
            background: var(--jomezpro-white);
            border-radius: 0.5rem;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 4px var(--jomezpro-shadow);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--jomezpro-teal);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--jomezpro-light-gray);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .bulk-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .selected-count {
            padding: 0.75rem 1rem;
            background: var(--jomezpro-bg);
            border-radius: 0.375rem;
            font-size: 0.875rem;
            color: var(--jomezpro-gray);
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(2px);
        }

        .modal.hidden {
            display: none;
        }

        .modal-content {
            background: var(--jomezpro-white);
            border-radius: 0.75rem;
            max-width: 700px;
            width: 95%;
            max-height: 85vh;
            overflow: hidden;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            border: 2px solid var(--jomezpro-teal);
        }

        .modal-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--jomezpro-teal) 0%, var(--jomezpro-blue) 100%);
            color: var(--jomezpro-white);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-weight: 600;
        }

        .modal-close {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 0.375rem;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            color: var(--jomezpro-white);
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .modal-body {
            padding: 0;
            max-height: calc(85vh - 160px);
            overflow-y: auto;
        }

        .modal-footer {
            padding: 1.5rem;
            background: var(--jomezpro-bg);
            border-top: 1px solid var(--jomezpro-border);
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        /* Modal Guard Styles */
        .modal-guard {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(231, 76, 60, 0.1);
            z-index: 999;
            display: none;
            animation: pulseWarning 1s ease-in-out;
        }

        .modal-guard.show {
            display: block;
        }

        @keyframes pulseWarning {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }

        .guard-warning {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--jomezpro-white);
            border: 2px solid var(--jomezpro-error);
            border-radius: 0.5rem;
            padding: 2rem;
            max-width: 400px;
            text-align: center;
            box-shadow: 0 20px 50px rgba(231, 76, 60, 0.3);
            z-index: 1001;
        }

        .guard-warning h3 {
            color: var(--jomezpro-error);
            margin-bottom: 1rem;
        }

        .guard-warning p {
            margin-bottom: 1.5rem;
            color: var(--jomezpro-gray);
        }

        .guard-warning .btn {
            margin: 0 0.5rem;
        }

        /* Tab Styles */
        .modal-tabs {
            display: flex;
            background: var(--jomezpro-bg);
            border-bottom: 1px solid var(--jomezpro-border);
        }

        .tab-button {
            flex: 1;
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            color: var(--jomezpro-gray);
            transition: all 0.2s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-button:hover {
            background: rgba(0, 196, 170, 0.1);
            color: var(--jomezpro-teal);
        }

        .tab-button.active {
            background: var(--jomezpro-white);
            color: var(--jomezpro-teal);
            border-bottom-color: var(--jomezpro-teal);
        }

        .tab-content {
            display: none;
            padding: 1.5rem;
        }

        .tab-content.active {
            display: block;
        }

        .form-field {
            margin-bottom: 1.5rem;
        }

        .form-field label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--jomezpro-dark);
            font-size: 0.875rem;
        }

        .form-field input,
        .form-field select,
        .form-field textarea {
            width: 100%;
            padding: 0.875rem;
            border: 2px solid var(--jomezpro-border);
            border-radius: 0.5rem;
            font-size: 1rem;
            box-sizing: border-box;
            transition: all 0.2s ease;
            background: var(--jomezpro-white);
        }

        .form-field input:focus,
        .form-field select:focus,
        .form-field textarea:focus {
            outline: none;
            border-color: var(--jomezpro-teal);
            box-shadow: 0 0 0 4px rgba(0, 196, 170, 0.15);
            transform: translateY(-1px);
        }

        .form-field textarea {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
        }

        .form-field small {
            display: block;
            margin-top: 0.5rem;
            color: var(--jomezpro-light-gray);
            font-size: 0.75rem;
        }

        .dependencies-list {
            max-height: 200px;
            overflow-y: auto;
            border: 2px solid var(--jomezpro-border);
            border-radius: 0.5rem;
            background: var(--jomezpro-white);
        }

        .dependency-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid var(--jomezpro-bg);
            transition: background-color 0.2s ease;
        }

        .dependency-item:hover {
            background: var(--jomezpro-bg);
        }

        .dependency-item:last-child {
            border-bottom: none;
        }

        .dependency-checkbox {
            margin-right: 0.75rem;
            transform: scale(1.2);
        }

        .dependency-info {
            flex: 1;
        }

        .dependency-title {
            font-weight: 500;
            color: var(--jomezpro-dark);
            margin-bottom: 0.25rem;
        }

        .dependency-meta {
            font-size: 0.75rem;
            color: var(--jomezpro-light-gray);
        }

        .dependency-status {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.625rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-left: 0.5rem;
        }

        .dependency-status.pending {
            background: rgba(243, 156, 18, 0.1);
            color: var(--jomezpro-warning);
        }

        .dependency-status.completed {
            background: rgba(39, 174, 96, 0.1);
            color: var(--jomezpro-success);
        }

        .dependency-status.in_progress {
            background: rgba(52, 152, 219, 0.1);
            color: var(--jomezpro-info);
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <h1><a href="index.html" style="text-decoration: none; color: inherit;">📋 Todo Management System</a></h1>
            <nav class="main-nav" role="navigation" aria-label="Main navigation">
                <ul>
                    <li><a href="today.html">Today</a></li>
                    <li><a href="weekly.html">Weekly</a></li>
                    <li><a href="workflow.html">Workflows</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Loading State -->
    <div id="loading-state" class="loading-state" aria-live="polite">
        <div class="loading-spinner"></div>
        <p>Loading project details...</p>
    </div>

    <!-- Error State -->
    <div id="error-state" class="error-state hidden" role="alert" aria-live="assertive">
        <div class="error-icon">⚠️</div>
        <h2>Unable to Load Project</h2>
        <p id="error-message">Please check if the project exists and the API server is running.</p>
        <button id="retry-btn" class="btn btn-primary">Retry</button>
        <button id="back-btn" class="btn btn-secondary">Back to Dashboard</button>
    </div>

    <!-- Project Content -->
    <div id="project-content" class="hidden">
        <!-- Project Header -->
        <div class="project-header">
            <div class="container">
                <div class="header-content">
                    <div class="header-info">
                        <h1 id="project-name">Project Name</h1>
                        <div class="project-meta">
                            <span>📁 <span id="project-path">Project Path</span></span>
                            <span>📊 <span id="project-stats">0 todos</span></span>
                            <span>🕒 <span id="project-updated">Last updated</span></span>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button id="generate-report-btn" class="btn btn-secondary" title="Generate Progress Report">
                            📊 Progress Report
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <main class="main-content">
            <div class="container">
                <!-- Statistics Summary -->
                <div class="stats-summary">
                    <div class="stat-box">
                        <div id="total-count" class="stat-number">0</div>
                        <div class="stat-label">Total Todos</div>
                    </div>
                    <div class="stat-box">
                        <div id="completed-count" class="stat-number">0</div>
                        <div class="stat-label">Completed</div>
                    </div>
                    <div class="stat-box">
                        <div id="pending-count" class="stat-number">0</div>
                        <div class="stat-label">Pending</div>
                    </div>
                    <div class="stat-box">
                        <div id="completion-percentage" class="stat-number">0%</div>
                        <div class="stat-label">Completion Rate</div>
                    </div>
                </div>

                <!-- Filters and Actions -->
                <div class="todo-filters">
                    <div class="filter-bar">
                        <div class="filter-group">
                            <label for="status-filter">Status:</label>
                            <select id="status-filter">
                                <option value="">All</option>
                                <option value="pending">Pending</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="urgency-filter">Urgency:</label>
                            <select id="urgency-filter">
                                <option value="">All</option>
                                <option value="critical">Critical</option>
                                <option value="high">High</option>
                                <option value="medium">Medium</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="priority-filter">Priority:</label>
                            <select id="priority-filter">
                                <option value="">All</option>
                                <option value="critical">Critical</option>
                                <option value="high">High</option>
                                <option value="medium">Medium</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="category-filter">Category:</label>
                            <select id="category-filter">
                                <option value="">All</option>
                                <!-- Dynamically populated -->
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="search-filter">Search:</label>
                            <input type="search" id="search-filter" placeholder="Search todos...">
                        </div>
                        <div class="todo-actions">
                            <button id="refresh-todos-btn" class="btn btn-secondary btn-icon" title="Refresh Todos">
                                🔄
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div id="bulk-actions" class="bulk-actions hidden">
                    <div class="selected-count">
                        <span id="selected-count">0</span> todos selected
                    </div>
                    <button id="bulk-complete-btn" class="btn btn-success btn-small">
                        ✅ Mark Complete
                    </button>
                    <button id="bulk-pending-btn" class="btn btn-warning btn-small">
                        ⏳ Mark Pending
                    </button>
                    <button id="clear-selection-btn" class="btn btn-secondary btn-small">
                        Clear Selection
                    </button>
                </div>

                <!-- Pagination Controls -->
                <div id="pagination-controls" class="pagination-controls">
                    <div class="pagination-left">
                        <button id="add-todo-btn" class="btn btn-primary">
                            ➕ Add Todo
                        </button>
                    </div>
                    <div class="pagination-center">
                        <div class="pagination-info">
                            <span id="pagination-info-text">Showing 1-10 of 0 todos</span>
                        </div>
                        <div class="pagination-size">
                            <label for="page-size-select">Show:</label>
                            <select id="page-size-select">
                                <option value="10" selected>10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                                <option value="all">All</option>
                            </select>
                            <span>per page</span>
                        </div>
                    </div>
                    <div class="pagination-nav">
                        <button id="prev-page-btn" class="btn btn-secondary" disabled>◀ Previous</button>
                        <span id="page-numbers"></span>
                        <button id="next-page-btn" class="btn btn-secondary" disabled>Next ▶</button>
                    </div>
                </div>

                <!-- Todo List -->
                <div id="todo-list" class="todo-list">
                    <!-- Todos will be dynamically inserted here -->
                </div>

                <!-- Pagination Controls (Bottom) -->
                <div id="pagination-controls-bottom" class="pagination-controls pagination-controls-bottom">
                    <div class="pagination-left">
                        <!-- Empty space to maintain layout balance -->
                    </div>
                    <div class="pagination-center">
                        <div class="pagination-info">
                            <span id="pagination-info-text-bottom">Showing 1-10 of 0 todos</span>
                        </div>
                        <div class="pagination-size">
                            <label for="page-size-select-bottom">Show:</label>
                            <select id="page-size-select-bottom">
                                <option value="10" selected>10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                                <option value="all">All</option>
                            </select>
                            <span>per page</span>
                        </div>
                    </div>
                    <div class="pagination-nav">
                        <button id="prev-page-btn-bottom" class="btn btn-secondary" disabled>◀ Previous</button>
                        <span id="page-numbers-bottom"></span>
                        <button id="next-page-btn-bottom" class="btn btn-secondary" disabled>Next ▶</button>
                    </div>
                </div>

                <!-- Empty State -->
                <div id="empty-todos" class="empty-todos hidden">
                    <div class="empty-icon">📝</div>
                    <h3>No Todos Found</h3>
                    <p>No todos match the current filters, or this project has no todos yet.</p>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal Backdrop Guard -->
    <div id="modal-guard" class="modal-guard">
        <div class="guard-warning">
            <h3>⚠️ Save Failed</h3>
            <p>Could not save your changes automatically. Would you like to try again or close without saving?</p>
            <button id="guard-continue-btn" class="btn btn-danger">Close Without Saving</button>
            <button id="guard-cancel-btn" class="btn btn-primary">Keep Editing</button>
        </div>
    </div>

    <!-- Edit/Add Todo Modal -->
    <div id="todo-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">✨ Add New Todo</h2>
                <button class="modal-close" id="modal-close-btn">&times;</button>
            </div>
            
            <div class="modal-tabs">
                <button class="tab-button active" data-tab="basic">📝 Basic Info</button>
                <button class="tab-button" data-tab="advanced">⚙️ Advanced</button>
            </div>
            
            <div class="modal-body">
                <form id="todo-form">
                    <!-- Basic Tab -->
                    <div id="basic-tab" class="tab-content active">
                        <div class="form-field">
                            <label for="todo-title-input">Title *</label>
                            <input type="text" id="todo-title-input" required placeholder="Enter todo title...">
                            <small>A clear, concise description of what needs to be done</small>
                        </div>
                        
                        <div class="form-field">
                            <label for="todo-description-input">Description</label>
                            <textarea id="todo-description-input" placeholder="Provide detailed information about this task..."></textarea>
                            <small>Include any relevant context, requirements, or notes</small>
                        </div>
                        
                        <div class="form-field">
                            <label for="todo-start-date-input">Start Date</label>
                            <input type="date" id="todo-start-date-input">
                            <small>When should work on this task begin?</small>
                        </div>
                        
                        <div class="form-field">
                            <label for="todo-due-date-input">Due Date</label>
                            <input type="date" id="todo-due-date-input">
                            <small>When should this task be completed?</small>
                        </div>
                        
                        <div class="form-field">
                            <label for="todo-dependencies-input">Dependencies</label>
                            <div class="dependencies-list" id="dependencies-list">
                                <!-- Dependencies will be populated dynamically -->
                            </div>
                            <small>Select todos that must be completed before this task can start</small>
                        </div>
                    </div>
                    
                    <!-- Advanced Tab -->
                    <div id="advanced-tab" class="tab-content">
                        <div class="form-field">
                            <label for="todo-priority-input">Priority</label>
                            <select id="todo-priority-input">
                                <option value="low">🔵 Low</option>
                                <option value="medium" selected>🟡 Medium</option>
                                <option value="high">🟠 High</option>
                                <option value="critical">🔴 Critical</option>
                            </select>
                            <small>How important is this task?</small>
                        </div>
                        
                        <div class="form-field">
                            <label for="todo-urgency-input">Urgency</label>
                            <select id="todo-urgency-input">
                                <option value="low">🟢 Low</option>
                                <option value="medium" selected>🟡 Medium</option>
                                <option value="high">🟠 High</option>
                                <option value="critical">🔴 Critical</option>
                            </select>
                            <small>How time-sensitive is this task?</small>
                        </div>
                        
                        <div class="form-field">
                            <label for="todo-category-input">Category *</label>
                            <select id="todo-category-input" required>
                                <option value="">Select category...</option>
                                <option value="analysis">🔍 Analysis</option>
                                <option value="architecture">🏗️ Architecture</option>
                                <option value="development" selected>💻 Development</option>
                                <option value="testing">🧪 Testing</option>
                                <option value="uat">👥 User Acceptance Testing</option>
                                <option value="release">🚀 Release</option>
                            </select>
                            <small>What type of work does this involve?</small>
                        </div>
                        
                        <div class="form-field">
                            <label for="todo-estimated-hours-input">Estimated Hours</label>
                            <input type="number" id="todo-estimated-hours-input" min="0" step="0.5" placeholder="0.5">
                            <small>How long do you expect this task to take?</small>
                        </div>
                        
                        <div class="form-field">
                            <label for="todo-assigned-to-input">Assigned To</label>
                            <input type="text" id="todo-assigned-to-input" placeholder="Team member name...">
                            <small>Who is responsible for completing this task?</small>
                        </div>
                        
                        <div class="form-field">
                            <label for="todo-tags-input">Tags</label>
                            <input type="text" id="todo-tags-input" placeholder="frontend, api, urgent, review">
                            <small>Comma-separated tags for organization and filtering</small>
                        </div>
                    </div>
                </form>
            </div>
            
            <div class="modal-footer">
                <button id="modal-cancel-btn" class="btn btn-secondary">Cancel</button>
                <button id="modal-save-btn" class="btn btn-primary">💾 Save Todo</button>
            </div>
        </div>
    </div>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2025 Todo Management System | <a href="index.html">Dashboard</a> | <a href="api-test.html">API Test</a> | <a href="today.html">Today</a></p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script>
        // Project Detail Page Implementation
        class ProjectDetailPage {
            constructor() {
                this.api = window.todoAPI;
                this.projectName = this.getProjectName();
                this.todos = [];
                this.filteredTodos = [];
                this.selectedTodos = new Set();
                this.pagination = {
                    currentPage: 1,
                    pageSize: 10,
                    totalPages: 1,
                    totalItems: 0
                };
                this.filters = {
                    status: '',
                    priority: '',
                    category: '',
                    search: ''
                };

                // DOM elements
                this.elements = {
                    loadingState: document.getElementById('loading-state'),
                    errorState: document.getElementById('error-state'),
                    projectContent: document.getElementById('project-content'),
                    todoList: document.getElementById('todo-list'),
                    emptyTodos: document.getElementById('empty-todos'),
                    bulkActions: document.getElementById('bulk-actions'),
                    selectedCount: document.getElementById('selected-count')
                };

                this.init();
            }

            getProjectName() {
                const params = new URLSearchParams(window.location.search);
                return params.get('project') || '';
            }

            async init() {
                if (!this.projectName) {
                    this.showError('No project specified');
                    return;
                }

                this.setupEventListeners();
                await this.loadProject();
            }

            setupEventListeners() {
                // Filter changes
                document.getElementById('status-filter').addEventListener('change', (e) => {
                    this.filters.status = e.target.value;
                    this.applyFilters();
                });

                document.getElementById('urgency-filter').addEventListener('change', (e) => {
                    this.filters.urgency = e.target.value;
                    this.applyFilters();
                });

                document.getElementById('priority-filter').addEventListener('change', (e) => {
                    this.filters.priority = e.target.value;
                    this.applyFilters();
                });

                document.getElementById('category-filter').addEventListener('change', (e) => {
                    this.filters.category = e.target.value;
                    this.applyFilters();
                });

                document.getElementById('search-filter').addEventListener('input', 
                    TodoUtils.dom.debounce((e) => {
                        this.filters.search = e.target.value.toLowerCase();
                        this.applyFilters();
                    }, 300)
                );

                // Pagination controls (top)
                document.getElementById('page-size-select').addEventListener('change', (e) => {
                    this.changePageSize(e.target.value);
                });

                document.getElementById('prev-page-btn').addEventListener('click', () => {
                    if (this.pagination.currentPage > 1) {
                        this.goToPage(this.pagination.currentPage - 1);
                    }
                });

                document.getElementById('next-page-btn').addEventListener('click', () => {
                    if (this.pagination.currentPage < this.pagination.totalPages) {
                        this.goToPage(this.pagination.currentPage + 1);
                    }
                });

                // Pagination controls (bottom)
                document.getElementById('page-size-select-bottom').addEventListener('change', (e) => {
                    this.changePageSize(e.target.value);
                });

                document.getElementById('prev-page-btn-bottom').addEventListener('click', () => {
                    if (this.pagination.currentPage > 1) {
                        this.goToPage(this.pagination.currentPage - 1);
                    }
                });

                document.getElementById('next-page-btn-bottom').addEventListener('click', () => {
                    if (this.pagination.currentPage < this.pagination.totalPages) {
                        this.goToPage(this.pagination.currentPage + 1);
                    }
                });

                // Actions
                document.getElementById('refresh-todos-btn').addEventListener('click', () => {
                    this.loadProject();
                });

                document.getElementById('retry-btn').addEventListener('click', () => {
                    this.loadProject();
                });

                document.getElementById('back-btn').addEventListener('click', () => {
                    window.location.href = 'index.html';
                });

                // Progress report button
                document.getElementById('generate-report-btn').addEventListener('click', () => {
                    this.generateProgressReport();
                });

                // Bulk actions
                document.getElementById('bulk-complete-btn').addEventListener('click', () => {
                    this.bulkUpdateStatus('completed');
                });

                document.getElementById('bulk-pending-btn').addEventListener('click', () => {
                    this.bulkUpdateStatus('pending');
                });

                document.getElementById('clear-selection-btn').addEventListener('click', () => {
                    this.clearSelection();
                });

                // Add todo button
                document.getElementById('add-todo-btn').addEventListener('click', () => {
                    this.showAddTodoModal();
                });

                // Modal events
                document.getElementById('modal-close-btn').addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.tryCloseModal();
                });

                document.getElementById('modal-cancel-btn').addEventListener('click', () => {
                    this.tryCloseModal();
                });

                document.getElementById('modal-save-btn').addEventListener('click', () => {
                    this.saveTodo();
                });

                // Modal guard events
                document.getElementById('guard-continue-btn').addEventListener('click', () => {
                    this.hideModalGuard();
                    this.hideModal(true); // Force close
                });

                document.getElementById('guard-cancel-btn').addEventListener('click', () => {
                    this.hideModalGuard();
                });

                // Tab switching
                document.querySelectorAll('.tab-button').forEach(button => {
                    button.addEventListener('click', () => {
                        this.switchTab(button.dataset.tab);
                    });
                });

                // Close modal on background click
                document.getElementById('todo-modal').addEventListener('click', (e) => {
                    if (e.target.id === 'todo-modal') {
                        this.tryCloseModal();
                    }
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        // Check if modal is open first
                        if (!document.getElementById('todo-modal').classList.contains('hidden')) {
                            e.preventDefault();
                            this.tryCloseModal();
                        } else if (document.getElementById('modal-guard').classList.contains('show')) {
                            e.preventDefault();
                            this.hideModalGuard();
                        } else {
                            this.clearSelection();
                        }
                    }
                    if (e.ctrlKey && e.key === 'a') {
                        e.preventDefault();
                        this.selectAll();
                    }
                });
            }

            async loadProject() {
                this.showLoading();
                
                try {
                    // Load project details and todos in parallel
                    const [project, todosData] = await Promise.all([
                        this.api.getProject(this.projectName),
                        this.api.getTodos(this.projectName)
                    ]);

                    this.project = project;
                    this.todos = todosData.todos || [];
                    
                    this.updateProjectHeader();
                    this.updateStatistics();
                    this.populateFilters();
                    this.applyFilters();
                    this.showProjectContent();
                    
                    TodoUtils.toast.success(`Loaded ${this.todos.length} todos`);
                    
                } catch (error) {
                    console.error('Error loading project:', error);
                    this.showError(error.message);
                }
            }

            showLoading() {
                TodoUtils.dom.toggle(this.elements.loadingState, true);
                TodoUtils.dom.toggle(this.elements.errorState, false);
                TodoUtils.dom.toggle(this.elements.projectContent, false);
            }

            showError(message) {
                document.getElementById('error-message').textContent = message;
                TodoUtils.dom.toggle(this.elements.loadingState, false);
                TodoUtils.dom.toggle(this.elements.errorState, true);
                TodoUtils.dom.toggle(this.elements.projectContent, false);
            }

            showProjectContent() {
                TodoUtils.dom.toggle(this.elements.loadingState, false);
                TodoUtils.dom.toggle(this.elements.errorState, false);
                TodoUtils.dom.toggle(this.elements.projectContent, true);
            }

            updateProjectHeader() {
                // Use display name if available, otherwise use project name
                const displayName = this.project.metadata?.display_name || this.project.name;
                document.getElementById('project-name').textContent = displayName;
                
                // Format the project path with source root replacement and truncation
                const rawPath = this.project.path || 'Unknown';
                const formattedPath = this.formatProjectPath(rawPath);
                document.getElementById('project-path').textContent = formattedPath;
                
                document.getElementById('project-stats').textContent = `${this.todos.length} todos`;
                
                const lastUpdated = this.project.metadata?.last_updated;
                document.getElementById('project-updated').textContent = lastUpdated 
                    ? TodoUtils.formatters.relativeTime(lastUpdated)
                    : 'Unknown';
            }

            formatProjectPath(path) {
                if (!path || path === 'Unknown') {
                    return path;
                }
                
                // Replace the source root with placeholder
                const sourceRoot = 'C:\\Users\\<USER>\\source\\repos';
                let formattedPath = path.replace(sourceRoot, '{source root}');
                
                // Also handle forward slashes (in case of mixed path separators)
                formattedPath = formattedPath.replace('C:/Users/<USER>/source/repos', '{source root}');
                
                // Calculate truncation threshold (50% longer than the example)
                const exampleLength = 'C:\\Users\\<USER>\\source\\repos\\AccountingCrmInterfaces'.length;
                const maxLength = Math.floor(exampleLength * 1.5);
                
                // If path is too long, truncate from the beginning
                if (formattedPath.length > maxLength) {
                    const truncatedLength = formattedPath.length - maxLength + 3; // +3 for "..."
                    formattedPath = '...' + formattedPath.substring(truncatedLength);
                }
                
                return formattedPath;
            }

            updateStatistics() {
                const activeTodos = this.todos.filter(t => t.status !== 'deleted');
                const total = activeTodos.length;
                const completed = activeTodos.filter(t => t.status === 'completed').length;
                const pending = activeTodos.filter(t => t.status === 'pending').length;
                const completionRate = total > 0 ? (completed / total * 100) : 0;

                document.getElementById('total-count').textContent = total;
                document.getElementById('completed-count').textContent = completed;
                document.getElementById('pending-count').textContent = pending;
                document.getElementById('completion-percentage').textContent = TodoUtils.formatters.percentage(completionRate);
            }

            populateFilters() {
                // Populate category filter
                const categories = [...new Set(this.todos.map(t => t.category).filter(Boolean))];
                const categoryFilter = document.getElementById('category-filter');
                
                // Clear existing options except "All"
                categoryFilter.innerHTML = '<option value="">All</option>';
                
                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category.charAt(0).toUpperCase() + category.slice(1);
                    categoryFilter.appendChild(option);
                });
            }

            applyFilters() {
                this.filteredTodos = this.todos.filter(todo => {
                    // Always filter out deleted todos
                    if (todo.status === 'deleted') {
                        return false;
                    }

                    // Status filter
                    if (this.filters.status && todo.status !== this.filters.status) {
                        return false;
                    }

                    // Urgency filter
                    if (this.filters.urgency && todo.urgency !== this.filters.urgency) {
                        return false;
                    }

                    // Priority filter
                    if (this.filters.priority && todo.priority !== this.filters.priority) {
                        return false;
                    }

                    // Category filter
                    if (this.filters.category && todo.category !== this.filters.category) {
                        return false;
                    }

                    // Search filter
                    if (this.filters.search) {
                        const searchText = this.filters.search;
                        const todoText = `${todo.title} ${todo.description || ''}`.toLowerCase();
                        if (!todoText.includes(searchText)) {
                            return false;
                        }
                    }

                    return true;
                });

                // Sort by list_order (if available), then by urgency/priority
                this.filteredTodos.sort((a, b) => {
                    // Primary sort: list_order (lower numbers first)
                    if (a.list_order !== undefined && b.list_order !== undefined) {
                        return (a.list_order || 0) - (b.list_order || 0);
                    }
                    
                    // If no list_order, fall back to urgency/priority sorting
                    const aScore = this.getUrgencyScore(a.urgency || 'medium') + this.getUrgencyScore(a.priority || 'medium');
                    const bScore = this.getUrgencyScore(b.urgency || 'medium') + this.getUrgencyScore(b.priority || 'medium');
                    
                    if (aScore !== bScore) {
                        return bScore - aScore; // Higher scores first
                    }
                    
                    // Final sort: alphabetical by title
                    return (a.title || '').localeCompare(b.title || '');
                });

                // Reset pagination to first page when filters change
                this.pagination.currentPage = 1;
                
                this.renderTodos();
            }

            renderTodos() {
                // Update pagination info
                this.updatePagination();
                
                if (this.filteredTodos.length === 0) {
                    TodoUtils.dom.toggle(this.elements.todoList, false);
                    TodoUtils.dom.toggle(this.elements.emptyTodos, true);
                    document.getElementById('pagination-controls').style.display = 'none';
                    document.getElementById('pagination-controls-bottom').style.display = 'none';
                    return;
                }

                TodoUtils.dom.toggle(this.elements.todoList, true);
                TodoUtils.dom.toggle(this.elements.emptyTodos, false);
                document.getElementById('pagination-controls').style.display = 'flex';
                document.getElementById('pagination-controls-bottom').style.display = 'flex';

                // Get paginated todos
                const paginatedTodos = this.getPaginatedTodos();
                
                this.elements.todoList.innerHTML = '';

                paginatedTodos.forEach(todo => {
                    const todoElement = this.createTodoElement(todo);
                    this.elements.todoList.appendChild(todoElement);
                });
                
                // Update pagination controls
                this.renderPaginationControls();
            }

            createTodoElement(todo) {
                const todoDiv = TodoUtils.dom.createElement('div', {
                    className: `todo-item status-${todo.status} priority-${todo.priority} collapsed`,
                    'data-todo-id': todo.id
                });

                // Complete button (replaces checkbox)
                const completeBtn = TodoUtils.dom.createElement('button', {
                    className: `todo-complete-btn ${todo.status === 'completed' ? 'completed' : ''}`,
                    'aria-label': `Mark ${todo.title} as ${todo.status === 'completed' ? 'pending' : 'completed'}`
                });
                
                // Icon: checkbox for uncompleted, checkmark for completed
                completeBtn.innerHTML = todo.status === 'completed' ? '☑️' : '☐';
                
                // Order controls
                const orderControls = TodoUtils.dom.createElement('div', {
                    className: 'todo-order-controls'
                }, `
                    <button class="btn-order btn-order-up" data-todo-id="${todo.id}" title="Move up">▲</button>
                    <button class="btn-order btn-order-down" data-todo-id="${todo.id}" title="Move down">▼</button>
                `);

                // Collapsed view (always visible)
                const collapsedView = TodoUtils.dom.createElement('div', {
                    className: 'todo-collapsed-view'
                }, `
                    <div class="todo-title-row">
                        <h3 class="todo-title">${todo.title}</h3>
                        <div class="todo-status-badges">
                            <span class="todo-status status-${todo.status}">${TodoUtils.formatters.statusLabel(todo.status)}</span>
                        </div>
                    </div>
                `);

                // Expanded view (hidden by default)
                const expandedView = TodoUtils.dom.createElement('div', {
                    className: 'todo-expanded-view hidden'
                }, `
                    <div class="todo-item-header">
                        <div class="todo-meta-info">
                            <span class="todo-priority">${TodoUtils.formatters.priorityLabel(todo.priority)}</span>
                            <span class="todo-urgency">${TodoUtils.formatters.priorityLabel(todo.urgency || 'medium')}</span>
                        </div>
                    </div>

                    ${todo.description ? `<div class="todo-description">${todo.description}</div>` : ''}

                    <div class="todo-details">
                        ${todo.category ? `<div class="todo-detail">📂 ${todo.category}</div>` : ''}
                        ${todo.estimated_hours ? `<div class="todo-detail">⏱️ ${todo.estimated_hours}h estimated</div>` : ''}
                        ${todo.start_date ? `<div class="todo-detail">🚀 Start ${new Date(todo.start_date).toLocaleDateString()}</div>` : ''}
                        ${todo.due_date ? `<div class="todo-detail">📅 Due ${new Date(todo.due_date).toLocaleDateString()}</div>` : ''}
                        ${todo.assigned_to ? `<div class="todo-detail">👤 ${todo.assigned_to}</div>` : ''}
                    </div>

                    ${todo.tags && todo.tags.length > 0 ? `
                    <div class="todo-tags">
                        ${todo.tags.map(tag => `<span class="todo-tag">${tag}</span>`).join('')}
                    </div>
                    ` : ''}

                    ${todo.subtasks && todo.subtasks.length > 0 ? `
                    <div class="todo-subtasks">
                        <h4>Subtasks</h4>
                        <ul class="subtask-list">
                            ${todo.subtasks.map(subtask => `
                                <li class="subtask-item">
                                    <div class="subtask-checkbox ${subtask.completed ? 'completed' : ''}">
                                        ${subtask.completed ? '✓' : ''}
                                    </div>
                                    <span class="subtask-text ${subtask.completed ? 'completed' : ''}">${subtask.text}</span>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                    ` : ''}

                    <div class="todo-actions-expanded">
                        <div class="todo-actions-row">
                            <div class="todo-actions-left">
                                <div class="status-group">
                                    <label class="status-label">Status:</label>
                                    <select class="todo-status-dropdown" data-todo-id="${todo.id}">
                                        <option value="pending" ${todo.status === 'pending' ? 'selected' : ''}>⏳ Pending</option>
                                        <option value="in_progress" ${todo.status === 'in_progress' ? 'selected' : ''}>🔄 In Progress</option>
                                        <option value="completed" ${todo.status === 'completed' ? 'selected' : ''}>✅ Completed</option>
                                        <option value="blocked" ${todo.status === 'blocked' ? 'selected' : ''}>🚫 Blocked</option>
                                        <option value="cancelled" ${todo.status === 'cancelled' ? 'selected' : ''}>❌ Cancelled</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary btn-small edit-todo-btn" data-todo-id="${todo.id}">
                                    ✏️ Edit
                                </button>
                            </div>
                            <div class="todo-actions-right">
                                <button class="btn btn-danger btn-small delete-todo-btn" data-todo-id="${todo.id}">
                                    🗑️ Delete
                                </button>
                            </div>
                        </div>
                    </div>
                `);

                // Todo content wrapper
                const content = TodoUtils.dom.createElement('div', {
                    className: 'todo-content'
                });
                
                content.appendChild(collapsedView);
                content.appendChild(expandedView);

                // Main wrapper
                const wrapper = TodoUtils.dom.createElement('div', {
                    className: 'todo-wrapper'
                });

                wrapper.appendChild(completeBtn);
                wrapper.appendChild(content);
                wrapper.appendChild(orderControls);

                todoDiv.appendChild(wrapper);

                // Add event listeners
                this.setupNewTodoActions(todoDiv, todo.id);

                return todoDiv;
            }

            setupNewTodoActions(todoElement, todoId) {
                // Complete button
                const completeBtn = todoElement.querySelector('.todo-complete-btn');
                if (completeBtn) {
                    completeBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        const todo = this.todos.find(t => t.id === todoId);
                        const newStatus = todo.status === 'completed' ? 'pending' : 'completed';
                        this.updateTodoStatus(todoId, newStatus);
                    });
                }

                // Click to expand (anywhere except buttons)
                const collapsedView = todoElement.querySelector('.todo-collapsed-view');
                if (collapsedView) {
                    collapsedView.addEventListener('click', () => {
                        this.toggleTodoExpansion(todoId);
                    });
                }

                // Edit button
                const editBtn = todoElement.querySelector('.edit-todo-btn');
                if (editBtn) {
                    editBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.showEditTodoModal(todoId);
                    });
                }

                // Status dropdown
                const statusDropdown = todoElement.querySelector('.todo-status-dropdown');
                if (statusDropdown) {
                    statusDropdown.addEventListener('change', (e) => {
                        e.stopPropagation();
                        this.updateTodoStatus(todoId, e.target.value);
                    });
                }

                // Delete button
                const deleteBtn = todoElement.querySelector('.delete-todo-btn');
                if (deleteBtn) {
                    deleteBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.deleteTodo(todoId);
                    });
                }

                // Order controls
                const upBtn = todoElement.querySelector('.btn-order-up');
                const downBtn = todoElement.querySelector('.btn-order-down');
                
                if (upBtn) {
                    upBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.moveTodoUp(todoId);
                    });
                }
                
                if (downBtn) {
                    downBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.moveTodoDown(todoId);
                    });
                }
            }

            toggleTodoExpansion(todoId) {
                const todoElement = document.querySelector(`[data-todo-id="${todoId}"]`);
                const expandedView = todoElement.querySelector('.todo-expanded-view');
                
                if (expandedView) {
                    const isExpanded = !expandedView.classList.contains('hidden');
                    
                    if (isExpanded) {
                        // Collapse
                        expandedView.classList.add('hidden');
                        todoElement.classList.add('collapsed');
                    } else {
                        // Expand
                        expandedView.classList.remove('hidden');
                        todoElement.classList.remove('collapsed');
                    }
                }
            }

            updatePagination() {
                this.pagination.totalItems = this.filteredTodos.length;
                
                if (this.pagination.pageSize === 'all' || this.pagination.pageSize === 'unlimited') {
                    this.pagination.totalPages = 1;
                    this.pagination.currentPage = 1;
                } else {
                    const pageSize = parseInt(this.pagination.pageSize);
                    this.pagination.totalPages = Math.ceil(this.pagination.totalItems / pageSize);
                    
                    // Ensure current page is valid
                    if (this.pagination.currentPage > this.pagination.totalPages) {
                        this.pagination.currentPage = Math.max(1, this.pagination.totalPages);
                    }
                }
            }

            getPaginatedTodos() {
                if (this.pagination.pageSize === 'all' || this.pagination.pageSize === 'unlimited') {
                    return this.filteredTodos;
                }
                
                const pageSize = parseInt(this.pagination.pageSize);
                const startIndex = (this.pagination.currentPage - 1) * pageSize;
                const endIndex = startIndex + pageSize;
                
                return this.filteredTodos.slice(startIndex, endIndex);
            }

            renderPaginationControls() {
                // Update info text (both top and bottom)
                const infoTextTop = document.getElementById('pagination-info-text');
                const infoTextBottom = document.getElementById('pagination-info-text-bottom');
                let infoContent;
                
                if (this.pagination.pageSize === 'all' || this.pagination.pageSize === 'unlimited') {
                    infoContent = `Showing all ${this.pagination.totalItems} todos`;
                } else {
                    const pageSize = parseInt(this.pagination.pageSize);
                    const startIndex = (this.pagination.currentPage - 1) * pageSize + 1;
                    const endIndex = Math.min(startIndex + pageSize - 1, this.pagination.totalItems);
                    infoContent = `Showing ${startIndex}-${endIndex} of ${this.pagination.totalItems} todos`;
                }
                
                infoTextTop.textContent = infoContent;
                infoTextBottom.textContent = infoContent;

                // Update page size selectors (both)
                const pageSizeSelectTop = document.getElementById('page-size-select');
                const pageSizeSelectBottom = document.getElementById('page-size-select-bottom');
                pageSizeSelectTop.value = this.pagination.pageSize;
                pageSizeSelectBottom.value = this.pagination.pageSize;

                // Update navigation buttons (both)
                const prevBtnTop = document.getElementById('prev-page-btn');
                const nextBtnTop = document.getElementById('next-page-btn');
                const prevBtnBottom = document.getElementById('prev-page-btn-bottom');
                const nextBtnBottom = document.getElementById('next-page-btn-bottom');
                
                const isAllPages = this.pagination.pageSize === 'all';
                const isFirstPage = this.pagination.currentPage <= 1;
                const isLastPage = this.pagination.currentPage >= this.pagination.totalPages;
                
                prevBtnTop.disabled = isAllPages || isFirstPage;
                nextBtnTop.disabled = isAllPages || isLastPage;
                prevBtnBottom.disabled = isAllPages || isFirstPage;
                nextBtnBottom.disabled = isAllPages || isLastPage;

                // Render page numbers (both)
                this.renderPageNumbers();
            }

            renderPageNumbers() {
                const pageNumbersTop = document.getElementById('page-numbers');
                const pageNumbersBottom = document.getElementById('page-numbers-bottom');
                
                if (this.pagination.pageSize === 'all' || this.pagination.totalPages <= 1) {
                    pageNumbersTop.innerHTML = '';
                    pageNumbersBottom.innerHTML = '';
                    return;
                }

                let html = '';
                const current = this.pagination.currentPage;
                const total = this.pagination.totalPages;

                // Simple pagination: show current page and a few around it
                const start = Math.max(1, current - 2);
                const end = Math.min(total, current + 2);

                // Add first page and ellipsis if needed
                if (start > 1) {
                    html += `<span class="page-number ${current === 1 ? 'active' : ''}" data-page="1">1</span>`;
                    if (start > 2) {
                        html += `<span class="page-ellipsis">...</span>`;
                    }
                }

                // Add page numbers
                for (let i = start; i <= end; i++) {
                    html += `<span class="page-number ${i === current ? 'active' : ''}" data-page="${i}">${i}</span>`;
                }

                // Add last page and ellipsis if needed
                if (end < total) {
                    if (end < total - 1) {
                        html += `<span class="page-ellipsis">...</span>`;
                    }
                    html += `<span class="page-number ${current === total ? 'active' : ''}" data-page="${total}">${total}</span>`;
                }

                pageNumbersTop.innerHTML = html;
                pageNumbersBottom.innerHTML = html;

                // Add click handlers for page numbers (both top and bottom)
                [pageNumbersTop, pageNumbersBottom].forEach(container => {
                    container.querySelectorAll('.page-number').forEach(btn => {
                        btn.addEventListener('click', () => {
                            const page = parseInt(btn.dataset.page);
                            this.goToPage(page);
                        });
                    });
                });
            }

            goToPage(page) {
                if (page >= 1 && page <= this.pagination.totalPages) {
                    this.pagination.currentPage = page;
                    this.renderTodos();
                }
            }

            changePageSize(newSize) {
                this.pagination.pageSize = newSize === 'all' ? 'all' : parseInt(newSize);
                this.pagination.currentPage = 1; // Reset to first page
                this.renderTodos();
            }

            async moveTodoUp(todoId) {
                console.log('Moving todo up:', todoId);
                const todo = this.todos.find(t => t.id === todoId);
                if (!todo) return;
                
                // Get current list order or assign one
                await this.ensureListOrders();
                
                // Find the todo directly above this one
                const sortedTodos = [...this.todos].sort((a, b) => (a.list_order || 0) - (b.list_order || 0));
                const currentIndex = sortedTodos.findIndex(t => t.id === todoId);
                
                if (currentIndex > 0) {
                    const todoAbove = sortedTodos[currentIndex - 1];
                    const newOrder = todoAbove.list_order - 1;
                    
                    try {
                        await this.api.patchTodo(this.projectName, todoId, { list_order: newOrder });
                        todo.list_order = newOrder;
                        this.applyFilters(); // Re-render with new order
                        TodoUtils.toast.success('Todo moved up');
                    } catch (error) {
                        console.error('Error moving todo up:', error);
                        TodoUtils.toast.error('Failed to move todo');
                    }
                }
            }

            async moveTodoDown(todoId) {
                console.log('Moving todo down:', todoId);
                const todo = this.todos.find(t => t.id === todoId);
                if (!todo) return;
                
                // Get current list order or assign one
                await this.ensureListOrders();
                
                // Find the todo directly below this one
                const sortedTodos = [...this.todos].sort((a, b) => (a.list_order || 0) - (b.list_order || 0));
                const currentIndex = sortedTodos.findIndex(t => t.id === todoId);
                
                if (currentIndex < sortedTodos.length - 1) {
                    const todoBelow = sortedTodos[currentIndex + 1];
                    const newOrder = todoBelow.list_order + 1;
                    
                    try {
                        await this.api.patchTodo(this.projectName, todoId, { list_order: newOrder });
                        todo.list_order = newOrder;
                        this.applyFilters(); // Re-render with new order
                        TodoUtils.toast.success('Todo moved down');
                    } catch (error) {
                        console.error('Error moving todo down:', error);
                        TodoUtils.toast.error('Failed to move todo');
                    }
                }
            }

            async ensureListOrders() {
                // Make sure all todos have list_order values
                const todosNeedingOrder = this.todos.filter(t => t.list_order === undefined || t.list_order === null);
                
                if (todosNeedingOrder.length > 0) {
                    console.log('Assigning list orders to', todosNeedingOrder.length, 'todos');
                    
                    // Calculate initial list order based on urgency/priority
                    for (const todo of todosNeedingOrder) {
                        const initialOrder = this.calculateInitialListOrder(todo);
                        
                        try {
                            await this.api.patchTodo(this.projectName, todo.id, { list_order: initialOrder });
                            todo.list_order = initialOrder;
                        } catch (error) {
                            console.error('Error setting list order for todo:', todo.id, error);
                        }
                    }
                }
            }

            calculateInitialListOrder(newTodo) {
                // Sort existing todos to find insertion point
                const sortedTodos = [...this.todos]
                    .filter(t => t.list_order !== undefined && t.list_order !== null)
                    .sort((a, b) => (a.list_order || 0) - (b.list_order || 0));
                
                // Calculate priority score (higher = more important)
                const getPriorityScore = (todo) => {
                    const urgencyScore = this.getUrgencyScore(todo.urgency || 'medium');
                    const priorityScore = this.getUrgencyScore(todo.priority || 'medium'); // Reuse same scoring
                    return urgencyScore + priorityScore; // Combined score
                };
                
                const newTodoScore = getPriorityScore(newTodo);
                
                // Find insertion point
                for (let i = 0; i < sortedTodos.length; i++) {
                    const existingTodoScore = getPriorityScore(sortedTodos[i]);
                    
                    // If new todo has higher or equal priority, insert before this one
                    if (newTodoScore >= existingTodoScore) {
                        return i > 0 ? sortedTodos[i - 1].list_order - 1 : (sortedTodos[0].list_order || 1) - 1;
                    }
                }
                
                // If no insertion point found, add at the end
                const lastOrder = sortedTodos.length > 0 ? sortedTodos[sortedTodos.length - 1].list_order : 1000;
                return lastOrder + 1;
            }
            
            getUrgencyScore(level) {
                switch (level) {
                    case 'critical': return 4;
                    case 'high': return 3;
                    case 'medium': return 2;
                    case 'low': return 1;
                    default: return 2;
                }
            }

            async updateTodoStatus(todoId, status) {
                try {
                    await this.api.patchTodo(this.projectName, todoId, { status });
                    
                    // Update local todo data
                    const todo = this.todos.find(t => t.id === todoId);
                    if (todo) {
                        todo.status = status;
                        this.updateStatistics();
                        this.applyFilters();
                    }

                    TodoUtils.toast.success(`Todo marked as ${TodoUtils.formatters.statusLabel(status)}`);
                } catch (error) {
                    console.error('Error updating todo status:', error);
                    TodoUtils.toast.error('Failed to update todo status');
                }
            }

            toggleTodoSelection(todoId, selected) {
                if (selected) {
                    this.selectedTodos.add(todoId);
                } else {
                    this.selectedTodos.delete(todoId);
                }

                this.updateBulkActions();
            }

            updateBulkActions() {
                const count = this.selectedTodos.size;
                this.elements.selectedCount.textContent = count;

                if (count > 0) {
                    TodoUtils.dom.toggle(this.elements.bulkActions, true);
                } else {
                    TodoUtils.dom.toggle(this.elements.bulkActions, false);
                }
            }

            async bulkUpdateStatus(status) {
                if (this.selectedTodos.size === 0) return;

                const todoIds = Array.from(this.selectedTodos);
                const promises = todoIds.map(id => 
                    this.api.patchTodo(this.projectName, id, { status })
                );

                try {
                    await Promise.all(promises);

                    // Update local data
                    todoIds.forEach(id => {
                        const todo = this.todos.find(t => t.id === id);
                        if (todo) {
                            todo.status = status;
                        }
                    });

                    this.updateStatistics();
                    this.applyFilters();
                    this.clearSelection();

                    TodoUtils.toast.success(`${todoIds.length} todos marked as ${TodoUtils.formatters.statusLabel(status)}`);
                } catch (error) {
                    console.error('Error bulk updating todos:', error);
                    TodoUtils.toast.error('Failed to update some todos');
                }
            }

            clearSelection() {
                this.selectedTodos.clear();
                document.querySelectorAll('.todo-select').forEach(checkbox => {
                    checkbox.checked = false;
                });
                this.updateBulkActions();
            }

            selectAll() {
                this.selectedTodos.clear();
                this.filteredTodos.forEach(todo => {
                    this.selectedTodos.add(todo.id);
                });

                document.querySelectorAll('.todo-select').forEach(checkbox => {
                    checkbox.checked = true;
                });

                this.updateBulkActions();
            }

            showAddTodoModal() {
                console.log('[DEBUG] showAddTodoModal called - clearing editing state');
                this.currentEditingTodo = null;
                console.log('[DEBUG] currentEditingTodo set to:', this.currentEditingTodo);
                document.getElementById('modal-title').textContent = '✨ Add New Todo';
                this.resetForm();
                this.populateDependencies();
                this.switchTab('basic');
                this.showModal();
            }

            showEditTodoModal(todoId) {
                this.currentEditingTodo = this.todos.find(t => t.id === todoId);
                if (!this.currentEditingTodo) return;

                document.getElementById('modal-title').textContent = '✏️ Edit Todo';
                this.populateForm(this.currentEditingTodo);
                this.populateDependencies();
                this.switchTab('basic');
                this.showModal();
            }

            showModal() {
                document.getElementById('todo-modal').classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }

            hideModal(force = false) {
                console.log('[DEBUG] hideModal called with force =', force);
                console.log('[DEBUG] hasUnsavedChanges() =', this.hasUnsavedChanges());
                
                if (!force && this.hasUnsavedChanges()) {
                    console.log('[DEBUG] hideModal blocked due to unsaved changes');
                    return; // Don't close if there are unsaved changes
                }
                
                console.log('[DEBUG] hideModal proceeding to close modal');
                document.getElementById('todo-modal').classList.add('hidden');
                document.body.style.overflow = '';
                this.resetForm();
                console.log('[DEBUG] hideModal completed');
            }

            async tryCloseModal() {
                console.log('[DEBUG] tryCloseModal called');
                console.log('[DEBUG] hasUnsavedChanges:', this.hasUnsavedChanges());
                
                if (this.hasUnsavedChanges()) {
                    // Check if form has minimal required data for auto-save
                    const title = document.getElementById('todo-title-input').value.trim();
                    const category = document.getElementById('todo-category-input').value.trim();
                    
                    if (title && category) {
                        console.log('[DEBUG] Auto-saving changes before closing modal');
                        try {
                            await this.saveTodo();
                            // If save was successful, modal will be closed by saveTodo()
                        } catch (error) {
                            console.log('[DEBUG] Save failed, showing guard dialog');
                            this.showModalGuard();
                        }
                    } else {
                        console.log('[DEBUG] Insufficient data for auto-save, just closing');
                        this.hideModal(true);
                    }
                } else {
                    console.log('[DEBUG] No unsaved changes, closing modal');
                    this.hideModal(true);
                }
            }

            showModalGuard() {
                document.getElementById('modal-guard').classList.add('show');
            }

            hideModalGuard() {
                document.getElementById('modal-guard').classList.remove('show');
            }

            hasUnsavedChanges() {
                // Check if any form fields have been modified from their default/original state
                const title = document.getElementById('todo-title-input').value.trim();
                const description = document.getElementById('todo-description-input').value.trim();
                const category = document.getElementById('todo-category-input').value;
                const estimatedHours = document.getElementById('todo-estimated-hours-input').value;
                const assignedTo = document.getElementById('todo-assigned-to-input').value.trim();
                const startDate = document.getElementById('todo-start-date-input').value;
                const dueDate = document.getElementById('todo-due-date-input').value;
                const tags = document.getElementById('todo-tags-input').value.trim();

                // If editing, compare against original values
                if (this.currentEditingTodo) {
                    return (
                        title !== (this.currentEditingTodo.title || '') ||
                        description !== (this.currentEditingTodo.description || '') ||
                        category !== (this.currentEditingTodo.category || '') ||
                        estimatedHours !== (this.currentEditingTodo.estimated_hours?.toString() || '') ||
                        assignedTo !== (this.currentEditingTodo.assigned_to || '') ||
                        startDate !== (this.currentEditingTodo.start_date || '') ||
                        dueDate !== (this.currentEditingTodo.due_date || '') ||
                        tags !== (this.currentEditingTodo.tags ? this.currentEditingTodo.tags.join(', ') : '')
                    );
                }

                // If adding new, check if any fields have content beyond defaults
                // Note: category defaults to 'development', so we check if it's different from default
                const hasNonDefaultCategory = category && category !== 'development';
                return title || description || hasNonDefaultCategory || estimatedHours || assignedTo || startDate || dueDate || tags;
            }

            switchTab(tabName) {
                // Update tab buttons
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

                // Update tab content
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(`${tabName}-tab`).classList.add('active');
            }

            populateDependencies() {
                const dependenciesList = document.getElementById('dependencies-list');
                dependenciesList.innerHTML = '';

                // Get available todos (excluding current editing todo and completed todos)
                const availableTodos = this.todos.filter(todo => {
                    return todo.status !== 'deleted' && 
                           todo.status !== 'completed' &&
                           (!this.currentEditingTodo || todo.id !== this.currentEditingTodo.id);
                });

                if (availableTodos.length === 0) {
                    dependenciesList.innerHTML = `
                        <div class="dependency-item">
                            <div class="dependency-info">
                                <div class="dependency-title">No available dependencies</div>
                                <div class="dependency-meta">All todos are either completed or this is the only todo</div>
                            </div>
                        </div>
                    `;
                    return;
                }

                // Get current dependencies if editing
                const currentDependencies = this.currentEditingTodo?.dependencies || [];

                availableTodos.forEach(todo => {
                    const isSelected = currentDependencies.includes(todo.id);
                    const dependencyItem = TodoUtils.dom.createElement('div', {
                        className: 'dependency-item'
                    }, `
                        <input type="checkbox" class="dependency-checkbox" 
                               data-todo-id="${todo.id}" ${isSelected ? 'checked' : ''}>
                        <div class="dependency-info">
                            <div class="dependency-title">${todo.title}</div>
                            <div class="dependency-meta">
                                ${todo.category ? `${todo.category} • ` : ''}
                                ${todo.estimated_hours ? `${todo.estimated_hours}h • ` : ''}
                                Created ${new Date(todo.created_date).toLocaleDateString()}
                            </div>
                        </div>
                        <span class="dependency-status ${todo.status}">${TodoUtils.formatters.statusLabel(todo.status)}</span>
                    `);
                    
                    dependenciesList.appendChild(dependencyItem);
                });
            }

            resetForm() {
                document.getElementById('todo-title-input').value = '';
                document.getElementById('todo-description-input').value = '';
                document.getElementById('todo-priority-input').value = 'medium';
                document.getElementById('todo-urgency-input').value = 'medium';
                document.getElementById('todo-category-input').value = 'development';
                document.getElementById('todo-estimated-hours-input').value = '';
                document.getElementById('todo-assigned-to-input').value = '';
                document.getElementById('todo-start-date-input').value = '';
                document.getElementById('todo-due-date-input').value = '';
                document.getElementById('todo-tags-input').value = '';
                
                // Clear dependencies checkboxes
                document.querySelectorAll('.dependency-checkbox').forEach(checkbox => {
                    checkbox.checked = false;
                });
            }

            populateForm(todo) {
                document.getElementById('todo-title-input').value = todo.title || '';
                document.getElementById('todo-description-input').value = todo.description || '';
                document.getElementById('todo-priority-input').value = todo.priority || 'medium';
                document.getElementById('todo-urgency-input').value = todo.urgency || 'medium';
                document.getElementById('todo-category-input').value = todo.category || '';
                document.getElementById('todo-estimated-hours-input').value = todo.estimated_hours || '';
                document.getElementById('todo-assigned-to-input').value = todo.assigned_to || '';
                
                if (todo.start_date) {
                    const startDate = new Date(todo.start_date);
                    document.getElementById('todo-start-date-input').value = startDate.toISOString().split('T')[0];
                }
                
                if (todo.due_date) {
                    const dueDate = new Date(todo.due_date);
                    document.getElementById('todo-due-date-input').value = dueDate.toISOString().split('T')[0];
                }
                
                document.getElementById('todo-tags-input').value = todo.tags ? todo.tags.join(', ') : '';
            }

            async saveTodo() {
                const formData = this.getFormData();
                if (!formData.title.trim()) {
                    TodoUtils.toast.error('Title is required');
                    return;
                }
                
                if (!formData.category) {
                    TodoUtils.toast.error('Category is required');
                    return;
                }

                try {
                    console.log('[DEBUG] Form data being sent:', JSON.stringify(formData, null, 2));
                    console.log('[DEBUG] Project name:', this.projectName);
                    console.log('[DEBUG] Current editing todo:', this.currentEditingTodo);
                    console.log('[DEBUG] Is editing mode:', !!this.currentEditingTodo);
                    
                    if (this.currentEditingTodo) {
                        // Update existing todo
                        console.log('[DEBUG] UPDATING existing todo with ID:', this.currentEditingTodo.id);
                        await this.api.patchTodo(this.projectName, this.currentEditingTodo.id, formData);
                        
                        // Update local data
                        Object.assign(this.currentEditingTodo, formData);
                        TodoUtils.toast.success('Todo updated successfully');
                    } else {
                        // Create new todo
                        const newTodo = await this.api.createTodo(this.projectName, formData);
                        
                        // Assign list_order based on urgency/priority
                        const initialOrder = this.calculateInitialListOrder(newTodo);
                        await this.api.patchTodo(this.projectName, newTodo.id, { list_order: initialOrder });
                        newTodo.list_order = initialOrder;
                        
                        this.todos.push(newTodo);
                        TodoUtils.toast.success('Todo created successfully');
                    }

                    this.updateStatistics();
                    this.populateFilters();
                    this.applyFilters();
                    console.log('[DEBUG] About to call hideModal(true) after successful save');
                    this.hideModal(true); // Force close since we just saved successfully
                    console.log('[DEBUG] hideModal(true) called successfully');

                } catch (error) {
                    console.error('Error saving todo:', error);
                    TodoUtils.toast.error('Failed to save todo');
                }
            }

            getFormData() {
                const tags = document.getElementById('todo-tags-input').value
                    .split(',')
                    .map(tag => tag.trim())
                    .filter(tag => tag.length > 0);

                // Get selected dependencies
                const dependencies = Array.from(document.querySelectorAll('.dependency-checkbox:checked'))
                    .map(checkbox => checkbox.dataset.todoId);

                return {
                    title: document.getElementById('todo-title-input').value.trim(),
                    description: document.getElementById('todo-description-input').value.trim(),
                    priority: document.getElementById('todo-priority-input').value,
                    urgency: document.getElementById('todo-urgency-input').value,
                    category: document.getElementById('todo-category-input').value.trim() || 'development',
                    estimated_hours: parseFloat(document.getElementById('todo-estimated-hours-input').value) || null,
                    assigned_to: document.getElementById('todo-assigned-to-input').value.trim() || null,
                    start_date: document.getElementById('todo-start-date-input').value || null,
                    due_date: document.getElementById('todo-due-date-input').value || null,
                    tags: tags.length > 0 ? tags : [],
                    dependencies: dependencies.length > 0 ? dependencies : []
                };
            }

            async deleteTodo(todoId) {
                console.log('[DEBUG] deleteTodo called with todoId:', todoId);
                if (!confirm('Are you sure you want to delete this todo? This will mark it as deleted.')) {
                    console.log('[DEBUG] Delete cancelled by user');
                    return;
                }

                try {
                    console.log('[DEBUG] Deleting todo with DELETE request');
                    await this.api.deleteTodo(this.projectName, todoId);
                    
                    // Update local data
                    const todo = this.todos.find(t => t.id === todoId);
                    if (todo) {
                        todo.status = 'deleted';
                    }

                    this.updateStatistics();
                    this.applyFilters();
                    TodoUtils.toast.success('Todo deleted');

                } catch (error) {
                    console.error('Error deleting todo:', error);
                    TodoUtils.toast.error('Failed to delete todo');
                }
            }

            async generateProgressReport() {
                try {
                    TodoUtils.toast.info(`Generating progress report for ${this.projectName}...`);
                    
                    const response = await this.api.generateProgressReport(this.projectName);
                    
                    // Open the HTML in a new window/tab
                    const newWindow = window.open('', '_blank');
                    if (newWindow) {
                        newWindow.document.write(response.html);
                        newWindow.document.close();
                        TodoUtils.toast.success('Progress report opened in new tab');
                    } else {
                        // Fallback: create a blob URL and download
                        const blob = new Blob([response.html], { type: 'text/html' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `${this.projectName}_progress_report.html`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                        TodoUtils.toast.success('Progress report downloaded');
                    }
                    
                } catch (error) {
                    console.error('Error generating progress report:', error);
                    TodoUtils.toast.error('Failed to generate progress report: ' + error.message);
                }
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.projectDetailPage = new ProjectDetailPage();
        });
    </script>
</body>
</html>