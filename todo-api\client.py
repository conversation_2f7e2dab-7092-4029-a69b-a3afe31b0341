#!/usr/bin/env python3
"""
Todo API Client Library

A Python client library for easy interaction with the Todo Management API.
"""

import requests
import json
from typing import List, Dict, Any, Optional
from datetime import date, datetime
from pathlib import Path

class TodoAPIClient:
    """Client for the Todo Management API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """Initialize the client.
        
        Args:
            base_url: Base URL of the Todo API server
        """
        self.base_url = base_url.rstrip('/')
        self.api_base = f"{self.base_url}/api/v1"
        self.session = requests.Session()
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """Handle API response and raise exceptions for errors."""
        if response.status_code in [200, 201]:
            return response.json()
        elif response.status_code == 204:
            return {"success": True}
        else:
            try:
                error_data = response.json()
                error_msg = error_data.get("error", f"HTTP {response.status_code}")
            except:
                error_msg = f"HTTP {response.status_code}: {response.text}"
            raise Exception(f"API Error: {error_msg}")
    
    # Health and Status
    def health_check(self) -> Dict[str, Any]:
        """Check API health status."""
        response = self.session.get(f"{self.api_base}/health")
        return self._handle_response(response)
    
    # Security/Whitelist Management
    def get_whitelisted_directories(self) -> List[str]:
        """Get list of whitelisted directories."""
        response = self.session.get(f"{self.api_base}/security/whitelist")
        data = self._handle_response(response)
        return data.get("whitelisted_directories", [])
    
    def add_whitelisted_directory(self, directory: str) -> Dict[str, Any]:
        """Add a directory to the whitelist."""
        response = self.session.post(
            f"{self.api_base}/security/whitelist",
            json={"directory": directory}
        )
        return self._handle_response(response)
    
    def remove_whitelisted_directory(self, directory: str) -> Dict[str, Any]:
        """Remove a directory from the whitelist."""
        response = self.session.delete(
            f"{self.api_base}/security/whitelist",
            json={"directory": directory}
        )
        return self._handle_response(response)
    
    # Project Management
    def list_projects(self) -> List[Dict[str, Any]]:
        """List all projects with todo lists."""
        response = self.session.get(f"{self.api_base}/projects")
        data = self._handle_response(response)
        return data.get("projects", [])
    
    def get_project(self, project_name: str) -> Dict[str, Any]:
        """Get detailed information about a specific project."""
        response = self.session.get(f"{self.api_base}/projects/{project_name}")
        return self._handle_response(response)
    
    # Todo Management
    def list_todos(self, project_name: str, status: Optional[str] = None,
                  priority: Optional[str] = None, category: Optional[str] = None,
                  tag: Optional[str] = None) -> Dict[str, Any]:
        """List todos for a project with optional filtering."""
        params = {}
        if status:
            params["status"] = status
        if priority:
            params["priority"] = priority
        if category:
            params["category"] = category
        if tag:
            params["tag"] = tag
        
        response = self.session.get(
            f"{self.api_base}/projects/{project_name}/todos",
            params=params
        )
        return self._handle_response(response)
    
    def get_todo(self, project_name: str, todo_id: str) -> Dict[str, Any]:
        """Get a specific todo by its ID."""
        response = self.session.get(f"{self.api_base}/projects/{project_name}/todos/{todo_id}")
        return self._handle_response(response)
    
    def create_todo(self, project_name: str, title: str, priority: str, category: str,
                   description: Optional[str] = None, estimated_hours: Optional[float] = None,
                   assigned_to: Optional[str] = None, due_date: Optional[date] = None,
                   tags: Optional[List[str]] = None, dependencies: Optional[List[str]] = None,
                   subtasks: Optional[List[Dict[str, Any]]] = None,
                   section: Optional[str] = None) -> Dict[str, Any]:
        """Create a new todo item."""
        todo_data = {
            "title": title,
            "priority": priority,
            "category": category
        }
        
        if description is not None:
            todo_data["description"] = description
        if estimated_hours is not None:
            todo_data["estimated_hours"] = estimated_hours
        if assigned_to is not None:
            todo_data["assigned_to"] = assigned_to
        if due_date is not None:
            todo_data["due_date"] = due_date.isoformat()
        if tags is not None:
            todo_data["tags"] = tags
        if dependencies is not None:
            todo_data["dependencies"] = dependencies
        if subtasks is not None:
            todo_data["subtasks"] = subtasks
        if section is not None:
            todo_data["section"] = section
        
        response = self.session.post(
            f"{self.api_base}/projects/{project_name}/todos",
            json=todo_data
        )
        return self._handle_response(response)
    
    def update_todo(self, project_name: str, todo_id: str, title: str, status: str,
                   priority: str, category: str, description: Optional[str] = None,
                   estimated_hours: Optional[float] = None, assigned_to: Optional[str] = None,
                   due_date: Optional[date] = None, tags: Optional[List[str]] = None,
                   dependencies: Optional[List[str]] = None,
                   subtasks: Optional[List[Dict[str, Any]]] = None,
                   section: Optional[str] = None) -> Dict[str, Any]:
        """Update an existing todo item (full update)."""
        todo_data = {
            "title": title,
            "status": status,
            "priority": priority,
            "category": category
        }
        
        if description is not None:
            todo_data["description"] = description
        if estimated_hours is not None:
            todo_data["estimated_hours"] = estimated_hours
        if assigned_to is not None:
            todo_data["assigned_to"] = assigned_to
        if due_date is not None:
            todo_data["due_date"] = due_date.isoformat()
        if tags is not None:
            todo_data["tags"] = tags
        if dependencies is not None:
            todo_data["dependencies"] = dependencies
        if subtasks is not None:
            todo_data["subtasks"] = subtasks
        if section is not None:
            todo_data["section"] = section
        
        response = self.session.put(
            f"{self.api_base}/projects/{project_name}/todos/{todo_id}",
            json=todo_data
        )
        return self._handle_response(response)
    
    def patch_todo(self, project_name: str, todo_id: str, **kwargs) -> Dict[str, Any]:
        """Partially update a todo item.
        
        Args:
            project_name: Name of the project
            todo_id: ID of the todo to update
            **kwargs: Fields to update (title, status, priority, etc.)
        """
        # Convert date objects to strings
        if 'due_date' in kwargs and isinstance(kwargs['due_date'], date):
            kwargs['due_date'] = kwargs['due_date'].isoformat()
        
        response = self.session.patch(
            f"{self.api_base}/projects/{project_name}/todos/{todo_id}",
            json=kwargs
        )
        return self._handle_response(response)
    
    def delete_todo(self, project_name: str, todo_id: str) -> Dict[str, Any]:
        """Delete a specific todo item."""
        response = self.session.delete(f"{self.api_base}/projects/{project_name}/todos/{todo_id}")
        return self._handle_response(response)
    
    # Convenience Methods
    def mark_todo_completed(self, project_name: str, todo_id: str) -> Dict[str, Any]:
        """Mark a todo as completed."""
        return self.patch_todo(project_name, todo_id, status="completed")
    
    def assign_todo(self, project_name: str, todo_id: str, assignee: str) -> Dict[str, Any]:
        """Assign a todo to someone."""
        return self.patch_todo(project_name, todo_id, assigned_to=assignee)
    
    def add_todo_tag(self, project_name: str, todo_id: str, new_tag: str) -> Dict[str, Any]:
        """Add a tag to a todo (preserves existing tags)."""
        todo = self.get_todo(project_name, todo_id)
        current_tags = todo.get("tags", [])
        if new_tag not in current_tags:
            current_tags.append(new_tag)
            return self.patch_todo(project_name, todo_id, tags=current_tags)
        return todo
    
    def set_todo_priority(self, project_name: str, todo_id: str, priority: str) -> Dict[str, Any]:
        """Set the priority of a todo."""
        return self.patch_todo(project_name, todo_id, priority=priority)


def main():
    """Example usage of the Todo API client."""
    print("🚀 Todo API Client Example")
    print("=" * 40)
    
    # Initialize client
    client = TodoAPIClient()
    
    try:
        # Check health
        health = client.health_check()
        print(f"✅ API Health: {health['status']}")
        
        # List projects
        projects = client.list_projects()
        print(f"📁 Found {len(projects)} projects")
        
        if projects:
            project_name = projects[0]["name"]
            print(f"🔍 Using project: {project_name}")
            
            # List todos
            todos_data = client.list_todos(project_name)
            todos = todos_data.get("todos", [])
            print(f"📝 Found {len(todos)} todos")
            
            # Example: Create a new todo
            print("\n➕ Creating example todo...")
            new_todo = client.create_todo(
                project_name=project_name,
                title="API Client Test Todo",
                priority="low",
                category="testing",
                description="Created via Python client library",
                tags=["api", "client", "test"],
                subtasks=[
                    {"text": "Test API client", "completed": True},
                    {"text": "Clean up test data", "completed": False}
                ]
            )
            todo_id = new_todo["id"]
            print(f"✅ Created todo: {todo_id}")
            
            # Example: Update the todo
            print("🔄 Updating todo...")
            client.mark_todo_completed(project_name, todo_id)
            print("✅ Marked as completed")
            
            # Example: Add a tag
            client.add_todo_tag(project_name, todo_id, "completed")
            print("✅ Added tag")
            
            # Clean up
            print("🗑️  Cleaning up...")
            client.delete_todo(project_name, todo_id)
            print("✅ Deleted test todo")
        
        print("\n🎉 Client example completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()