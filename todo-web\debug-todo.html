<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Todo Creation</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; margin-bottom: 5px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 15px 0; white-space: pre-wrap; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .success { background: #d4edda; border-color: #c3e6cb; }
    </style>
</head>
<body>
    <h1>Debug Todo Creation</h1>
    
    <form id="debug-form">
        <div class="form-group">
            <label for="title">Title *</label>
            <input type="text" id="title" required placeholder="Enter todo title">
        </div>
        
        <div class="form-group">
            <label for="description">Description</label>
            <textarea id="description" rows="3" placeholder="Optional description"></textarea>
        </div>
        
        <div class="form-group">
            <label for="priority">Priority</label>
            <select id="priority">
                <option value="low">Low</option>
                <option value="medium" selected>Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="category">Category *</label>
            <select id="category" required>
                <option value="">Select category...</option>
                <option value="analysis">Analysis</option>
                <option value="architecture">Architecture</option>
                <option value="development">Development</option>
                <option value="testing">Testing</option>
                <option value="uat">User Acceptance Testing</option>
                <option value="release">Release</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="estimated_hours">Estimated Hours</label>
            <input type="number" id="estimated_hours" min="0" step="0.5" placeholder="0.5">
        </div>
        
        <button type="submit">Create Todo (Debug)</button>
    </form>
    
    <div id="log" class="log">Ready to debug...</div>
    
    <script>
        const log = document.getElementById('log');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            log.textContent += `[${timestamp}] ${message}\n`;
            if (type === 'error') {
                log.className = 'log error';
            } else if (type === 'success') {
                log.className = 'log success';
            }
            console.log(message);
        }
        
        function getFormData() {
            return {
                title: document.getElementById('title').value.trim(),
                description: document.getElementById('description').value.trim() || null,
                priority: document.getElementById('priority').value,
                category: document.getElementById('category').value.trim() || 'development',
                estimated_hours: parseFloat(document.getElementById('estimated_hours').value) || null,
                assigned_to: null,
                start_date: null,
                due_date: null,
                tags: [],
                dependencies: []
            };
        }
        
        async function createTodo(projectName, todoData) {
            const url = `http://localhost:8000/api/v1/projects/${encodeURIComponent(projectName)}/todos`;
            
            addLog(`Making request to: ${url}`);
            addLog(`Request data: ${JSON.stringify(todoData, null, 2)}`);
            
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(todoData)
                });
                
                addLog(`Response status: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    const errorData = await response.text();
                    addLog(`Error response: ${errorData}`, 'error');
                    throw new Error(`HTTP ${response.status}: ${errorData}`);
                }
                
                const result = await response.json();
                addLog(`Success response: ${JSON.stringify(result, null, 2)}`, 'success');
                return result;
                
            } catch (error) {
                addLog(`Request failed: ${error.message}`, 'error');
                throw error;
            }
        }
        
        document.getElementById('debug-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            log.textContent = '';
            log.className = 'log';
            
            addLog('Starting todo creation debug...');
            
            const formData = getFormData();
            addLog(`Form data collected: ${JSON.stringify(formData, null, 2)}`);
            
            if (!formData.title) {
                addLog('ERROR: Title is required', 'error');
                return;
            }
            
            if (!formData.category || formData.category === '') {
                addLog('ERROR: Category is required', 'error');
                return;
            }
            
            try {
                const result = await createTodo('todo-lists', formData);
                addLog('Todo created successfully!', 'success');
            } catch (error) {
                addLog(`Failed to create todo: ${error.message}`, 'error');
            }
        });
        
        addLog('Debug page loaded. Fill out the form and submit to test todo creation.');
    </script>
</body>
</html>