/**
 * Main Application Logic
 * Handles dashboard functionality, project management, and user interactions
 */

class TodoApp {
    constructor() {
        this.api = window.todoAPI;
        this.projects = [];
        this.filteredProjects = [];
        this.currentFilter = 'all';
        this.searchQuery = '';
        this.showWorkProjects = true;  // Default to checked
        this.showHomeProjects = false; // Default to unchecked
        
        // Initialize elements after DOM is ready
        this.initializeElements();
        this.init();
    }
    
    initializeElements() {
        // DOM elements
        this.elements = {
            loadingState: document.getElementById('loading-state'),
            errorState: document.getElementById('error-state'),
            dashboardContent: document.getElementById('dashboard-content'),
            projectsGrid: document.getElementById('projects-grid'),
            emptyState: document.getElementById('empty-state'),
            projectSearch: document.getElementById('project-search'),
            statusFilter: document.getElementById('status-filter'),
            refreshBtn: document.getElementById('refresh-btn'),
            retryBtn: document.getElementById('retry-btn'),
            errorMessage: document.getElementById('error-message'),
            aboutModal: document.getElementById('about-modal'),
            aboutLink: document.getElementById('about-link'),
            addExistingProjectBtn: document.getElementById('add-existing-project-btn'),
            addNewProjectBtn: document.getElementById('add-new-project-btn'),
            existingProjectModal: document.getElementById('existing-project-modal'),
            newProjectModal: document.getElementById('new-project-modal'),
            existingProjectsLoading: document.getElementById('existing-projects-loading'),
            existingProjectsList: document.getElementById('existing-projects-list'),
            existingProjectsEmpty: document.getElementById('existing-projects-empty'),
            newProjectForm: document.getElementById('new-project-form'),
            projectNameInput: document.getElementById('project-name-input'),
            projectDescriptionInput: document.getElementById('project-description-input'),
            projectTypeInput: document.getElementById('project-type-input'),
            existingProjectTypeSelect: document.getElementById('existing-project-type-select'),
            cancelNewProjectBtn: document.getElementById('cancel-new-project-btn'),
            editProjectModal: document.getElementById('edit-project-modal'),
            editProjectForm: document.getElementById('edit-project-form'),
            editProjectNameInput: document.getElementById('edit-project-name-input'),
            editProjectDisplayNameInput: document.getElementById('edit-project-display-name-input'),
            editProjectTypeInput: document.getElementById('edit-project-type-input'),
            editProjectUrgencyInput: document.getElementById('edit-project-urgency-input'),
            editProjectImportanceInput: document.getElementById('edit-project-importance-input'),
            cancelEditProjectBtn: document.getElementById('cancel-edit-project-btn'),
            showWorkProjectsCheckbox: document.getElementById('show-work-projects'),
            showHomeProjectsCheckbox: document.getElementById('show-home-projects')
        };

        // Summary elements
        this.summaryElements = {
            totalProjects: document.getElementById('total-projects'),
            totalTodos: document.getElementById('total-todos'),
            completionRate: document.getElementById('completion-rate'),
            pendingTodos: document.getElementById('pending-todos')
        };
        
        // Check if all elements are found
        const missingElements = [];
        Object.entries(this.elements).forEach(([key, element]) => {
            if (!element) {
                missingElements.push(key);
            }
        });
        
        Object.entries(this.summaryElements).forEach(([key, element]) => {
            if (!element) {
                missingElements.push(key);
            }
        });
        
        if (missingElements.length > 0) {
            console.error('Missing DOM elements:', missingElements);
            throw new Error(`Missing DOM elements: ${missingElements.join(', ')}`);
        }
        
        TodoUtils.debug.log('All DOM elements found successfully');
    }

    async init() {
        try {
            TodoUtils.debug.log('Initializing TodoApp');
            this.setupEventListeners();
            TodoUtils.debug.log('Event listeners setup complete');
            await this.loadProjects();
            TodoUtils.debug.log('App initialization complete');
        } catch (error) {
            TodoUtils.debug.error('Failed to initialize app', error);
            this.showErrorState('Failed to initialize application: ' + error.message);
        }
    }

    setupEventListeners() {
        // Search and filter
        this.elements.projectSearch.addEventListener('input', 
            TodoUtils.dom.debounce((e) => this.handleSearch(e.target.value), 300)
        );
        
        this.elements.statusFilter.addEventListener('change', (e) => {
            this.handleFilterChange(e.target.value);
        });

        // Project type filter checkboxes
        this.elements.showWorkProjectsCheckbox.addEventListener('change', (e) => {
            this.showWorkProjects = e.target.checked;
            this.applyFilters();
        });

        this.elements.showHomeProjectsCheckbox.addEventListener('change', (e) => {
            this.showHomeProjects = e.target.checked;
            this.applyFilters();
        });

        // Refresh button
        this.elements.refreshBtn.addEventListener('click', () => {
            this.loadProjects();
        });

        // Retry button
        this.elements.retryBtn.addEventListener('click', () => {
            this.loadProjects();
        });

        // About modal
        this.elements.aboutLink.addEventListener('click', (e) => {
            e.preventDefault();
            this.showAboutModal();
        });

        // Modal close functionality
        this.elements.aboutModal.addEventListener('click', (e) => {
            if (e.target === this.elements.aboutModal || e.target.classList.contains('modal-close')) {
                this.hideAboutModal();
            }
        });

        // Project management buttons
        this.elements.addExistingProjectBtn.addEventListener('click', () => {
            this.showExistingProjectsModal();
        });

        this.elements.addNewProjectBtn.addEventListener('click', () => {
            this.showNewProjectModal();
        });

        // Modal close functionality for new modals
        this.elements.existingProjectModal.addEventListener('click', (e) => {
            if (e.target === this.elements.existingProjectModal || e.target.classList.contains('modal-close')) {
                this.hideExistingProjectModal();
            }
        });

        this.elements.newProjectModal.addEventListener('click', (e) => {
            if (e.target === this.elements.newProjectModal || e.target.classList.contains('modal-close')) {
                this.hideNewProjectModal();
            }
        });

        // New project form handling
        this.elements.newProjectForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.createNewProject();
        });

        this.elements.cancelNewProjectBtn.addEventListener('click', () => {
            this.hideNewProjectModal();
        });

        // Edit project form handling
        this.elements.editProjectForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveProjectEdits();
        });

        this.elements.cancelEditProjectBtn.addEventListener('click', () => {
            this.hideEditProjectModal();
        });

        // Edit project modal close functionality
        this.elements.editProjectModal.addEventListener('click', (e) => {
            if (e.target === this.elements.editProjectModal || e.target.classList.contains('modal-close')) {
                this.hideEditProjectModal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideAboutModal();
                this.hideExistingProjectModal();
                this.hideNewProjectModal();
                this.hideEditProjectModal();
            }
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                this.loadProjects();
            }
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                this.elements.projectSearch.focus();
            }
        });
    }

    async loadProjects() {
        this.showLoadingState();
        
        try {
            TodoUtils.debug.log('Starting to load projects');
            
            // First check if API is accessible
            TodoUtils.debug.log('Checking API health');
            await this.api.healthCheck();
            TodoUtils.debug.log('API health check passed');
            
            // Load projects
            const projectsResponse = await this.api.getProjects();
            
            if (projectsResponse.length === 0) {
                this.showEmptyState();
                return;
            }

            // Load project summaries in parallel - extract project names from objects
            const projectPromises = projectsResponse.map(project => 
                this.api.getProjectSummary(project.name)
            );
            
            const projectSummaries = await Promise.all(projectPromises);
            
            // Enhance project summaries with urgency/importance from backend data
            this.projects = projectSummaries.map(summary => {
                // Find corresponding backend project data for urgency/importance
                const backendProject = projectsResponse.find(p => p.name === summary.name);
                return {
                    ...summary,
                    // Add urgency and importance with defaults
                    urgency: backendProject?.urgency || 'Normal',
                    importance: backendProject?.importance || 'Normal',
                    project_type: summary.project_type || backendProject?.project_type || 'Work',
                    display_name: summary.display_name || backendProject?.display_name
                };
            });
            
            // Filter out projects with errors
            this.projects = this.projects.filter(project => !project.error);
            
            this.applyFilters();
            this.updateSummaryStats();
            this.showDashboardContent();
            
            TodoUtils.toast.success(`Loaded ${this.projects.length} projects`);
            
        } catch (error) {
            console.error('Error loading projects:', error);
            this.showErrorState(error.message);
        }
    }

    showLoadingState() {
        TodoUtils.dom.toggle(this.elements.loadingState, true);
        TodoUtils.dom.toggle(this.elements.errorState, false);
        TodoUtils.dom.toggle(this.elements.dashboardContent, false);
    }

    showErrorState(message) {
        this.elements.errorMessage.textContent = message;
        TodoUtils.dom.toggle(this.elements.loadingState, false);
        TodoUtils.dom.toggle(this.elements.errorState, true);
        TodoUtils.dom.toggle(this.elements.dashboardContent, false);
    }

    showDashboardContent() {
        TodoUtils.dom.toggle(this.elements.loadingState, false);
        TodoUtils.dom.toggle(this.elements.errorState, false);
        TodoUtils.dom.toggle(this.elements.dashboardContent, true);
    }

    showEmptyState() {
        this.showDashboardContent();
        this.renderProjects([]);
        this.updateSummaryStats();
    }

    handleSearch(query) {
        this.searchQuery = query.toLowerCase();
        this.applyFilters();
    }

    handleFilterChange(filter) {
        this.currentFilter = filter;
        this.applyFilters();
    }

    applyFilters() {
        this.filteredProjects = this.projects.filter(project => {
            // Text search
            if (this.searchQuery && !project.name.toLowerCase().includes(this.searchQuery)) {
                return false;
            }

            // Status filter
            if (this.currentFilter === 'active' && project.pendingTodos === 0) {
                return false;
            }
            if (this.currentFilter === 'completed' && project.pendingTodos > 0) {
                return false;
            }

            // Project type filter
            const projectType = project.project_type || 'Work';
            if (projectType === 'Work' && !this.showWorkProjects) {
                return false;
            }
            if (projectType === 'Home' && !this.showHomeProjects) {
                return false;
            }

            return true;
        });

        // Sort projects using Stephen Covey's 4-quadrant system: urgency + importance
        this.filteredProjects.sort((a, b) => {
            // Helper function to get priority score based on Covey quadrants
            const getCoveyScore = (project) => {
                const urgency = project.urgency || 'Normal';
                const importance = project.importance || 'Normal';
                
                // Score mapping: High=3, Normal=2, Low=1
                const urgencyScore = urgency === 'High' ? 3 : urgency === 'Normal' ? 2 : 1;
                const importanceScore = importance === 'High' ? 3 : importance === 'Normal' ? 2 : 1;
                
                // Covey quadrants priority: I(3,3)=9, II(2,3)=6, III(3,2)=6, IV(2,2)=4, etc.
                // Weight importance slightly higher than urgency
                return (importanceScore * 10) + urgencyScore;
            };
            
            const aScore = getCoveyScore(a);
            const bScore = getCoveyScore(b);
            
            // Primary sort: Covey score (highest first)
            if (aScore !== bScore) {
                return bScore - aScore;
            }
            
            // Secondary sort: Project type (Work before Home)
            const aType = a.project_type || 'Work';
            const bType = b.project_type || 'Work';
            
            if (aType !== bType) {
                if (aType === 'Work' && bType === 'Home') {
                    return -1;
                }
                if (aType === 'Home' && bType === 'Work') {
                    return 1;
                }
            }
            
            // Tertiary sort: Alphabetical by display name or name
            const aName = (a.display_name || a.name).toLowerCase();
            const bName = (b.display_name || b.name).toLowerCase();
            return aName.localeCompare(bName);
        });

        this.renderProjects(this.filteredProjects);
    }

    renderProjects(projects) {
        if (projects.length === 0) {
            TodoUtils.dom.toggle(this.elements.projectsGrid, false);
            TodoUtils.dom.toggle(this.elements.emptyState, true);
            return;
        }

        TodoUtils.dom.toggle(this.elements.projectsGrid, true);
        TodoUtils.dom.toggle(this.elements.emptyState, false);

        this.elements.projectsGrid.innerHTML = '';

        projects.forEach(project => {
            const projectCard = this.createProjectCard(project);
            this.elements.projectsGrid.appendChild(projectCard);
        });
    }

    createProjectCard(project) {
        const completionRate = project.completionRate || 0;
        const progressBarWidth = Math.max(completionRate, 0);
        const projectType = project.project_type || 'Work';
        const isHomeProject = projectType === 'Home';
        
        const card = TodoUtils.dom.createElement('div', {
            className: `project-card ${isHomeProject ? 'home-project' : ''}`,
            'role': 'listitem',
            'data-project': project.name
        }, `
            <div class="project-header">
                <div class="project-title-container">
                    <button class="btn-icon edit-project-btn" data-project="${project.name}" title="Edit Project">
                        ✏️
                    </button>
                    <h3>${project.display_name || project.name}</h3>
                </div>
                <span class="project-completion ${completionRate >= 100 ? 'completed' : ''}">${TodoUtils.formatters.percentage(completionRate)}</span>
            </div>
            
            <div class="project-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progressBarWidth}%"></div>
                </div>
            </div>
            
            <div class="project-stats">
                <div class="stat">
                    <span class="stat-label">Total</span>
                    <span class="stat-value">${project.totalTodos}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Completed</span>
                    <span class="stat-value">${project.completedTodos}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Pending</span>
                    <span class="stat-value">${project.pendingTodos}</span>
                </div>
                ${project.inProgressTodos > 0 ? `
                <div class="stat">
                    <span class="stat-label">In Progress</span>
                    <span class="stat-value">${project.inProgressTodos}</span>
                </div>
                ` : ''}
            </div>
            
            ${Object.keys(project.priorities).length > 0 ? `
            <div class="project-priorities">
                ${Object.entries(project.priorities).map(([priority, count]) => `
                    <span class="priority-tag priority-${priority}">${TodoUtils.formatters.priorityLabel(priority)}: ${count}</span>
                `).join('')}
            </div>
            ` : ''}
            
            <div class="project-actions">
                <div class="project-meta">
                    <span class="project-type ${projectType.toLowerCase()}-type">${projectType === 'Home' ? '🏠' : '💼'} ${projectType}</span>
                    ${project.lastUpdated ? `
                    <span class="last-updated">Updated ${TodoUtils.formatters.relativeTime(project.lastUpdated)}</span>
                    ` : ''}
                </div>
                <div class="project-buttons">
                    <button class="btn btn-primary view-project-btn" data-project="${project.name}">
                        View Project
                    </button>
                </div>
            </div>
        `);

        // Add click handler for view project button
        const viewBtn = card.querySelector('.view-project-btn');
        viewBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.viewProject(project.name);
        });

        // Add click handler for edit project button
        const editBtn = card.querySelector('.edit-project-btn');
        editBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.editProject(project.name, project.project_type, project.display_name, project.urgency, project.importance);
        });


        // Make entire card clickable
        card.addEventListener('click', () => {
            this.viewProject(project.name);
        });

        card.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.viewProject(project.name);
            }
        });

        // Make card focusable
        card.setAttribute('tabindex', '0');

        return card;
    }

    viewProject(projectName) {
        TodoUtils.routing.navigate('project.html', { project: projectName });
    }

    updateSummaryStats() {
        const totalProjects = this.projects.length;
        const totalTodos = this.projects.reduce((sum, p) => sum + p.totalTodos, 0);
        const completedTodos = this.projects.reduce((sum, p) => sum + p.completedTodos, 0);
        const pendingTodos = this.projects.reduce((sum, p) => sum + p.pendingTodos, 0);
        const completionRate = totalTodos > 0 ? (completedTodos / totalTodos * 100) : 0;

        this.summaryElements.totalProjects.textContent = totalProjects;
        this.summaryElements.totalTodos.textContent = totalTodos;
        this.summaryElements.completionRate.textContent = TodoUtils.formatters.percentage(completionRate);
        this.summaryElements.pendingTodos.textContent = pendingTodos;
    }

    showAboutModal() {
        TodoUtils.dom.toggle(this.elements.aboutModal, true);
        this.elements.aboutModal.setAttribute('aria-hidden', 'false');
        
        // Focus the modal for accessibility
        const closeBtn = this.elements.aboutModal.querySelector('.modal-close');
        if (closeBtn) closeBtn.focus();
    }

    hideAboutModal() {
        TodoUtils.dom.toggle(this.elements.aboutModal, false);
        this.elements.aboutModal.setAttribute('aria-hidden', 'true');
        
        // Return focus to the about link
        this.elements.aboutLink.focus();
    }

    showExistingProjectsModal() {
        TodoUtils.dom.toggle(this.elements.existingProjectModal, true);
        this.elements.existingProjectModal.setAttribute('aria-hidden', 'false');
        
        // Focus the modal for accessibility
        const closeBtn = this.elements.existingProjectModal.querySelector('.modal-close');
        if (closeBtn) closeBtn.focus();
        
        // Load existing projects
        this.loadExistingProjects();
    }

    hideExistingProjectModal() {
        TodoUtils.dom.toggle(this.elements.existingProjectModal, false);
        this.elements.existingProjectModal.setAttribute('aria-hidden', 'true');
        
        // Return focus to the button
        this.elements.addExistingProjectBtn.focus();
    }

    showNewProjectModal() {
        TodoUtils.dom.toggle(this.elements.newProjectModal, true);
        this.elements.newProjectModal.setAttribute('aria-hidden', 'false');
        
        // Clear form
        this.elements.newProjectForm.reset();
        
        // Focus the first input
        this.elements.projectNameInput.focus();
    }

    hideNewProjectModal() {
        TodoUtils.dom.toggle(this.elements.newProjectModal, false);
        this.elements.newProjectModal.setAttribute('aria-hidden', 'true');
        
        // Return focus to the button
        this.elements.addNewProjectBtn.focus();
    }

    async loadExistingProjects() {
        // Show loading state
        TodoUtils.dom.toggle(this.elements.existingProjectsLoading, true);
        TodoUtils.dom.toggle(this.elements.existingProjectsList, false);
        TodoUtils.dom.toggle(this.elements.existingProjectsEmpty, false);

        try {
            // Get both managed and unmanaged projects
            const [managedResponse, unmanagedResponse] = await Promise.all([
                this.api.getProjects(),
                this.api.getUnmanagedProjects()
            ]);
            
            const managedProjects = managedResponse.projects || [];
            const unmanagedProjects = unmanagedResponse.projects || [];
            
            // Create a set of managed project names for quick lookup
            const managedProjectNames = new Set(managedProjects.map(p => p.name));
            
            // If we have managed projects, we might have some that were previously "inactive"
            // Let's also check if any managed projects are not showing up in the main list
            const allProjects = [...unmanagedProjects];
            
            // Add any managed projects that might not be showing in the main dashboard
            // (this handles cases where projects exist but might have issues)
            for (const managedProject of managedProjects) {
                if (!this.projects.find(p => p.name === managedProject.name)) {
                    // This is a managed project that's not showing in the main list
                    // Add it as a "re-enable" option
                    allProjects.push({
                        name: managedProject.name,
                        path: managedProject.path,
                        description: `Managed project (${managedProject.total_todos} todos)`,
                        has_git: true, // assume managed projects are git repos
                        last_modified: managedProject.last_updated,
                        isManaged: true,
                        managedData: managedProject
                    });
                }
            }
            
            TodoUtils.dom.toggle(this.elements.existingProjectsLoading, false);
            
            if (allProjects.length === 0) {
                TodoUtils.dom.toggle(this.elements.existingProjectsEmpty, true);
            } else {
                this.renderExistingProjects(allProjects);
                TodoUtils.dom.toggle(this.elements.existingProjectsList, true);
            }
            
        } catch (error) {
            console.error('Error loading existing projects:', error);
            TodoUtils.dom.toggle(this.elements.existingProjectsLoading, false);
            TodoUtils.toast.error('Failed to scan for existing projects: ' + error.message);
        }
    }

    renderExistingProjects(projects) {
        this.elements.existingProjectsList.innerHTML = '';
        
        projects.forEach(project => {
            const projectElement = this.createExistingProjectElement(project);
            this.elements.existingProjectsList.appendChild(projectElement);
        });
    }

    createExistingProjectElement(project) {
        const div = TodoUtils.dom.createElement('div', {
            className: `project-item ${project.isManaged ? 'managed-project' : ''}`,
            'data-project-name': project.name
        });

        const gitIcon = project.has_git ? '📘' : '📁';
        const lastModified = project.last_modified 
            ? new Date(project.last_modified).toLocaleDateString()
            : 'Unknown';

        // Different button text and action based on whether it's managed
        const buttonText = project.isManaged ? 'Re-enable in Dashboard' : 'Add Todo Management';
        const buttonClass = project.isManaged ? 'btn btn-secondary btn-small' : 'btn btn-primary btn-small';
        const statusIcon = project.isManaged ? '⚠️' : '➕';

        div.innerHTML = `
            <div class="project-info">
                <h4>${gitIcon} ${project.name} ${project.isManaged ? '(Has Todo Structure)' : ''}</h4>
                <small>${project.description || 'No description available'}</small>
                <br><small>Last modified: ${lastModified}</small>
            </div>
            <button class="${buttonClass}" data-action="enable" data-project="${project.name}">
                ${statusIcon} ${buttonText}
            </button>
        `;

        // Add click handler for the enable button
        const enableBtn = div.querySelector('[data-action="enable"]');
        enableBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            if (project.isManaged) {
                this.reEnableProject(project);
            } else {
                this.enableProjectTodoManagement(project.name);
            }
        });

        return div;
    }

    async enableProjectTodoManagement(projectName) {
        try {
            TodoUtils.toast.info(`Enabling todo management for ${projectName}...`);
            
            const response = await this.api.enableTodoManagement(projectName);
            
            // After enabling, update the project type if it's not the default (Work)
            const selectedProjectType = this.elements.existingProjectTypeSelect.value;
            if (selectedProjectType !== 'Work') {
                await this.api.updateProjectMetadata(projectName, {
                    project_type: selectedProjectType
                });
            }
            
            TodoUtils.toast.success(response.message);
            
            // Close the modal and refresh the main projects list
            this.hideExistingProjectModal();
            
            // Refresh the main dashboard to show the newly enabled project
            setTimeout(() => {
                this.loadProjects();
            }, 1000);
            
        } catch (error) {
            console.error('Error enabling todo management:', error);
            TodoUtils.toast.error('Failed to enable todo management: ' + error.message);
        }
    }

    async reEnableProject(project) {
        // For managed projects that aren't showing in the dashboard,
        // we just close this modal and refresh the dashboard
        try {
            TodoUtils.toast.info(`Re-enabling ${project.name} in dashboard...`);
            
            // Close the modal
            this.hideExistingProjectModal();
            
            // Refresh the main dashboard - this should pick up the project
            setTimeout(() => {
                this.loadProjects();
                TodoUtils.toast.success(`${project.name} should now appear in the dashboard`);
            }, 500);
            
        } catch (error) {
            console.error('Error re-enabling project:', error);
            TodoUtils.toast.error('Failed to re-enable project: ' + error.message);
        }
    }

    async createNewProject() {
        const projectName = this.elements.projectNameInput.value.trim();
        const projectDescription = this.elements.projectDescriptionInput.value.trim();
        const projectType = this.elements.projectTypeInput.value;

        if (!projectName) {
            TodoUtils.toast.error('Project name is required');
            this.elements.projectNameInput.focus();
            return;
        }

        // Validate project name (basic validation)
        if (!/^[a-zA-Z0-9_-]+$/.test(projectName)) {
            TodoUtils.toast.error('Project name can only contain letters, numbers, underscores, and hyphens');
            this.elements.projectNameInput.focus();
            return;
        }

        try {
            TodoUtils.toast.info(`Creating ${projectType.toLowerCase()} project: ${projectName}...`);
            
            // First enable todo management to create the basic structure
            const response = await this.api.enableTodoManagement(projectName);
            
            // Update the project type if it's not the default (Work)
            if (projectType !== 'Work') {
                await this.api.updateProjectMetadata(projectName, {
                    project_type: projectType
                });
            }
            
            TodoUtils.toast.success(`${projectName} ${projectType.toLowerCase()} project created successfully!`);
            
            // Close modal and refresh dashboard
            this.hideNewProjectModal();
            
            setTimeout(() => {
                this.loadProjects();
            }, 1000);
            
        } catch (error) {
            console.error('Error creating new project:', error);
            TodoUtils.toast.error('Failed to create new project: ' + error.message);
        }
    }

    editProject(projectName, currentProjectType, currentDisplayName, currentUrgency, currentImportance) {
        this.currentEditingProject = projectName;
        this.elements.editProjectNameInput.value = projectName;
        this.elements.editProjectDisplayNameInput.value = currentDisplayName || '';
        this.elements.editProjectTypeInput.value = currentProjectType || 'Work';
        this.elements.editProjectUrgencyInput.value = currentUrgency || 'Normal';
        this.elements.editProjectImportanceInput.value = currentImportance || 'Normal';
        this.showEditProjectModal();
    }

    showEditProjectModal() {
        TodoUtils.dom.toggle(this.elements.editProjectModal, true);
        this.elements.editProjectModal.setAttribute('aria-hidden', 'false');
        
        // Focus the display name input
        this.elements.editProjectDisplayNameInput.focus();
    }

    hideEditProjectModal() {
        TodoUtils.dom.toggle(this.elements.editProjectModal, false);
        this.elements.editProjectModal.setAttribute('aria-hidden', 'true');
        this.currentEditingProject = null;
    }

    async saveProjectEdits() {
        if (!this.currentEditingProject) {
            TodoUtils.toast.error('No project selected for editing');
            return;
        }

        const newProjectType = this.elements.editProjectTypeInput.value;
        const newDisplayName = this.elements.editProjectDisplayNameInput.value.trim();
        const newUrgency = this.elements.editProjectUrgencyInput.value;
        const newImportance = this.elements.editProjectImportanceInput.value;

        try {
            TodoUtils.toast.info(`Updating ${this.currentEditingProject}...`);
            
            const updateData = {
                project_type: newProjectType,
                display_name: newDisplayName || null,
                urgency: newUrgency,
                importance: newImportance
            };
            
            console.log('Sending update data:', updateData);
            const result = await this.api.updateProjectMetadata(this.currentEditingProject, updateData);
            console.log('Update result:', result);
            
            TodoUtils.toast.success(`Project updated successfully`);
            
            // Close modal and refresh dashboard
            this.hideEditProjectModal();
            
            setTimeout(() => {
                this.loadProjects();
            }, 500);
            
        } catch (error) {
            console.error('Error updating project:', error);
            TodoUtils.toast.error('Failed to update project: ' + error.message);
        }
    }

}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.todoApp = new TodoApp();
});

// Export for testing
window.TodoApp = TodoApp;