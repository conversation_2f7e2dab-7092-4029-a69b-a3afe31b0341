openapi: 3.0.3
info:
  title: Todo Management API
  description: |
    A comprehensive REST API for managing todo items across projects.
    
    ## Features
    - CRUD operations on todo items
    - Project-based organization
    - Directory whitelisting for security
    - Support for subtasks, tags, and metadata
    
    ## Security
    - Only directories under ~/source/repos/ can be whitelisted
    - All write operations require whitelisted directories
  version: 1.0.0
  contact:
    name: Todo API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8000
    description: Local development server

paths:
  # Security/Configuration Endpoints
  /api/v1/security/whitelist:
    get:
      summary: Get whitelisted directories
      description: Retrieve list of currently whitelisted base directories
      operationId: getWhitelistedDirectories
      tags:
        - Security
      responses:
        '200':
          description: List of whitelisted directories
          content:
            application/json:
              schema:
                type: object
                properties:
                  whitelisted_directories:
                    type: array
                    items:
                      type: string
                      example: "/home/<USER>/source/repos"
    post:
      summary: Add directory to whitelist
      description: Add a base directory to the whitelist (must be under ~/source/repos/)
      operationId: addWhitelistedDirectory
      tags:
        - Security
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - directory
              properties:
                directory:
                  type: string
                  description: Base directory path to whitelist
                  example: "/home/<USER>/source/repos"
      responses:
        '201':
          description: Directory added to whitelist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: Invalid directory path
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Directory not under ~/source/repos/
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Remove directory from whitelist
      description: Remove a directory from the whitelist
      operationId: removeWhitelistedDirectory
      tags:
        - Security
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - directory
              properties:
                directory:
                  type: string
                  description: Directory to remove from whitelist
      responses:
        '200':
          description: Directory removed from whitelist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: Directory not found in whitelist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Project Endpoints
  /api/v1/projects:
    get:
      summary: List all projects
      description: Get list of all projects with todo lists
      operationId: listProjects
      tags:
        - Projects
      responses:
        '200':
          description: List of projects
          content:
            application/json:
              schema:
                type: object
                properties:
                  projects:
                    type: array
                    items:
                      $ref: '#/components/schemas/ProjectSummary'

  /api/v1/projects/{projectName}:
    get:
      summary: Get project details
      description: Get detailed information about a specific project
      operationId: getProject
      tags:
        - Projects
      parameters:
        - name: projectName
          in: path
          required: true
          schema:
            type: string
          description: Name of the project
          example: "AccountingCrmInterfaces"
      responses:
        '200':
          description: Project details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '404':
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Todo Endpoints
  /api/v1/projects/{projectName}/todos:
    get:
      summary: List todos for a project
      description: Get all todos for a specific project with optional filtering
      operationId: listTodos
      tags:
        - Todos
      parameters:
        - name: projectName
          in: path
          required: true
          schema:
            type: string
          description: Name of the project
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, completed, in_progress, cancelled]
          description: Filter by todo status
        - name: priority
          in: query
          schema:
            type: string
            enum: [critical, high, medium, low]
          description: Filter by priority level
        - name: category
          in: query
          schema:
            type: string
          description: Filter by category
        - name: tag
          in: query
          schema:
            type: string
          description: Filter by tag
      responses:
        '200':
          description: List of todos
          content:
            application/json:
              schema:
                type: object
                properties:
                  todos:
                    type: array
                    items:
                      $ref: '#/components/schemas/Todo'
                  metadata:
                    $ref: '#/components/schemas/TodoListMetadata'
        '404':
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      summary: Create a new todo
      description: Create a new todo item for the specified project
      operationId: createTodo
      tags:
        - Todos
      parameters:
        - name: projectName
          in: path
          required: true
          schema:
            type: string
          description: Name of the project
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTodoRequest'
      responses:
        '201':
          description: Todo created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Todo'
        '400':
          description: Invalid todo data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Project directory not whitelisted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/projects/{projectName}/todos/{todoId}:
    get:
      summary: Get a specific todo
      description: Retrieve a specific todo by its ID
      operationId: getTodo
      tags:
        - Todos
      parameters:
        - name: projectName
          in: path
          required: true
          schema:
            type: string
          description: Name of the project
        - name: todoId
          in: path
          required: true
          schema:
            type: string
          description: ID of the todo item
      responses:
        '200':
          description: Todo details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Todo'
        '404':
          description: Todo or project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Update a todo
      description: Update an existing todo item
      operationId: updateTodo
      tags:
        - Todos
      parameters:
        - name: projectName
          in: path
          required: true
          schema:
            type: string
          description: Name of the project
        - name: todoId
          in: path
          required: true
          schema:
            type: string
          description: ID of the todo item
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTodoRequest'
      responses:
        '200':
          description: Todo updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Todo'
        '400':
          description: Invalid todo data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Project directory not whitelisted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Todo or project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    patch:
      summary: Partially update a todo
      description: Update specific fields of a todo item
      operationId: patchTodo
      tags:
        - Todos
      parameters:
        - name: projectName
          in: path
          required: true
          schema:
            type: string
          description: Name of the project
        - name: todoId
          in: path
          required: true
          schema:
            type: string
          description: ID of the todo item
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchTodoRequest'
      responses:
        '200':
          description: Todo updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Todo'
        '400':
          description: Invalid todo data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Project directory not whitelisted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Todo or project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Delete a todo
      description: Delete a specific todo item
      operationId: deleteTodo
      tags:
        - Todos
      parameters:
        - name: projectName
          in: path
          required: true
          schema:
            type: string
          description: Name of the project
        - name: todoId
          in: path
          required: true
          schema:
            type: string
          description: ID of the todo item
      responses:
        '204':
          description: Todo deleted successfully
        '403':
          description: Project directory not whitelisted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Todo or project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Health and Status
  /api/v1/health:
    get:
      summary: Health check
      description: Check API health and status
      operationId: healthCheck
      tags:
        - System
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  timestamp:
                    type: string
                    format: date-time
                  version:
                    type: string
                    example: "1.0.0"

components:
  schemas:
    Todo:
      type: object
      required:
        - id
        - title
        - status
        - priority
        - category
      properties:
        id:
          type: string
          description: Unique identifier for the todo
          example: "testing-001"
        title:
          type: string
          description: Title of the todo item
          example: "Complete UAT environment setup"
        description:
          type: string
          description: Detailed description of the todo
          example: "Prepare representative dataset for stakeholder testing"
        status:
          type: string
          enum: [pending, completed, in_progress, cancelled]
          description: Current status of the todo
          example: "pending"
        priority:
          type: string
          enum: [critical, high, medium, low]
          description: Priority level of the todo
          example: "critical"
        category:
          type: string
          description: Category of the todo
          example: "testing"
        estimated_hours:
          type: number
          format: float
          description: Estimated hours to complete
          example: 1.5
        assigned_to:
          type: string
          nullable: true
          description: Person assigned to the todo
          example: "john.doe"
        due_date:
          type: string
          format: date
          nullable: true
          description: Due date for the todo
          example: "2025-12-31"
        created_date:
          type: string
          format: date
          description: Date when todo was created
          example: "2025-08-02"
        tags:
          type: array
          items:
            type: string
          description: Tags associated with the todo
          example: ["environment", "sync", "testing"]
        dependencies:
          type: array
          items:
            type: string
          description: List of todo IDs this item depends on
          example: ["setup-001"]
        subtasks:
          type: array
          items:
            $ref: '#/components/schemas/Subtask'
          description: Sub-tasks for this todo
        section:
          type: string
          description: Section this todo belongs to
          example: "UAT Preparation & Testing"
        _hash:
          type: string
          description: Hash for change detection
          example: "8ef87cac"

    Subtask:
      type: object
      required:
        - text
        - completed
      properties:
        text:
          type: string
          description: Description of the subtask
          example: "Prepare representative dataset"
        completed:
          type: boolean
          description: Whether the subtask is completed
          example: false

    CreateTodoRequest:
      type: object
      required:
        - title
        - priority
        - category
      properties:
        title:
          type: string
          description: Title of the todo item
          example: "Complete UAT environment setup"
        description:
          type: string
          description: Detailed description of the todo
        priority:
          type: string
          enum: [critical, high, medium, low]
          description: Priority level of the todo
        category:
          type: string
          description: Category of the todo
        estimated_hours:
          type: number
          format: float
          description: Estimated hours to complete
        assigned_to:
          type: string
          nullable: true
          description: Person assigned to the todo
        due_date:
          type: string
          format: date
          nullable: true
          description: Due date for the todo
        tags:
          type: array
          items:
            type: string
          description: Tags associated with the todo
        dependencies:
          type: array
          items:
            type: string
          description: List of todo IDs this item depends on
        subtasks:
          type: array
          items:
            type: object
            required: [text]
            properties:
              text:
                type: string
              completed:
                type: boolean
                default: false
        section:
          type: string
          description: Section this todo belongs to

    UpdateTodoRequest:
      type: object
      required:
        - title
        - status
        - priority
        - category
      properties:
        title:
          type: string
          description: Title of the todo item
        description:
          type: string
          description: Detailed description of the todo
        status:
          type: string
          enum: [pending, completed, in_progress, cancelled]
          description: Current status of the todo
        priority:
          type: string
          enum: [critical, high, medium, low]
          description: Priority level of the todo
        category:
          type: string
          description: Category of the todo
        estimated_hours:
          type: number
          format: float
          description: Estimated hours to complete
        assigned_to:
          type: string
          nullable: true
          description: Person assigned to the todo
        due_date:
          type: string
          format: date
          nullable: true
          description: Due date for the todo
        tags:
          type: array
          items:
            type: string
          description: Tags associated with the todo
        dependencies:
          type: array
          items:
            type: string
          description: List of todo IDs this item depends on
        subtasks:
          type: array
          items:
            $ref: '#/components/schemas/Subtask'
        section:
          type: string
          description: Section this todo belongs to

    PatchTodoRequest:
      type: object
      properties:
        title:
          type: string
          description: Title of the todo item
        description:
          type: string
          description: Detailed description of the todo
        status:
          type: string
          enum: [pending, completed, in_progress, cancelled]
          description: Current status of the todo
        priority:
          type: string
          enum: [critical, high, medium, low]
          description: Priority level of the todo
        category:
          type: string
          description: Category of the todo
        estimated_hours:
          type: number
          format: float
          description: Estimated hours to complete
        assigned_to:
          type: string
          nullable: true
          description: Person assigned to the todo
        due_date:
          type: string
          format: date
          nullable: true
          description: Due date for the todo
        tags:
          type: array
          items:
            type: string
          description: Tags associated with the todo
        dependencies:
          type: array
          items:
            type: string
          description: List of todo IDs this item depends on
        subtasks:
          type: array
          items:
            $ref: '#/components/schemas/Subtask'
        section:
          type: string
          description: Section this todo belongs to

    Project:
      type: object
      properties:
        name:
          type: string
          description: Name of the project
          example: "AccountingCrmInterfaces"
        path:
          type: string
          description: Full path to project directory
          example: "/home/<USER>/source/repos/AccountingCrmInterfaces"
        todo_list_path:
          type: string
          description: Path to consolidated todo list
          example: "/home/<USER>/source/repos/AccountingCrmInterfaces/project-progress/to-do-lists/todos.yml"
        metadata:
          $ref: '#/components/schemas/TodoListMetadata'
        total_todos:
          type: integer
          description: Total number of todos
          example: 57

    ProjectSummary:
      type: object
      properties:
        name:
          type: string
          description: Name of the project
          example: "AccountingCrmInterfaces"
        path:
          type: string
          description: Full path to project directory
        total_todos:
          type: integer
          description: Total number of todos
        completed_todos:
          type: integer
          description: Number of completed todos
        pending_todos:
          type: integer
          description: Number of pending todos
        last_updated:
          type: string
          format: date-time
          description: When the todo list was last updated

    TodoListMetadata:
      type: object
      properties:
        project_name:
          type: string
          description: Name of the project
        generated_date:
          type: string
          format: date
          description: Date when todo list was generated
        last_updated:
          type: string
          format: date-time
          description: When the todo list was last updated
        source:
          type: string
          description: Source of the todo list
        total_todos:
          type: integer
          description: Total number of todos
        integration_date:
          type: string
          format: date-time
          description: Date of last integration

    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation completed successfully"

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "Todo not found"
        code:
          type: string
          description: Error code
          example: "TODO_NOT_FOUND"
        details:
          type: object
          description: Additional error details
          additionalProperties: true

tags:
  - name: Security
    description: Directory whitelisting and security operations
  - name: Projects
    description: Project management operations
  - name: Todos
    description: Todo CRUD operations
  - name: System
    description: System health and status