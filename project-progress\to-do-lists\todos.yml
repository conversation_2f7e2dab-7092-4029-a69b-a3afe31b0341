metadata:
  display_name: To Do Lists
  generated_date: '2025-08-05'
  last_updated: '2025-08-06T09:42:19.508092'
  project_name: todo-lists
  project_type: Work
  source: api_initialization
  total_todos: 4
todos:
- _hash: b5c037be
  assigned_to: null
  category: setup
  created_date: '2025-08-05'
  dependencies: []
  description: This is your first todo item. You can edit or delete it and start adding
    your own tasks.
  due_date: null
  estimated_hours: 0.25
  id: welcome-001
  priority: medium
  section: Project Setup
  status: pending
  subtasks: []
  tags:
  - welcome
  - setup
  title: Welcome to todo-lists Todo Management
- _hash: f7ea120c
  assigned_to: null
  category: testing
  created_date: '2025-08-06'
  dependencies: []
  description: null
  due_date: null
  estimated_hours: null
  id: testing-fd1c64
  priority: medium
  section: null
  status: pending
  subtasks: []
  tags: []
  title: Test Todo Creation
- _hash: 4d9bd2d5
  assigned_to: null
  category: development
  created_date: '2025-08-06'
  dependencies: []
  description: null
  due_date: null
  estimated_hours: null
  id: development-5e5fda
  priority: medium
  section: null
  status: pending
  subtasks: []
  tags: []
  title: Frontend Fix Test
- _hash: e46290ea
  assigned_to: null
  category: architecture
  created_date: '2025-08-06'
  dependencies: []
  description: ''
  due_date: null
  estimated_hours: null
  id: architecture-fc9f00
  priority: medium
  section: null
  status: pending
  subtasks: []
  tags: []
  title: Test Architecture Todo
