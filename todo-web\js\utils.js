/**
 * Utility Functions
 * Common helper functions for DOM manipulation, formatting, and state management
 */

// Date and time formatting  
const formatters = {
    /**
     * Format a date string to relative time (e.g., "2 hours ago")
     */
    relativeTime(dateString) {
        if (!dateString) return 'Unknown';
        
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMinutes = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMinutes / 60);
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffMinutes < 1) return 'Just now';
        if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
        if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
        if (diffDays < 7) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
        
        return date.toLocaleDateString();
    },

    /**
     * Format todo status for display
     */
    statusLabel(status) {
        const labels = {
            pending: 'Pending',
            in_progress: 'In Progress',
            completed: 'Completed'
        };
        return labels[status] || status;
    },

    /**
     * Format priority for display
     */
    priorityLabel(priority) {
        const labels = {
            critical: 'Critical',
            high: 'High',
            medium: 'Medium',
            low: 'Low'
        };
        return labels[priority] || priority;
    },

    /**
     * Format percentage with one decimal place
     */
    percentage(value) {
        return `${Math.round(value * 10) / 10}%`;
    },

    /**
     * Get display name for a project, falling back to project name
     */
    projectDisplayName(project) {
        if (!project) return '';
        
        // Handle different project object structures
        if (typeof project === 'string') {
            return project; // Just a project name string
        }
        
        // Check for display_name in metadata first, then direct property
        const displayName = project.metadata?.display_name || 
                           project.display_name || 
                           project.name;
        
        return displayName || project.name || '';
    },

    /**
     * Lambda-style function to get display name from todo objects
     * Usage: todos.map(formatters.todoDisplayName)
     */
    todoDisplayName(todo) {
        return todo?.projectDisplayName || 
               todo?.enteredDisplayName || 
               todo?.projectName || 
               '';
    },

    /**
     * Lambda-style function to get display name from project objects  
     * Usage: projects.map(formatters.getProjectDisplay)
     */
    getProjectDisplay(project) {
        if (!project) return '';
        if (typeof project === 'string') return project;
        
        return project.metadata?.display_name || 
               project.display_name || 
               project.name || 
               '';
    }
};

// DOM utilities
const dom = {
    /**
     * Create an element with attributes and content
     */
    createElement(tag, attributes = {}, content = '') {
        const element = document.createElement(tag);
        
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'dataset') {
                Object.entries(value).forEach(([dataKey, dataValue]) => {
                    element.dataset[dataKey] = dataValue;
                });
            } else if (key.startsWith('aria-') || key.startsWith('data-')) {
                element.setAttribute(key, value);
            } else {
                element[key] = value;
            }
        });
        
        if (content) {
            if (typeof content === 'string') {
                element.innerHTML = content;
            } else {
                element.appendChild(content);
            }
        }
        
        return element;
    },

    /**
     * Toggle element visibility
     */
    toggle(element, show = null) {
        if (show === null) {
            element.classList.toggle('hidden');
        } else if (show) {
            element.classList.remove('hidden');
        } else {
            element.classList.add('hidden');
        }
    },

    /**
     * Show loading state
     */
    showLoading(element, message = 'Loading...') {
        element.innerHTML = `
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        `;
    },

    /**
     * Show error state
     */
    showError(element, message, retryCallback = null) {
        const retryButton = retryCallback ? 
            `<button class="btn btn-primary retry-btn">Retry</button>` : '';
        
        element.innerHTML = `
            <div class="error-state">
                <div class="error-icon">⚠️</div>
                <p>${message}</p>
                ${retryButton}
            </div>
        `;

        if (retryCallback) {
            element.querySelector('.retry-btn').addEventListener('click', retryCallback);
        }
    },

    /**
     * Debounce function calls
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// State management utilities
const state = {
    /**
     * Simple state store with change notifications
     */
    createStore(initialState = {}) {
        let state = { ...initialState };
        const listeners = [];

        return {
            getState: () => ({ ...state }),
            
            setState: (newState) => {
                const prevState = { ...state };
                state = { ...state, ...newState };
                listeners.forEach(listener => listener(state, prevState));
            },
            
            subscribe: (listener) => {
                listeners.push(listener);
                return () => {
                    const index = listeners.indexOf(listener);
                    if (index > -1) listeners.splice(index, 1);
                };
            }
        };
    }
};

// URL and routing utilities
const routing = {
    /**
     * Get URL parameters
     */
    getParams() {
        return new URLSearchParams(window.location.search);
    },

    /**
     * Update URL without page reload
     */
    updateUrl(params) {
        const url = new URL(window.location);
        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                url.searchParams.set(key, value);
            } else {
                url.searchParams.delete(key);
            }
        });
        window.history.replaceState({}, '', url);
    },

    /**
     * Navigate to a page
     */
    navigate(path, params = {}) {
        const url = new URL(path, window.location.origin);
        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                url.searchParams.set(key, value);
            }
        });
        window.location.href = url.toString();
    }
};

// Validation utilities
const validation = {
    /**
     * Validate todo data
     */
    validateTodo(todoData) {
        const errors = [];
        
        if (!todoData.title || todoData.title.trim().length === 0) {
            errors.push('Title is required');
        }
        
        if (todoData.title && todoData.title.length > 200) {
            errors.push('Title must be less than 200 characters');
        }
        
        if (!['pending', 'in_progress', 'completed'].includes(todoData.status)) {
            errors.push('Invalid status');
        }
        
        if (!['critical', 'high', 'medium', 'low'].includes(todoData.priority)) {
            errors.push('Invalid priority');
        }
        
        return errors;
    },

    /**
     * Sanitize user input
     */
    sanitize(input) {
        if (typeof input !== 'string') return input;
        return input.trim().replace(/[<>]/g, '');
    }
};

// Storage utilities
const storage = {
    /**
     * Get from localStorage with JSON parsing
     */
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch {
            return defaultValue;
        }
    },

    /**
     * Set to localStorage with JSON stringification
     */
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch {
            return false;
        }
    },

    /**
     * Remove from localStorage
     */
    remove(key) {
        localStorage.removeItem(key);
    }
};

// Toast notifications
const toast = {
    /**
     * Show a toast notification
     */
    show(message, type = 'info', duration = 3000) {
        const toastContainer = this.getOrCreateContainer();
        
        const toast = dom.createElement('div', {
            className: `toast toast-${type}`,
            'aria-live': 'polite',
            'aria-atomic': 'true'
        }, `
            <span class="toast-message">${message}</span>
            <button class="toast-close" aria-label="Close notification">&times;</button>
        `);

        toastContainer.appendChild(toast);

        // Auto-remove after duration
        const timeoutId = setTimeout(() => {
            this.remove(toast);
        }, duration);

        // Manual close
        toast.querySelector('.toast-close').addEventListener('click', () => {
            clearTimeout(timeoutId);
            this.remove(toast);
        });

        return toast;
    },

    /**
     * Remove a toast
     */
    remove(toast) {
        toast.style.animation = 'slideOut 0.3s ease-in-out';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    },

    /**
     * Get or create toast container
     */
    getOrCreateContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = dom.createElement('div', {
                id: 'toast-container',
                className: 'toast-container',
                'aria-live': 'polite'
            });
            document.body.appendChild(container);
        }
        return container;
    },

    /**
     * Convenience methods
     */
    success(message) { return this.show(message, 'success'); },
    error(message) { return this.show(message, 'error', 5000); },
    warning(message) { return this.show(message, 'warning', 4000); },
    info(message) { return this.show(message, 'info'); }
};

// Export for global use
window.TodoUtils = { formatters, dom, state, routing, validation, storage, toast };

// Add debug logging
window.TodoUtils.debug = {
    log: (message, data) => {
        console.log(`[TodoUtils] ${message}`, data || '');
    },
    error: (message, error) => {
        console.error(`[TodoUtils] ${message}`, error);
    }
};