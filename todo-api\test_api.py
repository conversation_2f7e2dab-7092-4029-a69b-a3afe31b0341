#!/usr/bin/env python3
"""
Todo API Test Script

Simple test script to verify the API functionality.
"""

import requests
import json
import time
from pathlib import Path

API_BASE = "http://localhost:8000/api/v1"

def test_health():
    """Test health endpoint."""
    print("🏥 Testing health endpoint...")
    try:
        response = requests.get(f"{API_BASE}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server. Is it running?")
        return False

def test_whitelist():
    """Test whitelist functionality."""
    print("\n🔒 Testing whitelist functionality...")
    
    # Get current whitelist
    response = requests.get(f"{API_BASE}/security/whitelist")
    if response.status_code == 200:
        print("✅ Retrieved whitelist")
        current_dirs = response.json().get("whitelisted_directories", [])
        print(f"   Current directories: {len(current_dirs)}")
    else:
        print(f"❌ Failed to get whitelist: {response.status_code}")
        return False
    
    # Try to add base directory
    base_dir = str(Path.home() / "source" / "repos")
    whitelist_data = {"directory": base_dir}
    
    response = requests.post(
        f"{API_BASE}/security/whitelist",
        json=whitelist_data
    )
    
    if response.status_code in [200, 201]:
        print(f"✅ Added/confirmed whitelist for: {base_dir}")
        return True
    else:
        print(f"❌ Failed to whitelist directory: {response.status_code}")
        print(f"   Response: {response.text}")
        return False

def test_projects():
    """Test project listing."""
    print("\n📁 Testing project listing...")
    
    response = requests.get(f"{API_BASE}/projects")
    if response.status_code == 200:
        projects = response.json().get("projects", [])
        print(f"✅ Found {len(projects)} projects")
        
        for project in projects[:3]:  # Show first 3
            print(f"   - {project['name']}: {project['total_todos']} todos")
        
        return projects
    else:
        print(f"❌ Failed to list projects: {response.status_code}")
        return []

def test_todos(project_name):
    """Test todo operations."""
    print(f"\n📝 Testing todos for project: {project_name}")
    
    # List todos
    response = requests.get(f"{API_BASE}/projects/{project_name}/todos")
    if response.status_code == 200:
        data = response.json()
        todos = data.get("todos", [])
        print(f"✅ Found {len(todos)} todos")
        
        if todos:
            # Test getting a specific todo
            first_todo_id = todos[0]["id"]
            response = requests.get(f"{API_BASE}/projects/{project_name}/todos/{first_todo_id}")
            if response.status_code == 200:
                print(f"✅ Retrieved specific todo: {first_todo_id}")
            else:
                print(f"❌ Failed to get specific todo: {response.status_code}")
        
        return True
    elif response.status_code == 404:
        print("ℹ️  Project not found or no todos")
        return True
    else:
        print(f"❌ Failed to list todos: {response.status_code}")
        return False

def test_create_todo(project_name):
    """Test creating a new todo."""
    print(f"\n➕ Testing todo creation for project: {project_name}")
    
    new_todo = {
        "title": "API Test Todo",
        "description": "This is a test todo created by the API test script",
        "priority": "low",
        "category": "testing",
        "estimated_hours": 0.5,
        "tags": ["test", "api", "automated"],
        "subtasks": [
            {"text": "Create todo via API", "completed": True},
            {"text": "Verify todo exists", "completed": False}
        ]
    }
    
    response = requests.post(
        f"{API_BASE}/projects/{project_name}/todos",
        json=new_todo
    )
    
    if response.status_code == 201:
        created_todo = response.json()
        todo_id = created_todo["id"]
        print(f"✅ Created todo with ID: {todo_id}")
        
        # Test updating the todo
        print("🔄 Testing todo update...")
        update_data = {
            **new_todo,
            "status": "completed",
            "description": "Updated: This todo was successfully created and updated via API"
        }
        
        response = requests.put(
            f"{API_BASE}/projects/{project_name}/todos/{todo_id}",
            json=update_data
        )
        
        if response.status_code == 200:
            print("✅ Successfully updated todo")
        else:
            print(f"❌ Failed to update todo: {response.status_code}")
        
        # Test partial update (PATCH)
        print("🔧 Testing partial update...")
        patch_data = {"status": "in_progress"}
        
        response = requests.patch(
            f"{API_BASE}/projects/{project_name}/todos/{todo_id}",
            json=patch_data
        )
        
        if response.status_code == 200:
            print("✅ Successfully patched todo")
        else:
            print(f"❌ Failed to patch todo: {response.status_code}")
        
        # Clean up - delete the test todo
        print("🗑️  Cleaning up test todo...")
        response = requests.delete(f"{API_BASE}/projects/{project_name}/todos/{todo_id}")
        
        if response.status_code == 204:
            print("✅ Successfully deleted test todo")
        else:
            print(f"⚠️  Failed to delete test todo: {response.status_code}")
        
        return True
    
    elif response.status_code == 403:
        print("⚠️  Project directory not whitelisted for write operations")
        return True
    else:
        print(f"❌ Failed to create todo: {response.status_code}")
        print(f"   Response: {response.text}")
        return False

def main():
    """Run all tests."""
    print("🧪 Todo API Test Suite")
    print("=" * 50)
    
    # Test health
    if not test_health():
        print("\n❌ Health check failed. Exiting.")
        return
    
    # Test whitelist
    if not test_whitelist():
        print("\n⚠️  Whitelist setup failed. Some tests may not work.")
    
    # Test projects
    projects = test_projects()
    
    if projects:
        # Test with first available project
        test_project = projects[0]["name"]
        test_todos(test_project)
        test_create_todo(test_project)
    else:
        print("\n⚠️  No projects found. Creating a test todo requires an existing project.")
    
    print("\n" + "=" * 50)
    print("🎉 Test suite completed!")
    print("\n💡 To explore the API interactively, visit:")
    print("   📖 http://localhost:8000/docs")

if __name__ == "__main__":
    main()