<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Today - Todo Management System</title>
    <link rel="stylesheet" href="css/style.css?v=5">
    <meta name="description" content="Daily Task Planning and Scrum Report Generator">
    <style>
        /* Additional styles specific to Today page */
        .today-header {
            background: var(--jomezpro-teal);
            color: var(--jomezpro-white);
            padding: 1rem 0;
            margin-bottom: 1rem;
        }

        /* Override main-content padding for Today page to reduce white space */
        .main-content {
            padding: 1rem 0 2rem 0;
        }

        .today-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .today-header h1 {
            font-size: 1.8rem;
            margin-bottom: 0;
        }

        .today-date {
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .daily-planning {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .planning-section {
            background: var(--jomezpro-white);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px var(--jomezpro-shadow);
        }

        .planning-section h3 {
            color: var(--jomezpro-dark);
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--jomezpro-teal);
            padding-bottom: 0.5rem;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .section-header h3 {
            margin-bottom: 0;
            border-bottom: none;
            padding-bottom: 0;
        }

        .header-hours-display {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .hours-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            font-weight: 700;
            transition: all 0.3s ease;
        }

        .hours-circle.low {
            background: rgba(39, 174, 96, 0.15);
            color: #27ae60;
            border: 2px solid #27ae60;
        }

        .hours-circle.medium {
            background: rgba(241, 196, 15, 0.15);
            color: #f39c12;
            border: 2px solid #f39c12;
        }

        .hours-circle.high {
            background: rgba(230, 126, 34, 0.15);
            color: #e67e22;
            border: 2px solid #e67e22;
        }

        .hours-circle.critical {
            background: rgba(231, 76, 60, 0.15);
            color: #e74c3c;
            border: 2px solid #e74c3c;
        }

        .hours-total {
            font-size: 1.5rem;
            font-weight: 700;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            transition: all 0.3s ease;
        }

        .hours-total.low {
            background: rgba(255, 255, 255, 0.2);
            color: var(--jomezpro-white);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .hours-total.medium {
            background: rgba(255, 255, 255, 0.2);
            color: var(--jomezpro-white);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .hours-total.high {
            background: rgba(255, 255, 255, 0.2);
            color: var(--jomezpro-white);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .hours-total.critical {
            background: rgba(255, 255, 255, 0.2);
            color: var(--jomezpro-white);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .available-todos {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--jomezpro-border);
            border-radius: 0.375rem;
            padding: 1rem;
        }

        .todo-item-small {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border: 1px solid var(--jomezpro-border);
            border-radius: 0.375rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .todo-item-small:hover {
            background: var(--jomezpro-bg);
            border-color: var(--jomezpro-teal);
        }

        .todo-item-small.selected {
            background: rgba(0, 196, 170, 0.1);
            border-color: var(--jomezpro-teal);
        }

        .todo-checkbox {
            width: 1rem;
            height: 1rem;
            border: 2px solid var(--jomezpro-border);
            border-radius: 0.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .todo-checkbox.checked {
            background: var(--jomezpro-teal);
            border-color: var(--jomezpro-teal);
            color: var(--jomezpro-white);
        }

        .todo-info {
            flex: 1;
        }

        .todo-title-small {
            font-weight: 500;
            color: var(--jomezpro-dark);
            margin-bottom: 0.25rem;
        }

        .todo-meta-small {
            font-size: 0.75rem !important;
            color: var(--jomezpro-light-gray) !important;
            line-height: 1.2 !important;
        }

        /* Force sub-header structure */
        .todo-item-small .todo-meta-small * {
            display: block !important;
        }

        .todo-meta-project {
            font-weight: 600 !important;
            color: var(--jomezpro-teal) !important;
            font-size: 0.8rem !important;
            display: block !important;
            margin-bottom: 2px !important;
        }

        .todo-meta-details {
            font-weight: 400 !important;
            color: var(--jomezpro-light-gray) !important;
            font-size: 0.7rem !important;
            display: block !important;
        }

        .priority-indicator {
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .priority-critical { background: var(--jomezpro-error); }
        .priority-high { background: var(--jomezpro-warning); }
        .priority-medium { background: var(--jomezpro-info); }
        .priority-low { background: var(--jomezpro-success); }

        .daily-todos-empty {
            text-align: center;
            padding: 2rem;
            color: var(--jomezpro-light-gray);
            border: 2px dashed var(--jomezpro-border);
            border-radius: 0.375rem;
        }

        .planning-actions {
            margin-top: 1rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .scrum-section {
            background: var(--jomezpro-white);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px var(--jomezpro-shadow);
            margin-bottom: 2rem;
        }

        .scrum-report-container {
            margin-top: 1rem;
        }

        .scrum-report-box {
            background: var(--jomezpro-bg);
            border: 1px solid var(--jomezpro-border);
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            min-height: 120px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .scrum-report-actions {
            margin-top: 1rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .filter-and-actions {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-bottom: 1rem;
            gap: 1rem;
        }

        .project-filter {
            flex: 1;
        }

        .project-filter select {
            padding: 0.5rem;
            border: 1px solid var(--jomezpro-border);
            border-radius: 0.375rem;
            background: var(--jomezpro-white);
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .stats-bar {
            background: linear-gradient(135deg, var(--jomezpro-bright-teal) 0%, var(--jomezpro-teal) 100%);
            color: var(--jomezpro-white) !important;
            padding: 1rem;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stats-bar * {
            color: var(--jomezpro-white) !important;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .stat-label {
            font-size: 0.75rem;
            opacity: 0.9;
        }

        /* Animations */
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .daily-planning {
                grid-template-columns: 1fr;
            }
            
            .stats-bar {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <h1><a href="index.html" style="text-decoration: none; color: inherit;">📋 Todo Management System</a></h1>
            <nav class="main-nav" role="navigation" aria-label="Main navigation">
                <ul>
                    <li><a href="today.html" aria-current="page">Today</a></li>
                    <li><a href="weekly.html">Weekly</a></li>
                    <li><a href="workflow.html">Workflows</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="today-header">
        <div class="container">
            <h1>Today's Task Planning</h1>
            <div class="today-date" id="today-date">Loading...</div>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loading-state" class="loading-state" aria-live="polite">
        <div class="loading-spinner"></div>
        <p>Loading today's planning...</p>
    </div>

    <!-- Error State -->
    <div id="error-state" class="error-state hidden" role="alert" aria-live="assertive">
        <div class="error-icon">⚠️</div>
        <h2>Unable to Load Planning Data</h2>
        <p id="error-message">Please check if the API server is running.</p>
        <button id="retry-btn" class="btn btn-primary">Retry</button>
    </div>

    <!-- Today Content -->
    <div id="today-content" class="hidden">
        <main class="main-content">
            <div class="container">
                <!-- Statistics Bar -->
                <div class="stats-bar">
                    <div class="stat-item">
                        <span id="selected-count" class="stat-number">0</span>
                        <span class="stat-label">Selected for Today</span>
                    </div>
                    <div class="stat-item">
                        <span id="estimated-hours" class="stat-number">0h</span>
                        <span class="stat-label">Estimated Hours</span>
                    </div>
                    <div class="stat-item">
                        <span id="available-count" class="stat-number">0</span>
                        <span class="stat-label">Available Tasks</span>
                    </div>
                </div>

                <!-- Daily Planning Grid -->
                <div class="daily-planning">
                    <!-- Available Todos -->
                    <div class="planning-section">
                        <h3>📝 Available Tasks</h3>
                        
                        <div class="filter-and-actions">
                            <div class="project-filter">
                                <label for="project-select">Filter by Project:</label>
                                <select id="project-select">
                                    <option value="all">All Projects</option>
                                    <!-- Dynamically populated -->
                                </select>
                            </div>
                            <button id="add-task-btn" class="btn btn-primary btn-small">
                                ➕ Add Task
                            </button>
                        </div>

                        <div id="available-todos" class="available-todos">
                            <!-- Todos will be dynamically inserted here -->
                        </div>
                    </div>

                    <!-- Today's Selected Todos -->
                    <div class="planning-section">
                        <div class="section-header">
                            <h3>🎯 Today's Tasks</h3>
                            <div class="header-hours-display">
                                <div id="today-hours-total" class="hours-total">0h</div>
                                <div id="today-hours-circle" class="hours-circle">0h</div>
                            </div>
                        </div>
                        
                        <div id="daily-todos" class="available-todos">
                            <div class="daily-todos-empty">
                                <p>No tasks selected for today</p>
                                <small>Select tasks from the left to add them to today's plan</small>
                            </div>
                        </div>

                        <div class="planning-actions">
                            <button id="clear-daily-btn" class="btn btn-secondary">Clear All</button>
                            <button id="save-daily-btn" class="btn btn-primary">Save Daily Plan</button>
                        </div>
                    </div>
                </div>

                <!-- Scrum Report Section -->
                <div class="scrum-section">
                    <h3>📊 Daily Scrum Report Generator</h3>
                    <p>Generate a concise summary of today's selected tasks for stand-up meetings.</p>

                    <div class="scrum-report-container">
                        <div id="scrum-report" class="scrum-report-box">
                            Select tasks above and click "Generate Scrum Report" to create a summary.
                        </div>

                        <div class="scrum-report-actions">
                            <button id="generate-scrum-btn" class="btn btn-primary">
                                🚀 Generate Scrum Report
                            </button>
                            <button id="copy-scrum-btn" class="btn btn-secondary" disabled>
                                📋 Copy to Clipboard
                            </button>
                            <button id="refresh-scrum-btn" class="btn btn-secondary">
                                🔄 Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2025 Todo Management System | <a href="index.html">Back to Dashboard</a> | <a href="api-test.html">API Test</a> | <a href="today.html">Today</a></p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script>
        // Today Page Implementation
        class TodayPage {
            constructor() {
                this.api = window.todoAPI;
                this.allTodos = [];
                this.filteredTodos = [];
                this.selectedTodos = new Set();
                this.projects = [];

                // DOM elements
                this.elements = {
                    loadingState: document.getElementById('loading-state'),
                    errorState: document.getElementById('error-state'),
                    todayContent: document.getElementById('today-content'), 
                    availableTodos: document.getElementById('available-todos'),
                    dailyTodos: document.getElementById('daily-todos'),
                    projectSelect: document.getElementById('project-select'),
                    scrumReport: document.getElementById('scrum-report'),
                    todayDate: document.getElementById('today-date'),
                    selectedCount: document.getElementById('selected-count'),
                    estimatedHours: document.getElementById('estimated-hours'),
                    availableCount: document.getElementById('available-count')
                };

                this.init();
            }

            async init() {
                this.setTodayDate();
                this.setupEventListeners();
                await this.loadData();
                this.loadDailyPlan();
            }

            setTodayDate() {
                const today = new Date();
                const options = { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                };
                this.elements.todayDate.textContent = today.toLocaleDateString('en-US', options);
            }

            setupEventListeners() {
                // Project filter
                this.elements.projectSelect.addEventListener('change', (e) => {
                    this.filterTodos(e.target.value);
                });

                // Buttons
                document.getElementById('clear-daily-btn').addEventListener('click', () => {
                    this.clearDailyPlan();
                });

                document.getElementById('save-daily-btn').addEventListener('click', () => {
                    this.saveDailyPlan();
                });

                document.getElementById('generate-scrum-btn').addEventListener('click', () => {
                    this.generateScrumReport();
                });

                document.getElementById('copy-scrum-btn').addEventListener('click', () => {
                    this.copyToClipboard();
                });

                document.getElementById('refresh-scrum-btn').addEventListener('click', () => {
                    this.loadData();
                });

                document.getElementById('retry-btn').addEventListener('click', () => {
                    this.loadData();
                });

                document.getElementById('add-task-btn').addEventListener('click', () => {
                    this.showAddTaskModal();
                });
            }

            async loadData() {
                this.showLoading();

                try {
                    // Load all projects and their todos
                    const projectNames = await this.api.getProjects();
                    this.projects = [];
                    this.allTodos = [];

                    for (const project of projectNames) {
                        const todosData = await this.api.getTodos(project.name);
                        const projectTodos = todosData.todos.map(todo => ({
                            ...todo,
                            projectName: project.name,
                            projectDisplayName: TodoUtils.formatters.getProjectDisplay(project)
                        }));
                        
                        this.projects.push(project);
                        this.allTodos.push(...projectTodos);
                    }

                    // Only show pending and in_progress todos
                    this.filteredTodos = this.allTodos.filter(todo => 
                        todo.status === 'pending' || todo.status === 'in_progress'
                    );

                    this.populateProjectFilter();
                    this.renderAvailableTodos();
                    this.updateStats();
                    this.showContent();

                } catch (error) {
                    console.error('Error loading data:', error);
                    this.showError(error.message);
                }
            }

            showLoading() {
                TodoUtils.dom.toggle(this.elements.loadingState, true);
                TodoUtils.dom.toggle(this.elements.errorState, false);
                TodoUtils.dom.toggle(this.elements.todayContent, false);
            }

            showError(message) {
                document.getElementById('error-message').textContent = message;
                TodoUtils.dom.toggle(this.elements.loadingState, false);
                TodoUtils.dom.toggle(this.elements.errorState, true);
                TodoUtils.dom.toggle(this.elements.todayContent, false);
            }

            showContent() {
                TodoUtils.dom.toggle(this.elements.loadingState, false);
                TodoUtils.dom.toggle(this.elements.errorState, false);
                TodoUtils.dom.toggle(this.elements.todayContent, true);
            }

            populateProjectFilter() {
                const select = this.elements.projectSelect;
                select.innerHTML = '<option value="all">All Projects</option>';
                
                this.projects.forEach(project => {
                    const option = document.createElement('option');
                    option.value = project.name || project;
                    option.textContent = TodoUtils.formatters.getProjectDisplay(project);
                    select.appendChild(option);
                });
            }

            filterTodos(projectName) {
                let todos = this.allTodos.filter(todo => 
                    todo.status === 'pending' || todo.status === 'in_progress'
                );
                
                if (projectName !== 'all') {
                    todos = todos.filter(todo => todo.projectName === projectName);
                }
                
                this.filteredTodos = todos;
                this.renderAvailableTodos();
                this.updateStats();
            }

            renderAvailableTodos() {
                this.elements.availableTodos.innerHTML = '';
                
                if (this.filteredTodos.length === 0) {
                    this.elements.availableTodos.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">No available tasks</p>';
                    return;
                }

                this.filteredTodos.forEach(todo => {
                    const todoElement = this.createTodoElement(todo);
                    this.elements.availableTodos.appendChild(todoElement);
                });
            }

            createTodoElement(todo) {
                const isSelected = this.selectedTodos.has(todo.id);
                
                const div = TodoUtils.dom.createElement('div', {
                    className: `todo-item-small ${isSelected ? 'selected' : ''}`,
                    'data-todo-id': todo.id
                });

                div.innerHTML = `
                    <div class="todo-checkbox ${isSelected ? 'checked' : ''}">
                        ${isSelected ? '✓' : ''}
                    </div>
                    <div class="priority-indicator priority-${todo.priority}"></div>
                    <div class="todo-info">
                        <div class="todo-title-small">${todo.title}</div>
                        <div class="todo-meta-small">
                            <div class="todo-meta-project">${TodoUtils.formatters.todoDisplayName(todo)}</div>
                            <div class="todo-meta-details">
                                ${TodoUtils.formatters.priorityLabel(todo.priority)} 
                                ${todo.estimated_hours ? `• ${todo.estimated_hours}h` : ''}
                            </div>
                        </div>
                    </div>
                `;

                div.addEventListener('click', () => {
                    this.toggleTodoSelection(todo);
                });

                return div;
            }

            toggleTodoSelection(todo) {
                if (this.selectedTodos.has(todo.id)) {
                    this.selectedTodos.delete(todo.id);
                } else {
                    this.selectedTodos.add(todo.id);
                }

                this.renderAvailableTodos();
                this.renderDailyTodos();
                this.updateStats();
            }

            renderDailyTodos() {
                const selectedTodoItems = this.allTodos.filter(todo => 
                    this.selectedTodos.has(todo.id)
                );

                if (selectedTodoItems.length === 0) {
                    this.elements.dailyTodos.innerHTML = `
                        <div class="daily-todos-empty">
                            <p>No tasks selected for today</p>
                            <small>Select tasks from the left to add them to today's plan</small>
                        </div>
                    `;
                    return;
                }

                this.elements.dailyTodos.innerHTML = '';
                selectedTodoItems.forEach(todo => {
                    const todoElement = this.createTodoElement(todo);
                    this.elements.dailyTodos.appendChild(todoElement);
                });
            }

            updateStats() {
                const selectedTodoItems = this.allTodos.filter(todo => 
                    this.selectedTodos.has(todo.id)
                );

                const totalHours = selectedTodoItems.reduce((sum, todo) => 
                    sum + (todo.estimated_hours || 0), 0
                );

                this.elements.selectedCount.textContent = selectedTodoItems.length;
                this.elements.estimatedHours.textContent = `${totalHours}h`;
                this.elements.availableCount.textContent = this.filteredTodos.length;

                // Update the prominent hours total with color coding
                this.updateHoursTotal(totalHours);
            }

            updateHoursTotal(hours) {
                const hoursElement = document.getElementById('today-hours-total');
                const circleElement = document.getElementById('today-hours-circle');
                
                hoursElement.textContent = `${hours}h`;
                circleElement.textContent = `${hours}h`;
                
                // Remove existing color classes from both elements
                hoursElement.classList.remove('low', 'medium', 'high', 'critical');
                circleElement.classList.remove('low', 'medium', 'high', 'critical');
                
                // Determine color class based on hours
                let colorClass;
                if (hours < 4) {
                    colorClass = 'low';
                } else if (hours >= 4 && hours <= 6) {
                    colorClass = 'medium';
                } else if (hours >= 7 && hours <= 8) {
                    colorClass = 'high';
                } else if (hours >= 9) {
                    colorClass = 'critical';
                }
                
                // Apply color class to both elements
                hoursElement.classList.add(colorClass);
                circleElement.classList.add(colorClass);

                // Add subtle pulsing animation for high workloads
                if (hours >= 9) {
                    hoursElement.style.animation = 'pulse 2s infinite';
                    circleElement.style.animation = 'pulse 2s infinite';
                } else {
                    hoursElement.style.animation = 'none';
                    circleElement.style.animation = 'none';
                }
            }

            clearDailyPlan() {
                this.selectedTodos.clear();
                this.renderAvailableTodos();
                this.renderDailyTodos();
                this.updateStats();
                TodoUtils.toast.info('Daily plan cleared');
            }

            saveDailyPlan() {
                const selectedTodoItems = this.allTodos.filter(todo => 
                    this.selectedTodos.has(todo.id)
                );
                
                // Save to localStorage
                const dailyPlan = {
                    date: new Date().toISOString().split('T')[0],
                    todos: selectedTodoItems.map(todo => ({
                        id: todo.id,
                        title: todo.title,
                        projectName: todo.projectName,
                        priority: todo.priority,
                        estimated_hours: todo.estimated_hours
                    }))
                };

                TodoUtils.storage.set('dailyPlan', dailyPlan);
                TodoUtils.toast.success('Daily plan saved');
            }

            loadDailyPlan() {
                const savedPlan = TodoUtils.storage.get('dailyPlan');
                const today = new Date().toISOString().split('T')[0];

                if (savedPlan && savedPlan.date === today) {
                    this.selectedTodos = new Set(savedPlan.todos.map(todo => todo.id));
                    this.renderDailyTodos();
                    this.updateStats();
                }
            }

            async generateScrumReport() {
                const selectedTodoItems = this.allTodos.filter(todo => 
                    this.selectedTodos.has(todo.id)
                );

                if (selectedTodoItems.length === 0) {
                    TodoUtils.toast.warning('Please select at least one task for today');
                    return;
                }

                try {
                    // Create a summary of selected tasks
                    const taskSummary = selectedTodoItems.map(todo => 
                        `- ${todo.title} (${TodoUtils.formatters.todoDisplayName(todo)})`
                    ).join('\\n');

                    // For now, create a simple report locally
                    // TODO: Call claude-command.py script
                    const report = this.generateLocalScrumReport(selectedTodoItems);
                    
                    this.elements.scrumReport.textContent = report;
                    document.getElementById('copy-scrum-btn').disabled = false;
                    
                    TodoUtils.toast.success('Scrum report generated');

                } catch (error) {
                    console.error('Error generating scrum report:', error);
                    TodoUtils.toast.error('Failed to generate scrum report');
                }
            }

            generateLocalScrumReport(todos) {
                const today = new Date().toLocaleDateString();
                const projects = [...new Set(todos.map(TodoUtils.formatters.todoDisplayName))];
                const totalHours = todos.reduce((sum, todo) => sum + (todo.estimated_hours || 1), 0);
                
                const priorityCounts = todos.reduce((acc, todo) => {
                    acc[todo.priority] = (acc[todo.priority] || 0) + 1;
                    return acc;
                }, {});

                const priorityText = Object.entries(priorityCounts)
                    .map(([priority, count]) => `${count} ${priority}`)
                    .join(', ');

                return `Daily Scrum - ${today}

Focus Areas: ${projects.join(', ')}
Tasks Planned: ${todos.length} (${priorityText})
Estimated Effort: ${totalHours}h

Key Tasks:
${todos.slice(0, 3).map(t => `• ${t.title}`).join('\\n')}${todos.length > 3 ? `\\n+ ${todos.length - 3} more tasks` : ''}

Ready to deliver value and tackle today's challenges!`;
            }

            async copyToClipboard() {
                try {
                    await navigator.clipboard.writeText(this.elements.scrumReport.textContent);
                    TodoUtils.toast.success('Report copied to clipboard');
                } catch (error) {
                    console.error('Failed to copy:', error);
                    TodoUtils.toast.error('Failed to copy to clipboard');
                }
            }

            showAddTaskModal() {
                // Get the currently selected project from the filter
                const selectedProject = this.elements.projectSelect.value;
                
                if (selectedProject === 'all') {
                    TodoUtils.toast.info('Please select a specific project to add a task');
                    return;
                }

                // Find the project object to get the display name
                const project = this.projects.find(p => (p.name || p) === selectedProject);
                const displayName = TodoUtils.formatters.getProjectDisplay(project) || selectedProject;

                // Redirect to the project page where the add task functionality exists
                const projectUrl = `project.html?project=${encodeURIComponent(selectedProject)}`;
                window.open(projectUrl, '_blank');
                TodoUtils.toast.info(`Opening ${displayName} project page to add task`);
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.todayPage = new TodayPage();
        });
    </script>
</body>
</html>