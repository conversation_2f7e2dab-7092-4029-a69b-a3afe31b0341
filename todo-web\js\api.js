/**
 * API Integration Layer
 * Handles all communication with the Todo Management API
 */

class TodoAPI {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
        this.apiBase = `${baseUrl}/api/v1`;
    }

    /**
     * Generic HTTP request handler with error handling
     */
    async request(endpoint, options = {}) {
        const url = `${this.apiBase}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        console.log(`[API] ${options.method || 'GET'} ${url}`);

        try {
            const response = await fetch(url, config);
            
            console.log(`[API] Response status: ${response.status}`);
            
            // Handle different response types
            if (response.status === 204) {
                return { success: true };
            }

            const data = await response.json();
            console.log(`[API] Response data:`, data);

            if (!response.ok) {
                console.error(`[API] Full error response:`, data);
                let errorMsg;
                if (data.detail && Array.isArray(data.detail)) {
                    // FastAPI validation errors
                    errorMsg = data.detail.map(err => `${err.loc?.join('.')} : ${err.msg}`).join('; ');
                    console.error(`[API] Validation errors:`, data.detail);
                } else {
                    errorMsg = data.detail || data.error || JSON.stringify(data) || `HTTP ${response.status}: ${response.statusText}`;
                }
                console.error(`[API] Error message:`, errorMsg);
                throw new Error(errorMsg);
            }

            return data;
        } catch (error) {
            console.error(`[API] Request failed:`, error);
            
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                const connectError = 'Cannot connect to API server. Please ensure it is running on ' + this.baseUrl;
                console.error(`[API] Connection error:`, connectError);
                throw new Error(connectError);
            }
            
            if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
                const jsonError = 'Invalid JSON response from server';
                console.error(`[API] JSON parse error:`, jsonError);
                throw new Error(jsonError);
            }
            
            throw error;
        }
    }

    /**
     * Health check endpoint
     */
    async healthCheck() {
        return this.request('/health');
    }

    /**
     * Security/Whitelist management
     */
    async getWhitelistedDirectories() {
        const response = await this.request('/security/whitelist');
        return response.whitelisted_directories || [];
    }

    async addWhitelistedDirectory(directory) {
        return this.request('/security/whitelist', {
            method: 'POST',
            body: JSON.stringify({ directory })
        });
    }

    async removeWhitelistedDirectory(directory) {
        return this.request('/security/whitelist', {
            method: 'DELETE',
            body: JSON.stringify({ directory })
        });
    }

    /**
     * Project management
     */
    async getProjects() {
        const response = await this.request('/projects');
        return response.projects || [];
    }

    async getProject(projectName) {
        return this.request(`/projects/${encodeURIComponent(projectName)}`);
    }

    /**
     * Todo management
     */
    async getTodos(projectName, filters = {}) {
        const queryParams = new URLSearchParams();
        
        if (filters.status) queryParams.append('status', filters.status);
        if (filters.priority) queryParams.append('priority', filters.priority);
        if (filters.category) queryParams.append('category', filters.category);
        if (filters.tag) queryParams.append('tag', filters.tag);

        const queryString = queryParams.toString();
        const endpoint = `/projects/${encodeURIComponent(projectName)}/todos${queryString ? '?' + queryString : ''}`;
        
        const response = await this.request(endpoint);
        return {
            todos: response.todos || [],
            metadata: response.metadata || {}
        };
    }

    async getTodo(projectName, todoId) {
        return this.request(`/projects/${encodeURIComponent(projectName)}/todos/${encodeURIComponent(todoId)}`);
    }

    async createTodo(projectName, todoData) {
        return this.request(`/projects/${encodeURIComponent(projectName)}/todos`, {
            method: 'POST',
            body: JSON.stringify(todoData)
        });
    }

    async updateTodo(projectName, todoId, todoData) {
        return this.request(`/projects/${encodeURIComponent(projectName)}/todos/${encodeURIComponent(todoId)}`, {
            method: 'PUT',
            body: JSON.stringify(todoData)
        });
    }

    async patchTodo(projectName, todoId, updates) {
        return this.request(`/projects/${encodeURIComponent(projectName)}/todos/${encodeURIComponent(todoId)}`, {
            method: 'PATCH',
            body: JSON.stringify(updates)
        });
    }

    async deleteTodo(projectName, todoId) {
        return this.request(`/projects/${encodeURIComponent(projectName)}/todos/${encodeURIComponent(todoId)}`, {
            method: 'DELETE'
        });
    }

    /**
     * Convenience methods for common operations
     */
    async markTodoComplete(projectName, todoId) {
        return this.patchTodo(projectName, todoId, { status: 'completed' });
    }

    async markTodoPending(projectName, todoId) {
        return this.patchTodo(projectName, todoId, { status: 'pending' });
    }

    async markTodoInProgress(projectName, todoId) {
        return this.patchTodo(projectName, todoId, { status: 'in_progress' });
    }

    /**
     * Batch operations
     */
    async getProjectSummary(projectName) {
        try {
            const [project, todosData] = await Promise.all([
                this.getProject(projectName),
                this.getTodos(projectName)
            ]);

            const todos = todosData.todos;
            const totalTodos = todos.length;
            const completedTodos = todos.filter(t => t.status === 'completed').length;
            const pendingTodos = todos.filter(t => t.status === 'pending').length;
            const inProgressTodos = todos.filter(t => t.status === 'in_progress').length;
            const completionRate = totalTodos > 0 ? (completedTodos / totalTodos * 100) : 0;

            // Priority breakdown
            const priorities = todos.reduce((acc, todo) => {
                acc[todo.priority] = (acc[todo.priority] || 0) + 1;
                return acc;
            }, {});

            return {
                name: project.name,
                path: project.path,
                totalTodos,
                completedTodos,
                pendingTodos,
                inProgressTodos,
                completionRate: Math.round(completionRate * 10) / 10, // Round to 1 decimal
                priorities,
                lastUpdated: project.metadata?.last_updated,
                project_type: project.metadata?.project_type || 'Work',
                display_name: project.metadata?.display_name,
                metadata: project.metadata
            };
        } catch (error) {
            console.warn(`Failed to get summary for project ${projectName}:`, error);
            return {
                name: projectName,
                path: '',
                totalTodos: 0,
                completedTodos: 0,
                pendingTodos: 0,
                inProgressTodos: 0,
                completionRate: 0,
                priorities: {},
                error: error.message
            };
        }
    }

    /**
     * Setup helper for first-time use
     */
    async setupWhitelist() {
        try {
            // Try to whitelist the common source directory
            const homeDir = 'C:/Users/<USER>/source/repos'; // Windows path
            const altHomeDir = '/home/<USER>/source/repos';   // Unix path
            
            try {
                await this.addWhitelistedDirectory(homeDir);
                return homeDir;
            } catch (error) {
                await this.addWhitelistedDirectory(altHomeDir);
                return altHomeDir;
            }
        } catch (error) {
            throw new Error('Failed to setup whitelist. Please use the API Test page to manually whitelist directories.');
        }
    }

    /**
     * Get all unmanaged projects (projects without todo management structure)
     */
    async getUnmanagedProjects() {
        return await this.request('/unmanaged-projects');
    }

    /**
     * Enable todo management for an existing project
     */
    async enableTodoManagement(projectName) {
        return await this.request(`/projects/${encodeURIComponent(projectName)}/enable-todo-management`, {
            method: 'POST'
        });
    }

    /**
     * Update project metadata (like project type)
     */
    async updateProjectMetadata(projectName, metadata) {
        console.log('[API] updateProjectMetadata called with:', {projectName, metadata});
        const result = await this.request(`/projects/${encodeURIComponent(projectName)}/metadata`, {
            method: 'PATCH',
            body: JSON.stringify(metadata)
        });
        console.log('[API] updateProjectMetadata result:', result);
        return result;
    }

    /**
     * Generate project progress report HTML
     */
    async generateProgressReport(projectName) {
        return await this.request(`/projects/${encodeURIComponent(projectName)}/progress-report`);
    }

    // =====================================================================
    // WORKFLOW MANAGEMENT API METHODS
    // =====================================================================

    /**
     * Get list of available workflows
     */
    async getAvailableWorkflows() {
        const url = `${this.baseUrl}/api/workflows`;
        console.log(`[API] GET ${url}`);

        try {
            const response = await fetch(url);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.detail || `HTTP ${response.status}: ${response.statusText}`);
            }

            return data;
        } catch (error) {
            console.error('[API] Error loading workflows:', error);
            throw error;
        }
    }

    /**
     * Start a workflow execution
     * @param {Object} config - Workflow configuration
     */
    async startWorkflow(config) {
        const { workflow_id, ...requestConfig } = config;
        const url = `${this.baseUrl}/api/workflows/${workflow_id}/start`;
        console.log(`[API] POST ${url}`, requestConfig);

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestConfig)
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.detail || `HTTP ${response.status}: ${response.statusText}`);
            }

            return data;
        } catch (error) {
            console.error('[API] Error starting workflow:', error);
            throw error;
        }
    }

    /**
     * Get workflow execution status
     * @param {string} executionId - Execution ID
     */
    async getWorkflowStatus(executionId) {
        const url = `${this.baseUrl}/api/workflows/executions/${executionId}/status`;
        console.log(`[API] GET ${url}`);

        try {
            const response = await fetch(url);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.detail || `HTTP ${response.status}: ${response.statusText}`);
            }

            return data;
        } catch (error) {
            console.error('[API] Error getting workflow status:', error);
            throw error;
        }
    }

    /**
     * Stop a running workflow
     * @param {string} executionId - Execution ID
     */
    async stopWorkflow(executionId) {
        const url = `${this.baseUrl}/api/workflows/executions/${executionId}/stop`;
        console.log(`[API] POST ${url}`);

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.detail || `HTTP ${response.status}: ${response.statusText}`);
            }

            return data;
        } catch (error) {
            console.error('[API] Error stopping workflow:', error);
            throw error;
        }
    }

    /**
     * Get workflow status HTML file content
     * @param {string} executionId - Execution ID
     */
    async getWorkflowStatusHtml(executionId) {
        const url = `${this.baseUrl}/api/workflows/executions/${executionId}/status-html`;
        console.log(`[API] GET ${url}`);

        try {
            const response = await fetch(url);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.detail || `HTTP ${response.status}: ${response.statusText}`);
            }

            return data;
        } catch (error) {
            console.error('[API] Error getting workflow status HTML:', error);
            throw error;
        }
    }

    /**
     * Get complete workflow definition
     */
    async getWorkflowDefinition(workflowId) {
        try {
            const url = `${this.baseUrl}/api/workflows/${workflowId}`;
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`Failed to load workflow definition: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('[API] Error getting workflow definition:', error);
            throw error;
        }
    }

    /**
     * Update a workflow step
     */
    async updateWorkflowStep(workflowId, stepFilename, stepConfig) {
        try {
            const url = `${this.baseUrl}/api/workflows/${workflowId}/steps/${stepFilename}`;
            const response = await fetch(url, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(stepConfig)
            });
            
            if (!response.ok) {
                throw new Error(`Failed to update step: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('[API] Error updating workflow step:', error);
            throw error;
        }
    }
}

// Create global API instance
window.todoAPI = new TodoAPI();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TodoAPI;
}