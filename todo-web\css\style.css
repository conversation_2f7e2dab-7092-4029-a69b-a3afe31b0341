/* Todo Management System - Jomez Pro Theme */

/* CSS Variables - Jomez Pro Color Palette */
:root {
    --jomezpro-teal: #00c4aa;
    --jomezpro-blue: #0099cc;
    --jomezpro-bright-teal: #1dd1a1;
    --jomezpro-dark: #2c3e50;
    --jomezpro-gray: #34495e;
    --jomezpro-light-gray: #7f8c8d;
    --jomezpro-bg: #f8f9fa;
    --jomezpro-white: #ffffff;
    --jomezpro-border: #dee2e6;
    --jomezpro-shadow: rgba(0, 0, 0, 0.1);
    --jomezpro-success: #27ae60;
    --jomezpro-warning: #f39c12;
    --jomezpro-error: #e74c3c;
    --jomezpro-info: #3498db;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--jomezpro-dark);
    background-color: var(--jomezpro-bg);
}

/* Typography */
h1 {
    font-size: 2.5em;
    font-weight: 700;
    color: var(--jomezpro-white);
    margin-bottom: 0.5rem;
}

h2 {
    font-size: 2em;
    font-weight: 600;
    color: var(--jomezpro-dark);
    margin-bottom: 1rem;
}

h3 {
    font-size: 1.5em;
    font-weight: 600;
    color: var(--jomezpro-gray);
    margin-bottom: 0.75rem;
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.hidden {
    display: none !important;
}

/* Header */
.main-header {
    background: linear-gradient(135deg, var(--jomezpro-blue) 0%, var(--jomezpro-blue) 75%, var(--jomezpro-teal) 100%);
    box-shadow: 0 2px 10px var(--jomezpro-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
}

.main-nav ul {
    list-style: none;
    display: flex;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.main-nav a {
    color: var(--jomezpro-white);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    transition: background-color 0.3s ease;
}

.main-nav a:hover,
.main-nav a[aria-current="page"] {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Main Content */
.main-content {
    padding: 6rem 0 2rem 0;  /* Add top padding to account for fixed header */
    min-height: calc(100vh - 200px);
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 4rem 2rem;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid var(--jomezpro-border);
    border-top: 3px solid var(--jomezpro-teal);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error State */
.error-state {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--jomezpro-white);
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px var(--jomezpro-shadow);
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.error-state h2 {
    color: var(--jomezpro-error);
    margin-bottom: 1rem;
}

.error-details {
    margin-top: 2rem;
    text-align: left;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.error-details summary {
    cursor: pointer;
    font-weight: 600;
    color: var(--jomezpro-gray);
    margin-bottom: 1rem;
}

.error-details ol {
    padding-left: 1.5rem;
}

.error-details code {
    background: var(--jomezpro-bg);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: 'Consolas', 'Monaco', monospace;
}

/* Dashboard Summary */
.dashboard-summary {
    margin-bottom: 3rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.summary-card {
    background: var(--jomezpro-white);
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px var(--jomezpro-shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
}

.summary-icon {
    font-size: 2rem;
}

.summary-info {
    display: flex;
    flex-direction: column;
}

.summary-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--jomezpro-teal);
}

.summary-label {
    color: var(--jomezpro-light-gray);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Dashboard Controls */
.dashboard-controls {
    margin-bottom: 2rem;
}

.controls-bar {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
    background: var(--jomezpro-white);
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px var(--jomezpro-shadow);
}

.search-container {
    flex: 1;
    min-width: 250px;
}

.search-container input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--jomezpro-border);
    border-radius: 0.375rem;
    font-size: 1rem;
}

.search-container input:focus {
    outline: none;
    border-color: var(--jomezpro-teal);
    box-shadow: 0 0 0 3px rgba(0, 196, 170, 0.1);
}

.filter-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-container label {
    font-weight: 500;
    color: var(--jomezpro-gray);
}

.filter-container select {
    padding: 0.75rem;
    border: 1px solid var(--jomezpro-border);
    border-radius: 0.375rem;
    background: var(--jomezpro-white);
}

/* Project Type Filters */
.project-type-filters {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.filter-label {
    font-weight: 500;
    color: var(--jomezpro-gray);
    font-size: 0.875rem;
}

.checkbox-group {
    display: flex;
    gap: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid transparent;
}

.checkbox-label:hover {
    background-color: var(--jomezpro-bg);
    border-color: var(--jomezpro-border);
}

.checkbox-label input[type="checkbox"] {
    width: 1.125rem;
    height: 1.125rem;
    cursor: pointer;
    accent-color: var(--jomezpro-teal);
}

.checkbox-text {
    font-weight: 500;
    color: var(--jomezpro-dark);
    font-size: 0.875rem;
    user-select: none;
}

.checkbox-label:has(input:checked) {
    background-color: rgba(0, 196, 170, 0.1);
    border-color: var(--jomezpro-teal);
}

.checkbox-label:has(input:checked) .checkbox-text {
    color: var(--jomezpro-teal);
    font-weight: 600;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, var(--jomezpro-teal) 0%, var(--jomezpro-blue) 100%);
    color: var(--jomezpro-white);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--jomezpro-shadow);
}

.btn-secondary {
    background: var(--jomezpro-white);
    color: var(--jomezpro-gray);
    border: 1px solid var(--jomezpro-border);
}

.btn-secondary:hover {
    background: var(--jomezpro-bg);
}

.btn-icon {
    min-width: auto;
    padding: 0.75rem;
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2em;
}

.btn-info {
    background: var(--jomezpro-info);
    color: var(--jomezpro-white);
    border: 1px solid var(--jomezpro-info);
}

.btn-info:hover {
    background: #2980b9;
    border-color: #2980b9;
}

/* Projects Grid */
.projects-section h2 {
    margin-bottom: 1.5rem;
}

.projects-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.projects-header h2 {
    margin-bottom: 0;
}

.projects-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.project-card {
    background: var(--jomezpro-white);
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px var(--jomezpro-shadow);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid transparent;
}

.project-card:hover,
.project-card:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px var(--jomezpro-shadow);
    border-color: var(--jomezpro-teal);
    outline: none;
}

.project-card.home-project {
    background: rgba(0, 196, 170, 0.03);
    border-color: rgba(0, 196, 170, 0.15);
}

.project-card.home-project:hover,
.project-card.home-project:focus {
    background: rgba(0, 196, 170, 0.08);
    border-color: var(--jomezpro-teal);
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.project-title-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.project-header h3 {
    margin: 0;
    color: var(--jomezpro-dark);
}

.btn-icon {
    background: none;
    border: none;
    padding: 0.25rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 1.5rem;
    height: 1.5rem;
}

.btn-icon:hover {
    background-color: var(--jomezpro-bg);
}

.project-completion {
    font-weight: 600;
    color: var(--jomezpro-light-gray);
    font-size: 0.875rem;
}

.project-completion.completed {
    color: var(--jomezpro-success);
}

.project-progress {
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 0.5rem;
    background: var(--jomezpro-border);
    border-radius: 0.25rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--jomezpro-teal) 0%, var(--jomezpro-blue) 100%);
    transition: width 0.3s ease;
}

.project-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--jomezpro-light-gray);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--jomezpro-teal);
}

.project-priorities {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.priority-tag {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-critical {
    background: rgba(231, 76, 60, 0.1);
    color: var(--jomezpro-error);
}

.priority-high {
    background: rgba(243, 156, 18, 0.1);
    color: var(--jomezpro-warning);
}

.priority-medium {
    background: rgba(52, 152, 219, 0.1);
    color: var(--jomezpro-info);
}

.priority-low {
    background: rgba(39, 174, 96, 0.1);
    color: var(--jomezpro-success);
}

.project-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.project-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.project-type {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    display: inline-block;
}

.project-type.work-type {
    background: rgba(52, 73, 94, 0.1);
    color: var(--jomezpro-gray);
}

.project-type.home-type {
    background: rgba(0, 196, 170, 0.15);
    color: var(--jomezpro-teal);
}

.project-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.last-updated {
    font-size: 0.75rem;
    color: var(--jomezpro-light-gray);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--jomezpro-white);
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px var(--jomezpro-shadow);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-details {
    margin-top: 2rem;
    text-align: left;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.empty-details summary {
    cursor: pointer;
    font-weight: 600;
    color: var(--jomezpro-gray);
    margin-bottom: 1rem;
}

.empty-details code {
    background: var(--jomezpro-bg);
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-family: 'Consolas', 'Monaco', monospace;
    display: block;
    margin: 0.5rem 0;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: var(--jomezpro-white);
    border-radius: 0.5rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.25);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--jomezpro-border);
}

.modal-header h2 {
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--jomezpro-light-gray);
    padding: 0.5rem;
}

.modal-close:hover {
    color: var(--jomezpro-dark);
}

.modal-body {
    padding: 1.5rem;
}

.modal-body ul {
    padding-left: 1.5rem;
}

/* Form Styles */
.form-field {
    margin-bottom: 1.5rem;
}

.form-field label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--jomezpro-dark);
}

.form-field input,
.form-field textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--jomezpro-border);
    border-radius: 0.375rem;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-field input:focus,
.form-field textarea:focus {
    outline: none;
    border-color: var(--jomezpro-teal);
    box-shadow: 0 0 0 3px rgba(0, 196, 170, 0.1);
}

.form-field small {
    display: block;
    margin-top: 0.25rem;
    color: var(--jomezpro-light-gray);
    font-size: 0.875rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

/* Projects List */
.projects-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--jomezpro-border);
    border-radius: 0.375rem;
    padding: 1rem;
}

.project-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid var(--jomezpro-border);
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.project-item:hover {
    background: var(--jomezpro-bg);
    border-color: var(--jomezpro-teal);
}

.project-item:last-child {
    margin-bottom: 0;
}

.project-info h4 {
    margin: 0 0 0.25rem 0;
    color: var(--jomezpro-dark);
}

.project-info small {
    color: var(--jomezpro-light-gray);
}

.project-item.managed-project {
    border-color: var(--jomezpro-warning);
    background-color: rgba(243, 156, 18, 0.05);
}

.project-item.managed-project:hover {
    background-color: rgba(243, 156, 18, 0.1);
    border-color: var(--jomezpro-warning);
}

.project-item.managed-project .project-info h4 {
    color: var(--jomezpro-warning);
}

/* Footer */
.main-footer {
    background: var(--jomezpro-gray);
    color: var(--jomezpro-white);
    text-align: center;
    padding: 2rem 0;
    margin-top: 4rem;
}

.main-footer a {
    color: var(--jomezpro-teal);
    text-decoration: none;
}

.main-footer a:hover {
    text-decoration: underline;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toast {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-radius: 0.375rem;
    box-shadow: 0 4px 12px var(--jomezpro-shadow);
    min-width: 300px;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.toast-success {
    background: var(--jomezpro-success);
    color: var(--jomezpro-white);
}

.toast-error {
    background: var(--jomezpro-error);
    color: var(--jomezpro-white);
}

.toast-warning {
    background: var(--jomezpro-warning);
    color: var(--jomezpro-white);
}

.toast-info {
    background: var(--jomezpro-info);
    color: var(--jomezpro-white);
}

.toast-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0 0 0 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .main-nav ul {
        flex-direction: column;
        width: 100%;
        text-align: center;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .controls-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .search-container {
        min-width: auto;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .project-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .project-actions {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }

    .toast-container {
        left: 1rem;
        right: 1rem;
    }

    .toast {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 0.5rem;
    }

    h1 {
        font-size: 2rem;
    }

    .summary-card {
        padding: 1rem;
    }

    .project-card {
        padding: 1rem;
    }

    .modal-content {
        width: 95%;
    }
}

/* Print Styles */
@media print {
    .main-header,
    .main-footer,
    .dashboard-controls,
    .project-actions,
    .toast-container {
        display: none;
    }

    .main-content {
        padding: 0;
    }

    .project-card {
        box-shadow: none;
        border: 1px solid var(--jomezpro-border);
        break-inside: avoid;
        margin-bottom: 1rem;
    }

    .projects-grid {
        gap: 1rem;
    }
}

/* ===================================================================== */
/* NEW COLLAPSIBLE TODO DESIGN */
/* ===================================================================== */

/* Todo Item Container */
.todo-item {
    background: var(--jomezpro-white);
    border-radius: 4px;
    border: 1px solid var(--jomezpro-border);
    margin-bottom: 0.125rem;
    box-shadow: 0 1px 2px var(--jomezpro-shadow);
    transition: all 0.2s ease;
    overflow: hidden;
}

.todo-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.todo-item.collapsed {
    cursor: pointer;
}

/* Todo Wrapper Layout */
.todo-wrapper {
    display: flex;
    align-items: flex-start;
    padding: 4px 8px;
    gap: 6px;
}

/* Complete Button (replaces checkbox) */
.todo-complete-btn {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid var(--jomezpro-border);
    border-radius: 4px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.todo-complete-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

.todo-complete-btn.completed {
    background: var(--jomezpro-success);
    color: white;
    border-color: var(--jomezpro-success);
}

/* Todo Content */
.todo-content {
    flex: 1;
    min-width: 0;
}

/* Collapsed View */
.todo-collapsed-view {
    padding: 1px 0;
}

.todo-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.todo-title {
    font-size: 1.1em;
    font-weight: 500;
    color: var(--jomezpro-dark);
    margin: 0;
    line-height: 1.3;
    flex: 1;
}

.todo-status-badges {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

/* Expanded View */
.todo-expanded-view {
    padding-top: 6px;
    border-top: 1px solid var(--jomezpro-border);
    margin-top: 6px;
}

.todo-expanded-view.hidden {
    display: none;
}

.todo-item-header {
    margin-bottom: 4px;
}

.todo-meta-info {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 4px;
}

.todo-description {
    color: var(--jomezpro-gray);
    margin-bottom: 6px;
    line-height: 1.3;
}

.todo-details {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 4px;
    font-size: 0.9em;
}

.todo-detail {
    color: var(--jomezpro-light-gray);
}

/* Status Badges */
.todo-status {
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-in_progress {
    background: #cce5ff;
    color: #0066cc;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-blocked {
    background: #f8d7da;
    color: #721c24;
}

.status-cancelled {
    background: #e2e3e5;
    color: #41464b;
}

/* Priority/Urgency Labels */
.todo-priority, .todo-urgency {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75em;
    font-weight: 500;
}

/* Tags */
.todo-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 12px;
}

.todo-tag {
    background: var(--jomezpro-teal);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75em;
    font-weight: 500;
}

/* Expanded Actions */
.todo-actions-expanded {
    margin-top: 12px;
}

.todo-actions-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.todo-actions-left {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
}

.todo-actions-right {
    display: flex;
    align-items: center;
}

.status-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-label {
    font-size: 0.9em;
    font-weight: 500;
    color: var(--jomezpro-gray);
    margin: 0;
}

.todo-status-dropdown {
    padding: 6px 12px;
    border: 1px solid var(--jomezpro-border);
    border-radius: 4px;
    background: var(--jomezpro-white);
    font-size: 0.9em;
    cursor: pointer;
}

/* Order Controls */
.todo-order-controls {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex-shrink: 0;
}

.btn-order {
    width: 24px;
    height: 20px;
    border: 1px solid var(--jomezpro-border);
    background: var(--jomezpro-white);
    border-radius: 3px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: var(--jomezpro-gray);
    transition: all 0.2s ease;
}

.btn-order:hover {
    background: var(--jomezpro-bg);
    color: var(--jomezpro-dark);
    transform: scale(1.1);
}

/* Subtasks */
.todo-subtasks {
    margin-bottom: 12px;
}

.todo-subtasks h4 {
    font-size: 0.9em;
    margin-bottom: 6px;
    color: var(--jomezpro-gray);
}

.subtask-list {
    list-style: none;
    padding-left: 0;
}

.subtask-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
}

.subtask-checkbox {
    width: 16px;
    height: 16px;
    border: 1px solid var(--jomezpro-border);
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    flex-shrink: 0;
}

.subtask-checkbox.completed {
    background: var(--jomezpro-success);
    color: white;
    border-color: var(--jomezpro-success);
}

.subtask-text {
    font-size: 0.9em;
    line-height: 1.3;
}

.subtask-text.completed {
    text-decoration: line-through;
    color: var(--jomezpro-light-gray);
}

/* Pagination Controls */
.pagination-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--jomezpro-white);
    padding: 1rem;
    border-radius: 0.375rem;
    border: 1px solid var(--jomezpro-border);
    margin-bottom: 1rem;
    gap: 1rem;
}

.pagination-left {
    flex: 0 0 auto;
}

.pagination-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
}

.pagination-info {
    color: var(--jomezpro-gray);
    font-size: 0.9em;
}

.pagination-size {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9em;
}

.pagination-size label {
    color: var(--jomezpro-gray);
    font-weight: 500;
}

.pagination-size select {
    padding: 0.5rem;
    border: 1px solid var(--jomezpro-border);
    border-radius: 0.25rem;
    background: var(--jomezpro-white);
    font-size: 0.9em;
}

.pagination-nav {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.pagination-nav .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9em;
}

.pagination-nav .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#page-numbers {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.page-number {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--jomezpro-border);
    border-radius: 0.25rem;
    background: var(--jomezpro-white);
    color: var(--jomezpro-gray);
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.page-number:hover {
    background: var(--jomezpro-bg);
    color: var(--jomezpro-dark);
}

.page-number.active {
    background: var(--jomezpro-teal);
    color: var(--jomezpro-white);
    border-color: var(--jomezpro-teal);
}

.page-ellipsis {
    padding: 0.5rem;
    color: var(--jomezpro-light-gray);
    font-size: 0.9em;
}

@media (max-width: 768px) {
    .pagination-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .pagination-left {
        order: -1;
        align-self: center;
    }
    
    .pagination-center {
        flex-direction: column;
        gap: 1rem;
    }
    
    .pagination-nav {
        justify-content: center;
    }
    
    .pagination-info, .pagination-size {
        justify-content: center;
    }
}