// Simple API test script
async function testAPI() {
    console.log('Testing API connectivity...');
    
    try {
        // Test health endpoint
        console.log('Testing health endpoint...');
        const response = await fetch('http://localhost:8000/api/v1/health');
        console.log('Health response status:', response.status);
        
        if (response.ok) {
            const data = await response.json();
            console.log('Health response data:', data);
        } else {
            console.error('Health check failed:', response.statusText);
            return false;
        }
        
        // Test projects endpoint
        console.log('Testing projects endpoint...');
        const projectsResponse = await fetch('http://localhost:8000/api/v1/projects');
        console.log('Projects response status:', projectsResponse.status);
        
        if (projectsResponse.ok) {
            const projectsData = await projectsResponse.json();
            console.log('Projects response data:', projectsData);
            
            if (projectsData.projects && projectsData.projects.length > 0) {
                console.log('✓ API is working correctly!');
                return true;
            } else {
                console.log('No projects found. You may need to whitelist directories.');
                return true;
            }
        } else {
            console.error('Projects check failed:', projectsResponse.statusText);
            return false;
        }
        
    } catch (error) {
        console.error('API test failed:', error);
        return false;
    }
}

// Run test when script loads
testAPI();