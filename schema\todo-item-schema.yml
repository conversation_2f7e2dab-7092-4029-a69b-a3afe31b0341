name: TodoItem
desc: Schema for validating todo items in the project management system
type: map
mapping:
  "_hash":
    type: str
    required: false
    default: ""
    desc: Unique hash identifier for the todo item
  "assigned_to":
    type: str
    required: false
    default: null
    desc: Person assigned to this todo item
  "category":
    type: str
    required: false
    default: "general"
    enum:
      - testing
      - general
      - infrastructure
      - documentation
      - development
      - integration
    desc: Category classification of the todo item
  "created_date":
    type: str
    required: false
    pattern: /^\d{4}-\d{2}-\d{2}$/
    desc: Date when the todo item was created (YYYY-MM-DD format)
  "dependencies":
    type: seq
    required: false
    default: []
    sequence:
      - type: str
    desc: List of dependencies for this todo item
  "description":
    type: str
    required: false
    default: ""
    desc: Detailed description of the todo item
  "due_date":
    type: str
    required: false
    default: null
    pattern: /^\d{4}-\d{2}-\d{2}$/
    desc: Due date for the todo item (YYYY-MM-DD format)
  "estimated_hours":
    type: number
    required: false
    default: 4.0
    range:
      min: 0
    desc: Estimated hours to complete the todo item
  "id":
    type: str
    required: true
    pattern: /^[a-zA-Z]+-[0-9]+$/
    desc: Unique identifier in format category-number
  "priority":
    type: str
    required: false
    default: "medium"
    enum:
      - critical
      - high
      - medium
      - low
    desc: Priority level of the todo item
  "section":
    type: str
    required: false
    default: "General Tasks"
    desc: Section or group this todo item belongs to
  "source_file":
    type: str
    required: false
    default: ""
    desc: Source file path where this todo originated
  "start_date":
    type: str
    required: false
    default: null
    pattern: /^\d{4}-\d{2}-\d{2}$/
    desc: Start date for the todo item (YYYY-MM-DD format)
  "status":
    type: str
    required: false
    default: "pending"
    enum:
      - pending
      - completed
      - in_progress
      - blocked
      - canceled
    desc: Current status of the todo item
  "parent_id":
    type: str
    required: false
    default: null
    pattern: /^[a-zA-Z]+-[0-9]+$/
    desc: ID of parent task if this is a subtask
  "tags":
    type: seq
    required: false
    default: []
    sequence:
      - type: str
    desc: Tags associated with this todo item
  "title":
    type: str
    required: true
    desc: Title of the todo item
  "urgency":
    type: str
    required: false
    default: "medium"
    enum:
      - critical
      - high
      - medium
      - low
    desc: Urgency level of the todo item
  "list_order":
    type: number
    required: false
    default: 1000
    desc: Position in the todo list for ordering