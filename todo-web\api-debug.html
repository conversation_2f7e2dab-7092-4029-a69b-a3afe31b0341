<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Debug - Todo Management System</title>
    <style>
        body { font-family: monospace; padding: 20px; }
        .test { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        .success { background: #eeffee; }
        .error { background: #ffeeee; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>API Debug Test</h1>
    <div id="results"></div>
    
    <button onclick="testGetProjects()">Test Get Projects</button>
    <button onclick="testGetProject('AcademicPetitions')">Test Get AcademicPetitions</button>
    <button onclick="testGetProject('AccountingCrmInterfaces')">Test Get AccountingCrmInterfaces</button>
    <button onclick="testGetTodos('AcademicPetitions')">Test Get AcademicPetitions Todos</button>
    <button onclick="testGetSummary('AcademicPetitions')">Test Get AcademicPetitions Summary</button>
    
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script>
        const results = document.getElementById('results');
        
        function addResult(test, success, data) {
            const div = document.createElement('div');
            div.className = `test ${success ? 'success' : 'error'}`;
            div.innerHTML = `
                <strong>${test}</strong><br>
                Status: ${success ? 'SUCCESS' : 'ERROR'}<br>
                Data: <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            results.appendChild(div);
            console.log(`${test}:`, success ? 'SUCCESS' : 'ERROR', data);
        }
        
        async function testGetProjects() {
            try {
                const projects = await todoAPI.getProjects();
                addResult('Get Projects', true, projects);
            } catch (error) {
                addResult('Get Projects', false, error.message);
            }
        }
        
        async function testGetProject(projectName) {
            try {
                const project = await todoAPI.getProject(projectName);
                addResult(`Get Project: ${projectName}`, true, project);
            } catch (error) {
                addResult(`Get Project: ${projectName}`, false, error.message);
            }
        }
        
        async function testGetTodos(projectName) {
            try {
                const todos = await todoAPI.getTodos(projectName);
                addResult(`Get Todos: ${projectName}`, true, {
                    todoCount: todos.todos ? todos.todos.length : 0,
                    todos: todos
                });
            } catch (error) {
                addResult(`Get Todos: ${projectName}`, false, error.message);
            }
        }
        
        async function testGetSummary(projectName) {
            try {
                const summary = await todoAPI.getProjectSummary(projectName);
                addResult(`Get Summary: ${projectName}`, true, summary);
            } catch (error) {
                addResult(`Get Summary: ${projectName}`, false, error.message);
            }
        }
        
        // Auto-run basic test
        setTimeout(testGetProjects, 1000);
    </script>
</body>
</html>