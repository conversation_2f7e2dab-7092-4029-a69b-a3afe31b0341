<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Planner - Todo Management System</title>
    <link rel="stylesheet" href="css/style.css">
    <meta name="description" content="Weekly Task Planning and Goal Setting">
    <style>
        /* Additional styles specific to Weekly page */
        .weekly-header {
            background: linear-gradient(135deg, var(--jomezpro-teal) 0%, var(--jomezpro-blue) 100%);
            color: var(--jomezpro-white);
            padding: 1rem 0;
            margin-bottom: 1rem;
        }

        /* Override main-content padding for Weekly page to reduce white space */
        .main-content {
            padding: 1rem 0 2rem 0;
        }

        .weekly-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .weekly-header h1 {
            font-size: 1.8rem;
            margin-bottom: 0;
        }

        .week-navigation {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .week-nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: var(--jomezpro-white);
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .week-nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .week-display {
            font-size: 1.1rem;
            font-weight: 600;
            text-align: center;
            min-width: 200px;
        }

        /* Weekly Calendar Grid */
        .weekly-calendar {
            background: var(--jomezpro-white);
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow-x: auto;
        }

        .month-header {
            text-align: center;
            margin-bottom: 1rem;
            padding: 0.75rem 0;
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--jomezpro-dark);
            border-bottom: 2px solid var(--jomezpro-teal);
            position: relative;
        }

        .month-header .month-transition {
            display: inline-block;
            position: relative;
        }

        .month-header .month-separator {
            color: var(--jomezpro-teal);
            font-weight: 700;
            margin: 0 0.5rem;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: var(--jomezpro-border);
            border-radius: 0.5rem;
            overflow: hidden;
            max-width: 100%;
            width: 100%;
            min-width: 800px;
        }

        .day-column {
            background: var(--jomezpro-white);
            min-height: 400px;
            display: flex;
            flex-direction: column;
        }

        .day-column.weekend {
            background: rgba(29, 209, 161, 0.1);
        }

        .day-column.today {
            border: 2px solid var(--jomezpro-teal);
            box-shadow: 0 4px 16px rgba(0, 196, 170, 0.15);
        }

        .day-header {
            padding: 1rem;
            border-bottom: 1px solid var(--jomezpro-border);
            text-align: center;
            background: var(--jomezpro-bg);
        }

        .day-column.weekend .day-header {
            background: rgba(29, 209, 161, 0.2);
        }

        .day-column.today .day-header {
            background: linear-gradient(135deg, var(--jomezpro-teal), var(--jomezpro-accent));
            color: var(--jomezpro-white);
        }

        .day-column.today .day-name {
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .day-column.today .day-date {
            background: rgba(255, 255, 255, 0.2);
            color: var(--jomezpro-white);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin: 0 auto;
            margin-top: 0.25rem;
        }

        .day-name {
            font-weight: 600;
            color: var(--jomezpro-dark);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .day-date {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--jomezpro-teal);
            margin-top: 0.25rem;
        }

        .day-content {
            flex: 1;
            padding: 1rem;
            position: relative;
        }

        .day-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.75rem;
            color: var(--jomezpro-light-gray);
        }

        .day-hours {
            font-weight: 600;
        }

        .day-tasks {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .task-item {
            background: var(--jomezpro-bg);
            border: 1px solid var(--jomezpro-border);
            border-radius: 0.375rem;
            padding: 0.75rem;
            cursor: move;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .task-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .task-item.high-priority {
            border-left: 4px solid var(--jomezpro-error);
        }

        .task-item.medium-priority {
            border-left: 4px solid var(--jomezpro-warning);
        }

        .task-item.low-priority {
            border-left: 4px solid var(--jomezpro-success);
        }

        .task-item.suggested {
            opacity: 0.3;
            border-style: dashed;
            background: linear-gradient(45deg, rgba(0, 196, 170, 0.05) 25%, transparent 25%), 
                        linear-gradient(-45deg, rgba(0, 196, 170, 0.05) 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, rgba(0, 196, 170, 0.05) 75%), 
                        linear-gradient(-45deg, transparent 75%, rgba(0, 196, 170, 0.05) 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            border-color: var(--jomezpro-teal);
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .task-item.suggested:hover {
            opacity: 0.9;
            background: rgba(0, 196, 170, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 196, 170, 0.2);
            border-style: solid;
        }

        .task-item.suggested:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0, 196, 170, 0.15);
        }

        .task-item.suggested::before {
            content: "💡";
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--jomezpro-warning);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            border: 2px solid var(--jomezpro-white);
        }

        .day-suggestions {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px dashed var(--jomezpro-border);
        }

        .suggestions-header {
            font-size: 0.75rem;
            color: var(--jomezpro-light-gray);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .suggestions-toggle {
            background: none;
            border: none;
            color: var(--jomezpro-teal);
            cursor: pointer;
            font-size: 0.75rem;
            padding: 0;
            text-decoration: underline;
        }

        .suggestions-toggle:hover {
            color: var(--jomezpro-dark);
        }

        .day-suggestions[data-expanded="true"] .suggestions-list {
            display: block !important;
        }

        .day-suggestions[data-expanded="false"] .suggestions-list {
            display: none !important;
        }

        /* Daily Planning Modal Styles */
        .modal-large .modal-content {
            max-width: 1200px;
            width: 95vw;
            max-height: 90vh;
            overflow-y: auto;
        }

        .daily-planning-container {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .planning-day-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: var(--jomezpro-bg);
            border-radius: 0.5rem;
            border: 1px solid var(--jomezpro-border);
        }

        .day-info h3 {
            margin: 0;
            color: var(--jomezpro-dark);
            font-size: 1.5rem;
        }

        .day-info div {
            color: var(--jomezpro-light-gray);
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .day-summary {
            display: flex;
            gap: 2rem;
        }

        .summary-stat {
            text-align: center;
        }

        .summary-stat .stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--jomezpro-teal);
            line-height: 1;
        }

        .summary-stat .stat-label {
            font-size: 0.75rem;
            color: var(--jomezpro-light-gray);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .planning-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1.5rem;
        }

        .planning-column {
            background: var(--jomezpro-white);
            border: 1px solid var(--jomezpro-border);
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .column-header {
            padding: 1rem;
            background: var(--jomezpro-bg);
            border-bottom: 1px solid var(--jomezpro-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .column-header h4 {
            margin: 0;
            font-size: 1rem;
            color: var(--jomezpro-dark);
        }

        .task-count {
            font-size: 0.75rem;
            color: var(--jomezpro-light-gray);
            background: var(--jomezpro-white);
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
            border: 1px solid var(--jomezpro-border);
        }

        .tasks-list {
            padding: 1rem;
            min-height: 200px;
            max-height: 300px;
            overflow-y: auto;
        }

        .planning-task-item {
            padding: 0.75rem;
            border: 1px solid var(--jomezpro-border);
            border-radius: 0.375rem;
            margin-bottom: 0.5rem;
            cursor: move;
            transition: all 0.2s ease;
            background: var(--jomezpro-white);
        }

        .planning-task-item:hover {
            border-color: var(--jomezpro-teal);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .planning-task-item.suggested {
            border-style: dashed;
            opacity: 0.8;
            background: rgba(0, 196, 170, 0.05);
        }

        .planning-task-item.selected {
            border-color: var(--jomezpro-teal);
            background: rgba(0, 196, 170, 0.1);
        }

        .task-item-title {
            font-weight: 600;
            color: var(--jomezpro-dark);
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }

        .task-item-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: var(--jomezpro-light-gray);
        }

        .task-item-project {
            background: var(--jomezpro-teal);
            color: var(--jomezpro-white);
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            font-weight: 500;
        }

        .suggestions-filters {
            padding: 0.5rem 1rem;
            border-bottom: 1px solid var(--jomezpro-border);
        }

        .suggestions-filters select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--jomezpro-border);
            border-radius: 0.25rem;
            font-size: 0.75rem;
        }

        .quick-add-form {
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .quick-add-form input,
        .quick-add-form select {
            padding: 0.5rem;
            border: 1px solid var(--jomezpro-border);
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
        }

        .btn-toggle {
            background: none;
            border: 1px solid var(--jomezpro-teal);
            color: var(--jomezpro-teal);
            padding: 0.25rem 0.75rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-toggle:hover,
        .btn-toggle.active {
            background: var(--jomezpro-teal);
            color: var(--jomezpro-white);
        }

        .planning-insights {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        .insight-panel {
            background: var(--jomezpro-white);
            border: 1px solid var(--jomezpro-border);
            border-radius: 0.5rem;
            padding: 1rem;
        }

        .insight-panel h5 {
            margin: 0 0 1rem 0;
            color: var(--jomezpro-dark);
            font-size: 1rem;
        }

        .recommendations {
            font-size: 0.875rem;
            color: var(--jomezpro-dark);
            line-height: 1.5;
        }

        .week-context {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .context-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--jomezpro-border);
        }

        .context-item:last-child {
            border-bottom: none;
        }

        .context-label {
            font-size: 0.875rem;
            color: var(--jomezpro-light-gray);
        }

        .context-value {
            font-weight: 600;
            color: var(--jomezpro-teal);
        }

        .planning-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            padding-top: 1rem;
            border-top: 1px solid var(--jomezpro-border);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .planning-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .planning-insights {
                grid-template-columns: 1fr;
            }
            
            .day-summary {
                flex-direction: column;
                gap: 0.5rem;
                text-align: left;
            }
        }

        .task-title {
            font-weight: 600;
            color: var(--jomezpro-dark);
            margin-bottom: 0.25rem;
        }

        .task-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: var(--jomezpro-light-gray);
        }

        .task-project {
            background: var(--jomezpro-teal);
            color: var(--jomezpro-white);
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            font-weight: 500;
        }

        .day-add-btn {
            position: absolute;
            bottom: 1rem;
            right: 1rem;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--jomezpro-teal);
            color: var(--jomezpro-white);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 196, 170, 0.3);
        }

        .day-add-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 196, 170, 0.4);
        }

        /* Week Goals Panel */
        .week-goals {
            background: var(--jomezpro-white);
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .goals-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .goals-header h2 {
            color: var(--jomezpro-dark);
            margin: 0;
            font-size: 1.5rem;
        }

        .set-goal-btn {
            background: var(--jomezpro-success);
            color: var(--jomezpro-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .set-goal-btn:hover {
            background: #229954;
            transform: translateY(-1px);
        }

        .goal-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .goal-stat {
            text-align: center;
            padding: 1rem;
            background: var(--jomezpro-bg);
            border-radius: 0.5rem;
            border: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .goal-stat.locked {
            border-color: var(--jomezpro-success);
        }

        .goal-stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--jomezpro-teal);
            display: block;
        }

        .goal-stat-label {
            font-size: 0.875rem;
            color: var(--jomezpro-light-gray);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Drag and Drop */
        .drag-over {
            background: rgba(0, 196, 170, 0.1);
            border: 2px dashed var(--jomezpro-teal);
        }

        .dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        /* Goal Modal Styles */
        .goal-preview {
            background: var(--jomezpro-bg);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
            border: 1px solid var(--jomezpro-border);
        }

        .goal-preview h4 {
            margin: 0 0 1rem 0;
            color: var(--jomezpro-dark);
            font-size: 1rem;
        }

        .goal-stats-preview {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .stat-preview {
            text-align: center;
            padding: 0.75rem;
            background: var(--jomezpro-white);
            border-radius: 0.375rem;
            border: 1px solid var(--jomezpro-border);
        }

        .stat-preview .stat-label {
            display: block;
            font-size: 0.75rem;
            color: var(--jomezpro-light-gray);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.25rem;
        }

        .stat-preview .stat-number {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--jomezpro-teal);
        }

        .stat-preview .stat-number.negative {
            color: var(--jomezpro-error);
        }

        .stat-preview .stat-number.positive {
            color: var(--jomezpro-success);
        }

        .goal-recommendation {
            background: var(--jomezpro-white);
            border-radius: 0.375rem;
            padding: 0.75rem;
            border-left: 4px solid var(--jomezpro-info);
        }

        .goal-recommendation p {
            margin: 0;
            font-size: 0.875rem;
            color: var(--jomezpro-dark);
        }

        .goal-recommendation.warning {
            border-left-color: var(--jomezpro-warning);
        }

        .goal-recommendation.success {
            border-left-color: var(--jomezpro-success);
        }

        .goal-recommendation.error {
            border-left-color: var(--jomezpro-error);
        }

        /* Goal Status in Week Goals Panel */
        .goal-stat.active {
            border: 2px solid var(--jomezpro-teal);
            background: rgba(0, 196, 170, 0.05);
        }

        .goal-stat.achieved {
            border: 2px solid var(--jomezpro-success);
            background: rgba(39, 174, 96, 0.05);
        }

        .goal-stat.behind {
            border: 2px solid var(--jomezpro-warning);
            background: rgba(243, 156, 18, 0.05);
        }

        .goal-stat.critical {
            border: 2px solid var(--jomezpro-error);
            background: rgba(231, 76, 60, 0.05);
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .calendar-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .day-column {
                min-height: 200px;
            }
        }

        @media (max-width: 768px) {
            .weekly-header .container {
                flex-direction: column;
                gap: 1rem;
            }
            
            .week-navigation {
                width: 100%;
                justify-content: center;
            }

            .goal-stats-preview {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <h1><a href="index.html" style="text-decoration: none; color: inherit;">📋 Todo Management System</a></h1>
            <nav class="main-nav" role="navigation" aria-label="Main navigation">
                <ul>
                    <li><a href="today.html">Today</a></li>
                    <li><a href="weekly.html" aria-current="page">Weekly</a></li>
                    <li><a href="workflow.html">Workflows</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Weekly Header -->
    <div class="weekly-header">
        <div class="container">
            <h1>📅 Weekly Task Planning</h1>
            <div class="week-navigation">
                <button id="prev-week-btn" class="week-nav-btn">‹ Previous</button>
                <div id="week-display" class="week-display">Loading...</div>
                <button id="next-week-btn" class="week-nav-btn">Next ›</button>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loading-state" class="loading-state" aria-live="polite">
        <div class="loading-spinner"></div>
        <p>Loading weekly planner...</p>
    </div>

    <!-- Error State -->
    <div id="error-state" class="error-state hidden" role="alert" aria-live="assertive">
        <div class="error-icon">⚠️</div>
        <h2>Unable to Load Weekly Planner</h2>
        <p id="error-message">Please check if the API server is running.</p>
        <button id="retry-btn" class="btn btn-primary">Retry</button>
    </div>

    <!-- Weekly Content -->
    <div id="weekly-content" class="hidden">
        <main class="main-content">
            <div class="container">
                <!-- Week Goals Panel -->
                <div class="week-goals">
                    <div class="goals-header">
                        <h2>🎯 Week Goals</h2>
                        <button id="set-goal-btn" class="set-goal-btn">Set Weekly Goal</button>
                    </div>
                    <div class="goal-stats">
                        <div class="goal-stat">
                            <span id="planned-hours" class="goal-stat-number">0h</span>
                            <span class="goal-stat-label">Planned Hours</span>
                        </div>
                        <div class="goal-stat">
                            <span id="completed-hours" class="goal-stat-number">0h</span>
                            <span class="goal-stat-label">Completed Hours</span>
                        </div>
                        <div class="goal-stat">
                            <span id="goal-progress" class="goal-stat-number">0%</span>
                            <span class="goal-stat-label">Progress</span>
                        </div>
                        <div class="goal-stat">
                            <span id="tasks-count" class="goal-stat-number">0</span>
                            <span class="goal-stat-label">Total Tasks</span>
                        </div>
                    </div>
                </div>

                <!-- Weekly Calendar -->
                <div class="weekly-calendar">
                    <div class="month-header" id="month-header">
                        <!-- Month name(s) will be generated dynamically -->
                    </div>
                    <div class="calendar-grid" id="calendar-grid">
                        <!-- Days will be generated dynamically -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Daily Planning Modal -->
    <div id="daily-planning-modal" class="modal hidden" role="dialog" aria-labelledby="daily-planning-title" aria-hidden="true">
        <div class="modal-content modal-large">
            <header class="modal-header">
                <h2 id="daily-planning-title">📅 Daily Planning</h2>
                <button class="modal-close" aria-label="Close dialog">&times;</button>
            </header>
            <div class="modal-body">
                <div class="daily-planning-container">
                    <!-- Day Header -->
                    <div class="planning-day-header">
                        <div class="day-info">
                            <h3 id="planning-day-name">Monday</h3>
                            <div id="planning-day-date">January 1, 2025</div>
                        </div>
                        <div class="day-summary">
                            <div class="summary-stat">
                                <span id="planning-current-hours" class="stat-number">0h</span>
                                <span class="stat-label">Currently Planned</span>
                            </div>
                            <div class="summary-stat">
                                <span id="planning-target-hours" class="stat-number">6h</span>
                                <span class="stat-label">Target Hours</span>
                            </div>
                            <div class="summary-stat">
                                <span id="planning-status" class="stat-number">Need +6h</span>
                                <span class="stat-label">Status</span>
                            </div>
                        </div>
                    </div>

                    <!-- Planning Grid -->
                    <div class="planning-grid">
                        <!-- Current Tasks Column -->
                        <div class="planning-column">
                            <div class="column-header">
                                <h4>📋 Current Tasks</h4>
                                <span id="current-tasks-count" class="task-count">0 tasks</span>
                            </div>
                            <div id="current-tasks-list" class="tasks-list">
                                <!-- Current tasks will be populated here -->
                            </div>
                        </div>

                        <!-- Available Tasks Column -->
                        <div class="planning-column">
                            <div class="column-header">
                                <h4>💡 Suggested Tasks</h4>
                                <span id="suggested-tasks-count" class="task-count">0 suggestions</span>
                            </div>
                            <div class="suggestions-filters">
                                <select id="suggestion-filter">
                                    <option value="all">All Suggestions</option>
                                    <option value="high">High Priority Only</option>
                                    <option value="perfect-fit">Perfect Time Fit</option>
                                    <option value="urgent">Due Soon</option>
                                </select>
                            </div>
                            <div id="suggested-tasks-list" class="tasks-list">
                                <!-- Suggested tasks will be populated here -->
                            </div>
                        </div>

                        <!-- Quick Add Column -->
                        <div class="planning-column">
                            <div class="column-header">
                                <h4>⚡ Quick Add Task</h4>
                                <button id="quick-add-toggle" class="btn-toggle">Show</button>
                            </div>
                            <div id="quick-add-form" class="quick-add-form" style="display: none;">
                                <input type="text" id="quick-task-title" placeholder="Task title..." maxlength="100">
                                <select id="quick-task-project">
                                    <option value="">Select project...</option>
                                    <!-- Projects populated dynamically -->
                                </select>
                                <div class="form-row">
                                    <select id="quick-task-priority">
                                        <option value="medium">Medium Priority</option>
                                        <option value="high">High Priority</option>
                                        <option value="critical">Critical Priority</option>
                                        <option value="low">Low Priority</option>
                                    </select>
                                    <input type="number" id="quick-task-hours" placeholder="Hours" min="0.5" max="24" step="0.5">
                                </div>
                                <button id="quick-add-btn" class="btn btn-primary btn-small">Add & Schedule</button>
                            </div>
                        </div>
                    </div>

                    <!-- Planning Insights -->
                    <div class="planning-insights">
                        <div class="insight-panel">
                            <h5>🎯 Planning Insights</h5>
                            <div id="planning-recommendations" class="recommendations">
                                <p>Select a day to see personalized planning insights...</p>
                            </div>
                        </div>
                        <div class="insight-panel">
                            <h5>📊 Week Context</h5>
                            <div id="week-context" class="week-context">
                                <div class="context-item">
                                    <span class="context-label">Week Total:</span>
                                    <span id="week-total-hours" class="context-value">0h</span>
                                </div>
                                <div class="context-item">
                                    <span class="context-label">Avg/Day:</span>
                                    <span id="week-avg-hours" class="context-value">0h</span>
                                </div>
                                <div class="context-item">
                                    <span class="context-label">Goal Progress:</span>
                                    <span id="week-goal-progress" class="context-value">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="planning-actions">
                        <button id="auto-fill-btn" class="btn btn-info">🤖 Auto-Fill to 6h</button>
                        <button id="save-planning-btn" class="btn btn-primary">💾 Save Daily Plan</button>
                        <button id="cancel-planning-btn" class="btn btn-secondary">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Set Weekly Goal Modal -->
    <div id="goal-modal" class="modal hidden" role="dialog" aria-labelledby="goal-modal-title" aria-hidden="true">
        <div class="modal-content">
            <header class="modal-header">
                <h2 id="goal-modal-title">🎯 Set Weekly Goal</h2>
                <button class="modal-close" aria-label="Close dialog">&times;</button>
            </header>
            <div class="modal-body">
                <form id="goal-form">
                    <div class="form-field">
                        <label for="goal-target-hours">Target Hours for This Week</label>
                        <input type="number" id="goal-target-hours" min="1" max="168" step="0.5" placeholder="e.g., 40" required>
                        <small>How many hours do you want to work this week? (1-168 hours)</small>
                    </div>
                    
                    <div class="form-field">
                        <label for="goal-description">Goal Description (Optional)</label>
                        <textarea id="goal-description" placeholder="Describe what you want to accomplish this week..." rows="3"></textarea>
                        <small>Add context about your weekly objectives</small>
                    </div>
                    
                    <div class="form-field">
                        <label for="goal-priority">Priority Level</label>
                        <select id="goal-priority">
                            <option value="high">🔴 High - Must achieve this week</option>
                            <option value="medium" selected>🟡 Medium - Important but flexible</option>
                            <option value="low">🟢 Low - Nice to have</option>
                        </select>
                        <small>How critical is it to achieve this goal?</small>
                    </div>
                    
                    <div class="goal-preview">
                        <h4>📊 Week Overview</h4>
                        <div class="goal-stats-preview">
                            <div class="stat-preview">
                                <span class="stat-label">Current Planned</span>
                                <span id="preview-planned" class="stat-number">0h</span>
                            </div>
                            <div class="stat-preview">
                                <span class="stat-label">Goal Target</span>
                                <span id="preview-target" class="stat-number">0h</span>
                            </div>
                            <div class="stat-preview">
                                <span class="stat-label">Difference</span>
                                <span id="preview-difference" class="stat-number">0h</span>
                            </div>
                        </div>
                        <div class="goal-recommendation">
                            <p id="goal-recommendation-text">Set a target above to see recommendations</p>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="cancel-goal-btn">Cancel</button>
                        <button type="submit" class="btn btn-primary">🎯 Set Goal</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2025 Todo Management System | <a href="index.html">Dashboard</a> | <a href="today.html">Today</a> | <a href="weekly.html">Weekly</a></p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script>
        // Weekly Planner Implementation
        class WeeklyPlanner {
            constructor() {
                this.api = window.todoAPI;
                this.currentWeekStart = this.getWeekStart(new Date());
                this.todos = [];
                this.weeklyGoal = null;
                this.draggedTask = null;

                // DOM elements
                this.elements = {
                    loadingState: document.getElementById('loading-state'),
                    errorState: document.getElementById('error-state'),
                    weeklyContent: document.getElementById('weekly-content'),
                    weekDisplay: document.getElementById('week-display'),
                    prevWeekBtn: document.getElementById('prev-week-btn'),
                    nextWeekBtn: document.getElementById('next-week-btn'),
                    monthHeader: document.getElementById('month-header'),
                    calendarGrid: document.getElementById('calendar-grid'),
                    setGoalBtn: document.getElementById('set-goal-btn'),
                    plannedHours: document.getElementById('planned-hours'),
                    completedHours: document.getElementById('completed-hours'),
                    goalProgress: document.getElementById('goal-progress'),
                    tasksCount: document.getElementById('tasks-count'),
                    retryBtn: document.getElementById('retry-btn'),
                    errorMessage: document.getElementById('error-message'),
                    // Goal modal elements
                    goalModal: document.getElementById('goal-modal'),
                    goalForm: document.getElementById('goal-form'),
                    goalTargetHours: document.getElementById('goal-target-hours'),
                    goalDescription: document.getElementById('goal-description'),
                    goalPriority: document.getElementById('goal-priority'),
                    cancelGoalBtn: document.getElementById('cancel-goal-btn'),
                    previewPlanned: document.getElementById('preview-planned'),
                    previewTarget: document.getElementById('preview-target'),
                    previewDifference: document.getElementById('preview-difference'),
                    goalRecommendationText: document.getElementById('goal-recommendation-text'),
                    // Daily planning modal elements
                    dailyPlanningModal: document.getElementById('daily-planning-modal')
                };

                this.init();
            }

            async init() {
                try {
                    this.setupEventListeners();
                    await this.loadWeeklyData();
                    this.renderWeeklyCalendar();
                    this.showWeeklyContent();
                } catch (error) {
                    console.error('Error initializing weekly planner:', error);
                    this.showError(error.message);
                }
            }

            setupEventListeners() {
                // Week navigation
                this.elements.prevWeekBtn.addEventListener('click', () => {
                    this.navigateWeek(-1);
                });

                this.elements.nextWeekBtn.addEventListener('click', () => {
                    this.navigateWeek(1);
                });

                // Retry button
                this.elements.retryBtn.addEventListener('click', () => {
                    location.reload();
                });

                // Set goal button
                this.elements.setGoalBtn.addEventListener('click', () => {
                    this.showSetGoalDialog();
                });

                // Goal modal event listeners
                this.elements.goalForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.saveWeeklyGoal();
                });

                this.elements.cancelGoalBtn.addEventListener('click', () => {
                    this.hideGoalModal();
                });

                // Goal target input changes
                this.elements.goalTargetHours.addEventListener('input', () => {
                    this.updateGoalPreview();
                });

                // Modal close buttons
                document.querySelectorAll('.modal-close').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const modal = e.target.closest('.modal');
                        if (modal) {
                            modal.classList.add('hidden');
                            modal.setAttribute('aria-hidden', 'true');
                        }
                    });
                });

                // Daily planning dialog close
                this.elements.dailyPlanningModal.addEventListener('click', (e) => {
                    if (e.target === this.elements.dailyPlanningModal) {
                        this.hideDailyPlanningModal();
                    }
                });

                // Setup quick add form
                setTimeout(() => this.setupQuickAdd(), 100);
            }

            getWeekStart(date) {
                const d = new Date(date);
                const day = d.getDay();
                const diff = d.getDate() - day; // Sunday = 0
                return new Date(d.setDate(diff));
            }

            async navigateWeek(direction) {
                const newWeek = new Date(this.currentWeekStart);
                newWeek.setDate(newWeek.getDate() + (direction * 7));
                this.currentWeekStart = newWeek;
                
                await this.loadWeeklyData();
                this.renderWeeklyCalendar();
                this.updateGoalStats();
            }

            async loadWeeklyData() {
                try {
                    // Load all projects and their todos
                    const projects = await this.api.getProjects();
                    const allTodos = [];

                    for (const project of projects) {
                        try {
                            const projectTodos = await this.api.getTodos(project.name);
                            const todosWithProject = projectTodos.todos.map(todo => ({
                                ...todo,
                                projectName: project.name,
                                projectDisplayName: TodoUtils.formatters.getProjectDisplay(project)
                            }));
                            allTodos.push(...todosWithProject);
                        } catch (error) {
                            console.warn(`Failed to load todos for project ${project.name}`);
                        }
                    }

                    this.todos = allTodos;
                    this.updateWeekDisplay();
                } catch (error) {
                    throw new Error(`Failed to load weekly data: ${error.message}`);
                }
            }

            updateWeekDisplay() {
                const weekEnd = new Date(this.currentWeekStart);
                weekEnd.setDate(weekEnd.getDate() + 6);
                
                const formatDate = (date) => {
                    return date.toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric',
                        year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
                    });
                };

                this.elements.weekDisplay.textContent = 
                    `${formatDate(this.currentWeekStart)} - ${formatDate(weekEnd)}`;
            }

            updateMonthHeader() {
                const weekStart = new Date(this.currentWeekStart);
                const weekEnd = new Date(this.currentWeekStart);
                weekEnd.setDate(weekEnd.getDate() + 6);
                
                const startMonth = weekStart.toLocaleDateString('en-US', { month: 'long' });
                const endMonth = weekEnd.toLocaleDateString('en-US', { month: 'long' });
                const startYear = weekStart.getFullYear();
                const endYear = weekEnd.getFullYear();
                
                let monthDisplay = '';
                
                if (startYear !== endYear) {
                    // Week crosses year boundary: "December 2024 / January 2025"
                    monthDisplay = `${startMonth} ${startYear}<span class="month-separator">/</span>${endMonth} ${endYear}`;
                } else if (startMonth !== endMonth) {
                    // Week crosses month boundary within same year: "August / September"
                    monthDisplay = `${startMonth}<span class="month-separator">/</span>${endMonth}`;
                    // Add year if not current year
                    if (startYear !== new Date().getFullYear()) {
                        monthDisplay += ` ${startYear}`;
                    }
                } else {
                    // Week is within same month: "August" or "August 2024"
                    monthDisplay = startMonth;
                    if (startYear !== new Date().getFullYear()) {
                        monthDisplay += ` ${startYear}`;
                    }
                }
                
                this.elements.monthHeader.innerHTML = `<div class="month-transition">${monthDisplay}</div>`;
            }

            renderWeeklyCalendar() {
                const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                
                // Update month header
                this.updateMonthHeader();
                
                // Clear and populate calendar grid
                this.elements.calendarGrid.innerHTML = '';

                for (let i = 0; i < 7; i++) {
                    const dayDate = new Date(this.currentWeekStart);
                    dayDate.setDate(dayDate.getDate() + i);
                    
                    const dayColumn = this.createDayColumn(days[i], dayDate, i === 0 || i === 6);
                    this.elements.calendarGrid.appendChild(dayColumn);
                }

                this.updateGoalStats();
            }

            createDayColumn(dayName, date, isWeekend) {
                const dayColumn = document.createElement('div');
                const today = new Date();
                const isToday = date.toDateString() === today.toDateString();
                
                dayColumn.className = `day-column ${isWeekend ? 'weekend' : ''} ${isToday ? 'today' : ''}`;
                dayColumn.dataset.date = date.toISOString().split('T')[0];

                // Get tasks for this day
                const dayTasks = this.getTasksForDay(date);
                const plannedHours = dayTasks.reduce((sum, task) => sum + (task.estimated_hours || 0), 0);
                const completedHours = dayTasks
                    .filter(task => task.status === 'completed')
                    .reduce((sum, task) => sum + (task.estimated_hours || 0), 0);

                // Generate suggested tasks if needed
                const suggestedTasks = this.generateSuggestedTasks(dayTasks, plannedHours, date);
                
                // Create hours needed text only for weekdays
                const hoursNeededText = !isWeekend && suggestedTasks.length > 0 && plannedHours < 6 
                    ? `(${6 - plannedHours}h needed to reach 6h)` 
                    : '';
                
                dayColumn.innerHTML = `
                    <div class="day-header">
                        <div class="day-name">${dayName}</div>
                        <div class="day-date">${date.getDate()}</div>
                    </div>
                    <div class="day-content">
                        <div class="day-stats">
                            <span class="day-hours">${plannedHours}h planned</span>
                            <span class="day-completed">${completedHours}h done</span>
                        </div>
                        <div class="day-tasks" id="tasks-${date.toISOString().split('T')[0]}">
                            ${dayTasks.map(task => this.createTaskItem(task)).join('')}
                            ${suggestedTasks.length > 0 && plannedHours < 6 ? `
                                <div class="day-suggestions" data-expanded="false">
                                    <div class="suggestions-header">
                                        💡 Suggested Tasks 
                                        <button class="suggestions-toggle" onclick="this.parentElement.parentElement.dataset.expanded = this.parentElement.parentElement.dataset.expanded === 'false' ? 'true' : 'false'; this.textContent = this.parentElement.parentElement.dataset.expanded === 'true' ? 'Hide' : 'Show'">Show</button>
                                        ${hoursNeededText}
                                    </div>
                                    <div class="suggestions-list" style="display: none;">
                                        ${suggestedTasks.slice(0, 3).map(task => this.createTaskItem(task, true)).join('')}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <button class="day-add-btn" title="Add task to ${dayName}">+</button>
                    </div>
                `;

                // Setup drag and drop
                this.setupDayDropZone(dayColumn);

                // Setup day add button
                const addBtn = dayColumn.querySelector('.day-add-btn');
                addBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.showDailyPlanningDialog(date);
                });

                return dayColumn;
            }

            getTasksForDay(date) {
                const dateStr = date.toISOString().split('T')[0];
                return this.todos.filter(todo => {
                    // Use start_date if available, otherwise fall back to due_date
                    const taskDate = todo.start_date || todo.due_date;
                    if (!taskDate) return false;
                    const todoDate = new Date(taskDate).toISOString().split('T')[0];
                    return todoDate === dateStr && todo.status !== 'deleted';
                });
            }

            createTaskItem(task, isSuggested = false) {
                const priorityClass = task.priority ? `${task.priority}-priority` : 'medium-priority';
                const suggestedClass = isSuggested ? 'suggested' : '';
                return `
                    <div class="task-item ${priorityClass} ${suggestedClass}" 
                         draggable="true" 
                         data-task-id="${task.id}"
                         data-project="${task.projectName}"
                         data-suggested="${isSuggested}"
                         title="${isSuggested ? 'Click to schedule this suggested task' : ''}">
                        <div class="task-title">${task.title}${isSuggested ? ' (click to schedule)' : ''}</div>
                        <div class="task-meta">
                            <span class="task-project">${TodoUtils.formatters.todoDisplayName(task)}</span>
                            <span>${task.estimated_hours || 0}h</span>
                        </div>
                    </div>
                `;
            }

            setupDayDropZone(dayColumn) {
                const tasksContainer = dayColumn.querySelector('.day-tasks');

                // Click events for suggested tasks
                tasksContainer.addEventListener('click', (e) => {
                    const taskItem = e.target.closest('.task-item');
                    if (taskItem && taskItem.dataset.suggested === 'true') {
                        e.stopPropagation();
                        const taskId = taskItem.dataset.taskId;
                        const project = taskItem.dataset.project;
                        const targetDate = dayColumn.dataset.date;
                        
                        // Move suggested task to this day
                        this.moveTaskToDay({
                            id: taskId,
                            project: project,
                            element: taskItem
                        }, targetDate);
                    }
                });

                // Drag events for tasks
                tasksContainer.addEventListener('dragstart', (e) => {
                    if (e.target.classList.contains('task-item')) {
                        this.draggedTask = {
                            id: e.target.dataset.taskId,
                            project: e.target.dataset.project,
                            element: e.target
                        };
                        e.target.classList.add('dragging');
                    }
                });

                tasksContainer.addEventListener('dragend', (e) => {
                    if (e.target.classList.contains('task-item')) {
                        e.target.classList.remove('dragging');
                        this.draggedTask = null;
                    }
                });

                // Drop events for day columns
                dayColumn.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    dayColumn.classList.add('drag-over');
                });

                dayColumn.addEventListener('dragleave', (e) => {
                    if (!dayColumn.contains(e.relatedTarget)) {
                        dayColumn.classList.remove('drag-over');
                    }
                });

                dayColumn.addEventListener('drop', (e) => {
                    e.preventDefault();
                    dayColumn.classList.remove('drag-over');
                    
                    if (this.draggedTask) {
                        const newDate = dayColumn.dataset.date;
                        this.moveTaskToDay(this.draggedTask, newDate);
                    }
                });
            }

            async moveTaskToDay(draggedTask, newDate) {
                try {
                    const taskElement = draggedTask.element;
                    const isSuggested = taskElement.dataset.suggested === 'true';
                    
                    // Update the task's start_date for better weekly planning
                    await this.api.patchTodo(draggedTask.project, draggedTask.id, {
                        start_date: newDate
                    });

                    // Refresh the calendar
                    await this.loadWeeklyData();
                    this.renderWeeklyCalendar();
                    
                    if (isSuggested) {
                        TodoUtils.toast.success('✨ Suggested task scheduled successfully!');
                    } else {
                        TodoUtils.toast.success('Task moved successfully');
                    }
                } catch (error) {
                    console.error('Error moving task:', error);
                    TodoUtils.toast.error('Failed to move task');
                }
            }

            updateGoalStats() {
                const weekTasks = this.getWeekTasks();
                const plannedHours = weekTasks.reduce((sum, task) => sum + (task.estimated_hours || 0), 0);
                const completedHours = weekTasks
                    .filter(task => task.status === 'completed')
                    .reduce((sum, task) => sum + (task.estimated_hours || 0), 0);
                
                // Load goal for this week
                const weekKey = this.getWeekKey(this.currentWeekStart);
                const weeklyGoal = TodoUtils.storage.get(`weeklyGoal_${weekKey}`);
                
                let progress;
                if (weeklyGoal && weeklyGoal.targetHours > 0) {
                    // Calculate progress against goal
                    progress = Math.round((completedHours / weeklyGoal.targetHours) * 100);
                } else {
                    // Calculate progress against planned hours
                    progress = plannedHours > 0 ? Math.round((completedHours / plannedHours) * 100) : 0;
                }

                this.elements.plannedHours.textContent = `${plannedHours}h`;
                this.elements.completedHours.textContent = `${completedHours}h`;
                this.elements.goalProgress.textContent = `${progress}%`;
                this.elements.tasksCount.textContent = weekTasks.length;

                // Update goal stat styling based on progress
                this.updateGoalStatStyling(plannedHours, completedHours, weeklyGoal);
            }

            updateGoalStatStyling(plannedHours, completedHours, weeklyGoal) {
                // Get all goal stat elements
                const goalStats = document.querySelectorAll('.goal-stat');
                
                // Remove all status classes
                goalStats.forEach(stat => {
                    stat.classList.remove('active', 'achieved', 'behind', 'critical');
                });

                if (weeklyGoal && weeklyGoal.targetHours > 0) {
                    const targetHours = weeklyGoal.targetHours;
                    const progressRatio = completedHours / targetHours;
                    
                    if (progressRatio >= 1) {
                        // Goal achieved!
                        goalStats.forEach(stat => stat.classList.add('achieved'));
                    } else if (progressRatio >= 0.8) {
                        // On track
                        goalStats.forEach(stat => stat.classList.add('active'));
                    } else if (progressRatio >= 0.5) {
                        // Behind but recoverable
                        goalStats.forEach(stat => stat.classList.add('behind'));
                    } else {
                        // Critically behind
                        goalStats.forEach(stat => stat.classList.add('critical'));
                    }
                } else {
                    // No goal set, just mark as active
                    goalStats.forEach(stat => stat.classList.add('active'));
                }
            }

            getWeekTasks() {
                const weekEnd = new Date(this.currentWeekStart);
                weekEnd.setDate(weekEnd.getDate() + 6);
                
                return this.todos.filter(todo => {
                    const taskDate = todo.start_date || todo.due_date;
                    if (!taskDate || todo.status === 'deleted') return false;
                    const todoDate = new Date(taskDate);
                    return todoDate >= this.currentWeekStart && todoDate <= weekEnd;
                });
            }

            showSetGoalDialog() {
                // Load existing goal for this week if it exists
                this.loadExistingGoal();
                
                // Update preview with current planned hours
                this.updateGoalPreview();
                
                // Show modal
                this.elements.goalModal.classList.remove('hidden');
                this.elements.goalModal.setAttribute('aria-hidden', 'false');
                
                // Focus first input
                this.elements.goalTargetHours.focus();
            }

            hideGoalModal() {
                this.elements.goalModal.classList.add('hidden');
                this.elements.goalModal.setAttribute('aria-hidden', 'true');
                this.resetGoalForm();
            }

            loadExistingGoal() {
                const weekKey = this.getWeekKey(this.currentWeekStart);
                const existingGoal = TodoUtils.storage.get(`weeklyGoal_${weekKey}`);
                
                if (existingGoal) {
                    this.elements.goalTargetHours.value = existingGoal.targetHours;
                    this.elements.goalDescription.value = existingGoal.description || '';
                    this.elements.goalPriority.value = existingGoal.priority || 'medium';
                    this.weeklyGoal = existingGoal;
                } else {
                    this.resetGoalForm();
                }
            }

            resetGoalForm() {
                this.elements.goalTargetHours.value = '';
                this.elements.goalDescription.value = '';
                this.elements.goalPriority.value = 'medium';
                this.updateGoalPreview();
            }

            updateGoalPreview() {
                const weekTasks = this.getWeekTasks();
                const plannedHours = weekTasks.reduce((sum, task) => sum + (task.estimated_hours || 0), 0);
                const targetHours = parseFloat(this.elements.goalTargetHours.value) || 0;
                const difference = targetHours - plannedHours;

                this.elements.previewPlanned.textContent = `${plannedHours}h`;
                this.elements.previewTarget.textContent = `${targetHours}h`;
                
                // Update difference with styling
                const diffElement = this.elements.previewDifference;
                diffElement.textContent = `${difference > 0 ? '+' : ''}${difference}h`;
                diffElement.className = 'stat-number';
                if (difference > 0) {
                    diffElement.classList.add('negative');
                } else if (difference < 0) {
                    diffElement.classList.add('positive');
                }

                // Update recommendation
                this.updateGoalRecommendation(plannedHours, targetHours, difference);
            }

            updateGoalRecommendation(plannedHours, targetHours, difference) {
                const recommendation = this.elements.goalRecommendationText.parentElement;
                recommendation.className = 'goal-recommendation';
                
                if (targetHours === 0) {
                    this.elements.goalRecommendationText.textContent = 'Set a target above to see recommendations';
                    return;
                }

                if (Math.abs(difference) < 1) {
                    recommendation.classList.add('success');
                    this.elements.goalRecommendationText.textContent = 
                        `✅ Perfect! Your planned hours (${plannedHours}h) align well with your target.`;
                } else if (difference > 0) {
                    if (difference <= 8) {
                        recommendation.classList.add('warning');
                        this.elements.goalRecommendationText.textContent = 
                            `⚠️ You need ${difference}h more tasks to reach your goal. Consider adding tasks or adjusting your target.`;
                    } else {
                        recommendation.classList.add('error');
                        this.elements.goalRecommendationText.textContent = 
                            `🚨 Your target is ${difference}h higher than planned. This might be too ambitious for this week.`;
                    }
                } else {
                    const overtime = Math.abs(difference);
                    if (overtime <= 4) {
                        recommendation.classList.add('warning');
                        this.elements.goalRecommendationText.textContent = 
                            `💪 You have ${overtime}h more planned than your target. Great productivity!`;
                    } else {
                        recommendation.classList.add('error');
                        this.elements.goalRecommendationText.textContent = 
                            `🔥 You're planning ${overtime}h more than your target. Consider spreading tasks across weeks.`;
                    }
                }
            }

            saveWeeklyGoal() {
                const targetHours = parseFloat(this.elements.goalTargetHours.value);
                const description = this.elements.goalDescription.value.trim();
                const priority = this.elements.goalPriority.value;

                if (!targetHours || targetHours <= 0) {
                    TodoUtils.toast.error('Please enter a valid target hours amount');
                    this.elements.goalTargetHours.focus();
                    return;
                }

                const weekKey = this.getWeekKey(this.currentWeekStart);
                const goal = {
                    targetHours,
                    description,
                    priority,
                    weekStart: this.currentWeekStart.toISOString(),
                    createdDate: new Date().toISOString()
                };

                // Save to localStorage
                TodoUtils.storage.set(`weeklyGoal_${weekKey}`, goal);
                this.weeklyGoal = goal;

                // Update the UI
                this.updateGoalStats();
                this.hideGoalModal();
                
                TodoUtils.toast.success(`Weekly goal set: ${targetHours}h target`);
            }

            getWeekKey(weekStart) {
                return weekStart.toISOString().split('T')[0]; // YYYY-MM-DD format
            }

            generateSuggestedTasks(dayTasks, plannedHours, date) {
                const now = new Date();
                const today = new Date();
                today.setHours(0, 0, 0, 0); // Start of today
                
                const targetDate = new Date(date);
                targetDate.setHours(0, 0, 0, 0); // Start of target day
                
                // Don't suggest for past days
                if (targetDate < today) return [];
                
                // For today, check if it's past 5 PM (17:00)
                const isToday = targetDate.getTime() === today.getTime();
                if (isToday && now.getHours() >= 17) {
                    // Calculate remaining hours until end of day (assuming 8 PM cutoff)
                    const remainingHours = Math.max(0, 20 - now.getHours());
                    if (remainingHours <= 1) return []; // Not enough time left
                }
                
                // Don't suggest if already over 6 hours (or remaining hours for today)
                const maxHours = isToday && now.getHours() >= 17 
                    ? Math.max(0, 20 - now.getHours()) 
                    : 6;
                    
                if (plannedHours >= maxHours) return [];

                const hoursNeeded = maxHours - plannedHours;
                const dayOfWeek = date.getDay(); // 0 = Sunday, 6 = Saturday
                
                // Get all available tasks (not already scheduled and not completed)
                const availableTasks = this.todos.filter(todo => {
                    // Skip if already scheduled for this week
                    const weekStart = this.currentWeekStart;
                    const weekEnd = new Date(weekStart);
                    weekEnd.setDate(weekEnd.getDate() + 6);
                    
                    const taskDate = todo.start_date || todo.due_date;
                    const isScheduledThisWeek = taskDate && 
                        new Date(taskDate) >= weekStart && 
                        new Date(taskDate) <= weekEnd;
                    
                    return todo.status === 'pending' && 
                           !isScheduledThisWeek &&
                           todo.estimated_hours > 0;
                });

                if (availableTasks.length === 0) return [];

                // Smart scoring algorithm (alpha 30 level!)
                const scoredTasks = availableTasks.map(task => {
                    let score = 0;
                    const estimatedHours = task.estimated_hours || 1;
                    
                    // 1. Priority scoring (40% of score)
                    const priorityScores = { critical: 40, high: 30, medium: 20, low: 10 };
                    score += priorityScores[task.priority] || 20;
                    
                    // 2. Hour fit scoring (30% of score)
                    if (estimatedHours <= hoursNeeded) {
                        score += 30; // Perfect fit
                    } else if (estimatedHours <= hoursNeeded + 2) {
                        score += 20; // Close fit
                    } else {
                        score += 5; // Poor fit
                    }
                    
                    // 3. Day context scoring (20% of score)
                    if (dayOfWeek === 0 || dayOfWeek === 6) {
                        // Weekend - prefer personal/maintenance tasks
                        if (task.category && ['personal', 'maintenance', 'learning'].includes(task.category.toLowerCase())) {
                            score += 20;
                        }
                    } else {
                        // Weekday - prefer work tasks
                        if (task.category && ['work', 'development', 'meeting'].includes(task.category.toLowerCase())) {
                            score += 20;
                        }
                    }
                    
                    // 4. Due date urgency (10% of score)
                    if (task.due_date) {
                        const dueDate = new Date(task.due_date);
                        const today = new Date();
                        const daysUntilDue = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
                        
                        if (daysUntilDue <= 3) {
                            score += 10; // Urgent
                        } else if (daysUntilDue <= 7) {
                            score += 5; // Somewhat urgent
                        }
                    }
                    
                    // 5. Project variety bonus
                    const dayProjects = dayTasks.map(t => t.projectName);
                    if (!dayProjects.includes(task.projectName)) {
                        score += 5; // Encourage project variety
                    }
                    
                    return { ...task, suggestionScore: score };
                });

                // Sort by score and return top suggestions
                return scoredTasks
                    .sort((a, b) => b.suggestionScore - a.suggestionScore)
                    .slice(0, 5); // Top 5 suggestions
            }

            showLoadingState() {
                TodoUtils.dom.toggle(this.elements.loadingState, true);
                TodoUtils.dom.toggle(this.elements.errorState, false);
                TodoUtils.dom.toggle(this.elements.weeklyContent, false);
            }

            showError(message) {
                this.elements.errorMessage.textContent = message;
                TodoUtils.dom.toggle(this.elements.loadingState, false);
                TodoUtils.dom.toggle(this.elements.errorState, true);
                TodoUtils.dom.toggle(this.elements.weeklyContent, false);
            }

            showWeeklyContent() {
                TodoUtils.dom.toggle(this.elements.loadingState, false);
                TodoUtils.dom.toggle(this.elements.errorState, false);
                TodoUtils.dom.toggle(this.elements.weeklyContent, true);
            }

            // Daily Planning Dialog Methods
            showDailyPlanningDialog(date) {
                this.currentPlanningDate = date;
                const formattedDate = date.toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
                
                // Update dialog header
                document.getElementById('daily-planning-title').textContent = `Plan Your ${formattedDate.split(',')[0]}`;
                document.getElementById('planning-date-display').textContent = formattedDate;
                
                // Load and populate dialog content
                this.loadDailyPlanningData(date);
                
                // Show modal
                this.elements.dailyPlanningModal.classList.remove('hidden');
                this.elements.dailyPlanningModal.setAttribute('aria-hidden', 'false');
                
                // Focus first actionable element
                const firstInput = document.getElementById('quick-add-title');
                if (firstInput) firstInput.focus();
            }

            hideDailyPlanningModal() {
                this.elements.dailyPlanningModal.classList.add('hidden');
                this.elements.dailyPlanningModal.setAttribute('aria-hidden', 'true');
                this.currentPlanningDate = null;
            }

            loadDailyPlanningData(date) {
                // Load current tasks for the day
                const currentTasks = this.getTasksForDay(date);
                const plannedHours = currentTasks.reduce((sum, task) => sum + (task.estimated_hours || 0), 0);
                
                // Generate suggested tasks
                const suggestedTasks = this.generateSuggestedTasks(currentTasks, plannedHours, date);
                
                // Update current tasks section
                this.renderCurrentTasks(currentTasks);
                
                // Update suggested tasks section
                this.renderSuggestedTasks(suggestedTasks, plannedHours);
                
                // Update planning insights
                this.updatePlanningInsights(currentTasks, suggestedTasks, plannedHours, date);
                
                // Update week context
                this.updateWeekContext(date);
            }

            renderCurrentTasks(tasks) {
                const container = document.getElementById('current-tasks-list');
                
                if (tasks.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state-mini">
                            <p>📋 No tasks scheduled</p>
                            <small>Add tasks below or drag from suggestions</small>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = tasks.map(task => `
                    <div class="current-task-item ${task.priority}-priority" data-task-id="${task.id}" data-project="${task.projectName}">
                        <div class="task-content">
                            <div class="task-title-row">
                                <h4 class="task-title">${task.title}</h4>
                                <span class="task-hours">${task.estimated_hours || 0}h</span>
                            </div>
                            <div class="task-meta-row">
                                <span class="task-project">${TodoUtils.formatters.todoDisplayName(task)}</span>
                                <span class="task-priority ${task.priority}-priority">${TodoUtils.formatters.priorityLabel(task.priority || 'medium')}</span>
                            </div>
                            ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
                        </div>
                        <div class="task-actions">
                            <button class="btn-icon edit-task-btn" title="Edit task">✏️</button>
                            <button class="btn-icon remove-task-btn" title="Remove from day">❌</button>
                        </div>
                    </div>
                `).join('');
                
                // Add event listeners for task actions
                this.setupCurrentTaskActions();
            }

            renderSuggestedTasks(tasks, currentPlannedHours) {
                const container = document.getElementById('suggested-tasks-list');
                const hoursNeeded = Math.max(0, 6 - currentPlannedHours);
                
                if (tasks.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state-mini">
                            <p>🎯 No suggestions available</p>
                            <small>All tasks are scheduled or completed</small>
                        </div>
                    `;
                    return;
                }

                // Filter tasks that could fit in the remaining time
                const fittingTasks = tasks.filter(task => (task.estimated_hours || 0) <= hoursNeeded + 2);
                const displayTasks = fittingTasks.length > 0 ? fittingTasks : tasks.slice(0, 3);

                container.innerHTML = displayTasks.map(task => `
                    <div class="suggested-task-item ${task.priority}-priority" 
                         data-task-id="${task.id}" 
                         data-project="${task.projectName}"
                         draggable="true">
                        <div class="task-content">
                            <div class="task-title-row">
                                <h4 class="task-title">${task.title}</h4>
                                <span class="task-hours">${task.estimated_hours || 0}h</span>
                                <span class="suggestion-score">Score: ${Math.round(task.suggestionScore)}</span>
                            </div>
                            <div class="task-meta-row">
                                <span class="task-project">${TodoUtils.formatters.todoDisplayName(task)}</span>
                                <span class="task-priority ${task.priority}-priority">${TodoUtils.formatters.priorityLabel(task.priority || 'medium')}</span>
                            </div>
                            ${task.due_date ? `
                                <div class="task-due-date">Due: ${new Date(task.due_date).toLocaleDateString()}</div>
                            ` : ''}
                        </div>
                        <div class="task-actions">
                            <button class="btn btn-small btn-primary add-task-btn">+ Add to Day</button>
                        </div>
                    </div>
                `).join('');
                
                // Add event listeners for suggested task actions
                this.setupSuggestedTaskActions();
            }

            updatePlanningInsights(currentTasks, suggestedTasks, plannedHours, date) {
                const container = document.getElementById('planning-insights-content');
                const hoursNeeded = Math.max(0, 6 - plannedHours);
                const dayOfWeek = date.getDay();
                const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
                
                let insights = [];
                
                // Hours analysis
                if (plannedHours >= 6) {
                    insights.push({
                        icon: '✅',
                        type: 'success',
                        title: 'Daily goal achieved!',
                        text: `You have ${plannedHours}h planned, meeting your 6-hour target.`
                    });
                } else if (hoursNeeded <= 2) {
                    insights.push({
                        icon: '🎯',
                        type: 'info',
                        title: 'Almost there!',
                        text: `Just ${hoursNeeded}h more to reach your 6-hour goal.`
                    });
                } else {
                    insights.push({
                        icon: '📈',
                        type: 'warning',
                        title: 'More planning needed',
                        text: `You need ${hoursNeeded}h more to reach your 6-hour target.`
                    });
                }
                
                // Day-specific insights
                if (isWeekend) {
                    insights.push({
                        icon: '🏠',
                        type: 'info',
                        title: 'Weekend focus',
                        text: 'Great time for personal projects, learning, and maintenance tasks.'
                    });
                } else {
                    insights.push({
                        icon: '💼',
                        type: 'info',
                        title: 'Weekday productivity',
                        text: 'Focus on work tasks and important projects today.'
                    });
                }
                
                // Suggestions insight
                if (suggestedTasks.length > 0 && hoursNeeded > 0) {
                    const topTask = suggestedTasks[0];
                    insights.push({
                        icon: '💡',
                        type: 'tip',
                        title: 'Top suggestion',
                        text: `"${topTask.title}" (${topTask.estimated_hours || 0}h) is highly recommended for today.`
                    });
                }
                
                container.innerHTML = insights.map(insight => `
                    <div class="planning-insight ${insight.type}">
                        <span class="insight-icon">${insight.icon}</span>
                        <div class="insight-content">
                            <strong>${insight.title}</strong>
                            <p>${insight.text}</p>
                        </div>
                    </div>
                `).join('');
            }

            updateWeekContext(date) {
                const container = document.getElementById('week-context-content');
                const weekTasks = this.getWeekTasks();
                const weekPlanned = weekTasks.reduce((sum, task) => sum + (task.estimated_hours || 0), 0);
                const weekCompleted = weekTasks
                    .filter(task => task.status === 'completed')
                    .reduce((sum, task) => sum + (task.estimated_hours || 0), 0);
                
                const weekProgress = weekPlanned > 0 ? Math.round((weekCompleted / weekPlanned) * 100) : 0;
                const dayOfWeek = date.getDay();
                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                const remainingDays = 7 - dayOfWeek;
                
                container.innerHTML = `
                    <div class="week-stat">
                        <span class="stat-label">Week Progress</span>
                        <span class="stat-value">${weekProgress}%</span>
                    </div>
                    <div class="week-stat">
                        <span class="stat-label">Total Planned</span>
                        <span class="stat-value">${weekPlanned}h</span>
                    </div>
                    <div class="week-stat">
                        <span class="stat-label">Completed</span>
                        <span class="stat-value">${weekCompleted}h</span>
                    </div>
                    <div class="week-context-note">
                        <small>${remainingDays} days left in this week</small>
                    </div>
                `;
            }

            setupCurrentTaskActions() {
                // Remove task buttons
                document.querySelectorAll('.remove-task-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const taskItem = e.target.closest('.current-task-item');
                        const taskId = taskItem.dataset.taskId;
                        const project = taskItem.dataset.project;
                        this.removeTaskFromDay(project, taskId);
                    });
                });
                
                // Edit task buttons (placeholder for now)
                document.querySelectorAll('.edit-task-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const taskItem = e.target.closest('.current-task-item');
                        const taskId = taskItem.dataset.taskId;
                        const project = taskItem.dataset.project;
                        TodoUtils.toast.info('Task editing coming soon!');
                    });
                });
            }

            setupSuggestedTaskActions() {
                // Add task buttons
                document.querySelectorAll('.add-task-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const taskItem = e.target.closest('.suggested-task-item');
                        const taskId = taskItem.dataset.taskId;
                        const project = taskItem.dataset.project;
                        this.addSuggestedTaskToDay(project, taskId);
                    });
                });
                
                // Drag and drop for suggested tasks
                document.querySelectorAll('.suggested-task-item[draggable="true"]').forEach(item => {
                    item.addEventListener('dragstart', (e) => {
                        this.draggedTask = {
                            id: item.dataset.taskId,
                            project: item.dataset.project,
                            element: item,
                            isSuggested: true
                        };
                        item.classList.add('dragging');
                    });
                    
                    item.addEventListener('dragend', (e) => {
                        item.classList.remove('dragging');
                        this.draggedTask = null;
                    });
                });
            }

            async addSuggestedTaskToDay(project, taskId) {
                try {
                    const dateStr = this.currentPlanningDate.toISOString().split('T')[0];
                    
                    await this.api.patchTodo(project, taskId, {
                        start_date: dateStr
                    });
                    
                    // Refresh the planning dialog
                    this.loadDailyPlanningData(this.currentPlanningDate);
                    
                    // Refresh the main calendar
                    await this.loadWeeklyData();
                    this.renderWeeklyCalendar();
                    
                    TodoUtils.toast.success('✨ Task added to your day!');
                } catch (error) {
                    console.error('Error adding suggested task:', error);
                    TodoUtils.toast.error('Failed to add task to day');
                }
            }

            async removeTaskFromDay(project, taskId) {
                try {
                    await this.api.patchTodo(project, taskId, {
                        start_date: null
                    });
                    
                    // Refresh the planning dialog
                    this.loadDailyPlanningData(this.currentPlanningDate);
                    
                    // Refresh the main calendar
                    await this.loadWeeklyData();
                    this.renderWeeklyCalendar();
                    
                    TodoUtils.toast.success('Task removed from day');
                } catch (error) {
                    console.error('Error removing task:', error);
                    TodoUtils.toast.error('Failed to remove task');
                }
            }

            setupQuickAdd() {
                const form = document.getElementById('quick-add-form');
                const titleInput = document.getElementById('quick-add-title');
                const hoursInput = document.getElementById('quick-add-hours');
                const prioritySelect = document.getElementById('quick-add-priority');
                const projectSelect = document.getElementById('quick-add-project');
                
                // Populate project dropdown
                this.populateProjectDropdown();
                
                form.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    
                    const title = titleInput.value.trim();
                    const hours = parseFloat(hoursInput.value) || 1;
                    const priority = prioritySelect.value;
                    const selectedProject = projectSelect.value;
                    
                    if (!title) {
                        TodoUtils.toast.error('Please enter a task title');
                        titleInput.focus();
                        return;
                    }
                    
                    if (!selectedProject) {
                        TodoUtils.toast.error('Please select a project');
                        return;
                    }
                    
                    try {
                        const dateStr = this.currentPlanningDate.toISOString().split('T')[0];
                        
                        await this.api.createTodo(selectedProject, {
                            title: title,
                            priority: priority,
                            estimated_hours: hours,
                            start_date: dateStr,
                            status: 'pending'
                        });
                        
                        // Clear form
                        form.reset();
                        hoursInput.value = '1';
                        prioritySelect.value = 'medium';
                        
                        // Refresh the planning dialog
                        this.loadDailyPlanningData(this.currentPlanningDate);
                        
                        // Refresh the main calendar
                        await this.loadWeeklyData();
                        this.renderWeeklyCalendar();
                        
                        TodoUtils.toast.success('📝 New task created and scheduled!');
                        titleInput.focus();
                        
                    } catch (error) {
                        console.error('Error creating quick task:', error);
                        TodoUtils.toast.error('Failed to create task');
                    }
                });
                
                // Auto-fill project based on current context
                this.autoFillProject();
            }

            populateProjectDropdown() {
                const select = document.getElementById('quick-add-project');
                
                // Get unique project names from todos
                const projects = [...new Set(this.todos.map(todo => ({
                    name: todo.projectName,
                    displayName: TodoUtils.formatters.todoDisplayName(todo)
                })))];
                
                select.innerHTML = '<option value="">Select project...</option>' +
                    projects.map(project => 
                        `<option value="${project.name}">${project.displayName}</option>`
                    ).join('');
            }

            autoFillProject() {
                const select = document.getElementById('quick-add-project');
                const currentTasks = this.getTasksForDay(this.currentPlanningDate);
                
                if (currentTasks.length > 0) {
                    // Use the most common project from today's tasks
                    const projectCounts = {};
                    currentTasks.forEach(task => {
                        projectCounts[task.projectName] = (projectCounts[task.projectName] || 0) + 1;
                    });
                    
                    const mostCommonProject = Object.entries(projectCounts)
                        .sort((a, b) => b[1] - a[1])[0][0];
                    
                    select.value = mostCommonProject;
                }
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.weeklyPlanner = new WeeklyPlanner();
        });
    </script>
</body>
</html>