# Start Todo Web Server
Write-Host "Starting Web Server on port 8080..." -ForegroundColor Green
Write-Host "Directory: $(Get-Location)" -ForegroundColor Gray
Write-Host ""

# Wait a moment for API server to be ready
Write-Host "Waiting 3 seconds for API server to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

try {
    python -m http.server 8080
} catch {
    Write-Host "Error starting web server: $_" -ForegroundColor Red
} finally {
    Write-Host ""
    Write-Host "Web Server started on http://localhost:8080" -ForegroundColor Green
    Write-Host "Open http://localhost:8080 to access the Todo Management System" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Press any key to close this tab..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}