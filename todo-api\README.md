# Todo Management API

A comprehensive REST API for managing todo items across projects with directory whitelisting security and full CRUD operations.

## Features

- 🔒 **Security**: Directory whitelisting to protect against unauthorized file access
- 🏗️ **Project-based**: Organize todos by project with automatic path resolution
- 📝 **Full CRUD**: Create, Read, Update, Delete operations on todo items
- 🏷️ **Rich Metadata**: Support for tags, subtasks, priorities, categories, and dependencies
- 🔍 **Filtering**: Filter todos by status, priority, category, or tags
- 📊 **OpenAPI**: Complete OpenAPI 3.0 specification with interactive documentation
- ⚡ **Fast**: Built with FastAPI for high performance and automatic validation

## Quick Start

### 1. Installation

```bash
# Navigate to the todo-api directory
cd ~/source/repos/scripts/todo-api

# Install dependencies
pip install -r requirements.txt
```

### 2. Start the Server

```bash
# Option 1: Use the startup script (recommended)
python start_server.py

# Option 2: Start directly
python app.py --reload

# Option 3: Use uvicorn directly
uvicorn app:app --reload --host localhost --port 8000
```

### 3. Access the API

- **API Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/api/v1/health

## Security Setup

The API requires directory whitelisting for security. Only directories under `~/source/repos/` can be whitelisted.

### Whitelist a Directory

```bash
curl -X POST "http://localhost:8000/api/v1/security/whitelist" \
  -H "Content-Type: application/json" \
  -d '{"directory": "/home/<USER>/source/repos"}'
```

### View Whitelisted Directories

```bash
curl "http://localhost:8000/api/v1/security/whitelist"
```

## API Usage Examples

### List All Projects

```bash
curl "http://localhost:8000/api/v1/projects"
```

### Get Project Details

```bash
curl "http://localhost:8000/api/v1/projects/AccountingCrmInterfaces"
```

### List Todos for a Project

```bash
# Get all todos
curl "http://localhost:8000/api/v1/projects/AccountingCrmInterfaces/todos"

# Filter by status
curl "http://localhost:8000/api/v1/projects/AccountingCrmInterfaces/todos?status=pending"

# Filter by priority and category
curl "http://localhost:8000/api/v1/projects/AccountingCrmInterfaces/todos?priority=critical&category=testing"
```

### Create a New Todo

```bash
curl -X POST "http://localhost:8000/api/v1/projects/AccountingCrmInterfaces/todos" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Fix authentication bug",
    "description": "Resolve login issues reported by QA team",
    "priority": "high",
    "category": "development",
    "estimated_hours": 2.5,
    "tags": ["bug", "authentication", "urgent"],
    "subtasks": [
      {"text": "Investigate login flow", "completed": false},
      {"text": "Fix authentication logic", "completed": false},
      {"text": "Add unit tests", "completed": false}
    ]
  }'
```

### Update a Todo

```bash
curl -X PUT "http://localhost:8000/api/v1/projects/AccountingCrmInterfaces/todos/development-abc123" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Fix authentication bug",
    "description": "Resolve login issues - COMPLETED",
    "status": "completed",
    "priority": "high",
    "category": "development",
    "estimated_hours": 2.5,
    "tags": ["bug", "authentication", "completed"],
    "subtasks": [
      {"text": "Investigate login flow", "completed": true},
      {"text": "Fix authentication logic", "completed": true},
      {"text": "Add unit tests", "completed": true}
    ]
  }'
```

### Partially Update a Todo (PATCH)

```bash
curl -X PATCH "http://localhost:8000/api/v1/projects/AccountingCrmInterfaces/todos/development-abc123" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "in_progress",
    "assigned_to": "john.doe"
  }'
```

### Delete a Todo

```bash
curl -X DELETE "http://localhost:8000/api/v1/projects/AccountingCrmInterfaces/todos/development-abc123"
```

## Data Model

### Todo Item Structure

```yaml
id: "testing-001"                    # Unique identifier
title: "Complete UAT environment setup"
description: "Detailed description..."
status: "pending"                    # pending|completed|in_progress|cancelled
priority: "critical"                 # critical|high|medium|low
category: "testing"                  # Custom category
estimated_hours: 1.5
assigned_to: "john.doe"              # Optional assignee
due_date: "2025-12-31"              # Optional due date
created_date: "2025-08-02"          # Auto-generated
tags: ["environment", "sync"]        # Array of tags
dependencies: ["setup-001"]          # Array of todo IDs
subtasks:                           # Array of subtasks
  - text: "Prepare dataset"
    completed: false
section: "UAT Preparation"          # Optional section grouping
_hash: "8ef87cac"                   # Change detection hash
```

### Project Structure

The API expects projects to follow this structure:

```
~/source/repos/
└── ProjectName/
    └── project-progress/
        └── to-do-lists/
            └── todos.yml           # Consolidated todo list
```

## Security

### Directory Whitelisting

- Only directories under `~/source/repos/` can be whitelisted
- Write operations require the target directory to be whitelisted
- Read operations work on any valid project structure
- Whitelist configuration is stored in `~/.todo-api-whitelist.json`

### Path Validation

- All file paths are resolved and validated
- Relative path traversal attacks are prevented
- Only YAML files in the expected structure are accessed

## Development

### Running in Development Mode

```bash
python app.py --reload --host 0.0.0.0 --port 8000
```

### Testing

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests (when available)
pytest
```

### API Documentation

The API includes comprehensive OpenAPI 3.0 documentation:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI Schema**: http://localhost:8000/openapi.json

## Error Handling

The API returns structured error responses:

```json
{
  "error": "Todo not found",
  "code": "TODO_NOT_FOUND",
  "details": {
    "todo_id": "invalid-id",
    "project": "ProjectName"
  }
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `204`: No Content (delete success)
- `400`: Bad Request (validation error)
- `403`: Forbidden (directory not whitelisted)
- `404`: Not Found
- `500`: Internal Server Error

## Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Ensure security considerations are addressed

## License

MIT License - see LICENSE file for details.